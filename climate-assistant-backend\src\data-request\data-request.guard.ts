import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { DataRequestService } from './data-request.service';

@Injectable()
export class DataRequestGuard implements CanActivate {
  constructor(private readonly dataRequestService: DataRequestService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const dataRequestId = request.params.dataRequestId;
    const workspaceId = request.user.workspaceId;

    const dataRequest =
      await this.dataRequestService.findProject(dataRequestId);
    const project = dataRequest.project;

    if (project.workspaceId !== workspaceId) {
      throw new UnauthorizedException(`Project is not from this workspace`);
    }
    return true;
  }
}
