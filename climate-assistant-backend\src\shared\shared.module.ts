import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DatapointDataRequestSharedService } from './shared-datapoint-datarequest.service';
import { DataRequest } from 'src/data-request/entities/data-request.entity';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { BullModule } from '@nestjs/bull';
import { JobProcessor } from 'src/types/jobs';

@Module({
  imports: [
    TypeOrmModule.forFeature([DataRequest, DatapointRequest]),
    BullModule.registerQueue({ name: JobProcessor.DatapointGeneration }),
    BullModule.registerQueue({ name: JobProcessor.DatapointReview }),
  ],
  providers: [DatapointDataRequestSharedService],
  exports: [DatapointDataRequestSharedService],
})
export class SharedModule {} 