import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1738567330125 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    queryRunner.query(`
            UPDATE esrs_disclosure_requirement SET sort = 1 WHERE id = 1;
            UPDATE esrs_disclosure_requirement SET sort = 2 WHERE id = 2;
            UPDATE esrs_disclosure_requirement SET sort = 3 WHERE id = 3;
            UPDATE esrs_disclosure_requirement SET sort = 4 WHERE id = 4;
            UPDATE esrs_disclosure_requirement SET sort = 5 WHERE id = 5;
            UPDATE esrs_disclosure_requirement SET sort = 6 WHERE id = 6;
            UPDATE esrs_disclosure_requirement SET sort = 7 WHERE id = 7;
            UPDATE esrs_disclosure_requirement SET sort = 8 WHERE id = 8;
            UPDATE esrs_disclosure_requirement SET sort = 9 WHERE id = 9;
            UPDATE esrs_disclosure_requirement SET sort = 10 WHERE id = 10;
            UPDATE esrs_disclosure_requirement SET sort = 11 WHERE id = 11;
            UPDATE esrs_disclosure_requirement SET sort = 12 WHERE id = 12;
            UPDATE esrs_disclosure_requirement SET sort = 1 WHERE id = 13;
            UPDATE esrs_disclosure_requirement SET sort = 2 WHERE id = 14;
            UPDATE esrs_disclosure_requirement SET sort = 3 WHERE id = 15;
            UPDATE esrs_disclosure_requirement SET sort = 4 WHERE id = 16;
            UPDATE esrs_disclosure_requirement SET sort = 5 WHERE id = 17;
            UPDATE esrs_disclosure_requirement SET sort = 6 WHERE id = 18;
            UPDATE esrs_disclosure_requirement SET sort = 7 WHERE id = 19;
            UPDATE esrs_disclosure_requirement SET sort = 1 WHERE id = 20;
            UPDATE esrs_disclosure_requirement SET sort = 2 WHERE id = 21;
            UPDATE esrs_disclosure_requirement SET sort = 3 WHERE id = 22;
            UPDATE esrs_disclosure_requirement SET sort = 4 WHERE id = 23;
            UPDATE esrs_disclosure_requirement SET sort = 5 WHERE id = 24;
            UPDATE esrs_disclosure_requirement SET sort = 6 WHERE id = 25;
            UPDATE esrs_disclosure_requirement SET sort = 1 WHERE id = 26;
            UPDATE esrs_disclosure_requirement SET sort = 2 WHERE id = 27;
            UPDATE esrs_disclosure_requirement SET sort = 3 WHERE id = 28;
            UPDATE esrs_disclosure_requirement SET sort = 4 WHERE id = 29;
            UPDATE esrs_disclosure_requirement SET sort = 5 WHERE id = 30;
            UPDATE esrs_disclosure_requirement SET sort = 6 WHERE id = 31;
            UPDATE esrs_disclosure_requirement SET sort = 7 WHERE id = 32;
            UPDATE esrs_disclosure_requirement SET sort = 8 WHERE id = 33;
            UPDATE esrs_disclosure_requirement SET sort = 1 WHERE id = 34;
            UPDATE esrs_disclosure_requirement SET sort = 2 WHERE id = 35;
            UPDATE esrs_disclosure_requirement SET sort = 3 WHERE id = 36;
            UPDATE esrs_disclosure_requirement SET sort = 4 WHERE id = 37;
            UPDATE esrs_disclosure_requirement SET sort = 5 WHERE id = 38;
            UPDATE esrs_disclosure_requirement SET sort = 6 WHERE id = 39;
            UPDATE esrs_disclosure_requirement SET sort = 7 WHERE id = 40;
            UPDATE esrs_disclosure_requirement SET sort = 1 WHERE id = 84;
            UPDATE esrs_disclosure_requirement SET sort = 2 WHERE id = 85;
            UPDATE esrs_disclosure_requirement SET sort = 3 WHERE id = 86;
            UPDATE esrs_disclosure_requirement SET sort = 4 WHERE id = 87;
            UPDATE esrs_disclosure_requirement SET sort = 5 WHERE id = 88;
            UPDATE esrs_disclosure_requirement SET sort = 6 WHERE id = 89;
            UPDATE esrs_disclosure_requirement SET sort = 7 WHERE id = 90;
            UPDATE esrs_disclosure_requirement SET sort = 8 WHERE id = 91;
            UPDATE esrs_disclosure_requirement SET sort = 9 WHERE id = 92;
            UPDATE esrs_disclosure_requirement SET sort = 10 WHERE id = 93;
            UPDATE esrs_disclosure_requirement SET sort = 11 WHERE id = 94;
            UPDATE esrs_disclosure_requirement SET sort = 12 WHERE id = 95;
            UPDATE esrs_disclosure_requirement SET sort = 1 WHERE id = 77;
            UPDATE esrs_disclosure_requirement SET sort = 2 WHERE id = 78;
            UPDATE esrs_disclosure_requirement SET sort = 3 WHERE id = 79;
            UPDATE esrs_disclosure_requirement SET sort = 4 WHERE id = 80;
            UPDATE esrs_disclosure_requirement SET sort = 5 WHERE id = 81;
            UPDATE esrs_disclosure_requirement SET sort = 6 WHERE id = 82;
            UPDATE esrs_disclosure_requirement SET sort = 7 WHERE id = 83;
            UPDATE esrs_disclosure_requirement SET sort = 8 WHERE id = 97;
            UPDATE esrs_disclosure_requirement SET sort = 1 WHERE id = 41;
            UPDATE esrs_disclosure_requirement SET sort = 2 WHERE id = 42;
            UPDATE esrs_disclosure_requirement SET sort = 3 WHERE id = 43;
            UPDATE esrs_disclosure_requirement SET sort = 4 WHERE id = 44;
            UPDATE esrs_disclosure_requirement SET sort = 5 WHERE id = 45;
            UPDATE esrs_disclosure_requirement SET sort = 6 WHERE id = 46;
            UPDATE esrs_disclosure_requirement SET sort = 7 WHERE id = 47;
            UPDATE esrs_disclosure_requirement SET sort = 8 WHERE id = 48;
            UPDATE esrs_disclosure_requirement SET sort = 9 WHERE id = 49;
            UPDATE esrs_disclosure_requirement SET sort = 10 WHERE id = 50;
            UPDATE esrs_disclosure_requirement SET sort = 11 WHERE id = 51;
            UPDATE esrs_disclosure_requirement SET sort = 12 WHERE id = 52;
            UPDATE esrs_disclosure_requirement SET sort = 13 WHERE id = 53;
            UPDATE esrs_disclosure_requirement SET sort = 14 WHERE id = 54;
            UPDATE esrs_disclosure_requirement SET sort = 15 WHERE id = 55;
            UPDATE esrs_disclosure_requirement SET sort = 16 WHERE id = 56;
            UPDATE esrs_disclosure_requirement SET sort = 17 WHERE id = 57;
            UPDATE esrs_disclosure_requirement SET sort = 18 WHERE id = 58;
            UPDATE esrs_disclosure_requirement SET sort = 1 WHERE id = 96;
            UPDATE esrs_disclosure_requirement SET sort = 2 WHERE id = 59;
            UPDATE esrs_disclosure_requirement SET sort = 3 WHERE id = 60;
            UPDATE esrs_disclosure_requirement SET sort = 4 WHERE id = 61;
            UPDATE esrs_disclosure_requirement SET sort = 5 WHERE id = 62;
            UPDATE esrs_disclosure_requirement SET sort = 6 WHERE id = 63;
            UPDATE esrs_disclosure_requirement SET sort = 7 WHERE id = 64;
            UPDATE esrs_disclosure_requirement SET sort = 1 WHERE id = 65;
            UPDATE esrs_disclosure_requirement SET sort = 2 WHERE id = 66;
            UPDATE esrs_disclosure_requirement SET sort = 3 WHERE id = 67;
            UPDATE esrs_disclosure_requirement SET sort = 4 WHERE id = 68;
            UPDATE esrs_disclosure_requirement SET sort = 5 WHERE id = 69;
            UPDATE esrs_disclosure_requirement SET sort = 6 WHERE id = 70;
            UPDATE esrs_disclosure_requirement SET sort = 1 WHERE id = 71;
            UPDATE esrs_disclosure_requirement SET sort = 2 WHERE id = 72;
            UPDATE esrs_disclosure_requirement SET sort = 3 WHERE id = 73;
            UPDATE esrs_disclosure_requirement SET sort = 4 WHERE id = 74;
            UPDATE esrs_disclosure_requirement SET sort = 5 WHERE id = 75;
            UPDATE esrs_disclosure_requirement SET sort = 6 WHERE id = 76;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
