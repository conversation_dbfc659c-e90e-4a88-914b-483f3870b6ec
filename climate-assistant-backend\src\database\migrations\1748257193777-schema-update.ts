import { MigrationInterface, QueryRunner } from 'typeorm';

//This migration updates the data_request_status_enum to include new statuses and renames existing ones.
// This is required as going forward we will just have 3 statuses
export class SchemaUpdate1748257193777 implements MigrationInterface {
  name = 'SchemaUpdate1748257193777';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."datapoint_request_status_enum_new" AS ENUM('incomplete_data', 'complete_data', 'not_reported')`
    );

    // Update column to use new type, converting values in the process
    await queryRunner.query(`
      ALTER TABLE "datapoint_request" 
      ALTER COLUMN status TYPE "public"."datapoint_request_status_enum_new" 
      USING (
        CASE status::text
          WHEN 'not_answered' THEN 'not_reported'
          WHEN 'no_data' THEN 'incomplete_data'
          WHEN 'draft' THEN 'incomplete_data'
          WHEN 'approved_answer' THEN 'complete_data'
          WHEN 'incomplete_data' THEN 'incomplete_data'
          WHEN 'complete_data' THEN 'complete_data'
          ELSE 'incomplete_data'
        END
      )::"public"."datapoint_request_status_enum_new"
    `);

    // Drop old enum type
    await queryRunner.query(
      `DROP TYPE "public"."datapoint_request_status_enum"`
    );

    // Rename new enum type to final name
    await queryRunner.query(
      `ALTER TYPE "public"."datapoint_request_status_enum_new" RENAME TO "datapoint_request_status_enum"`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."datapoint_request_status_enum_old" AS ENUM('not_answered', 'incomplete_data', 'no_data', 'complete_data')`
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" ALTER COLUMN "status" TYPE "public"."datapoint_request_status_enum_old" USING "status"::"text"::"public"."datapoint_request_status_enum_old"`
    );
    await queryRunner.query(
      `DROP TYPE "public"."datapoint_request_status_enum"`
    );
    await queryRunner.query(
      `ALTER TYPE "public"."datapoint_request_status_enum_old" RENAME TO "datapoint_request_status_enum"`
    );
  }
}
