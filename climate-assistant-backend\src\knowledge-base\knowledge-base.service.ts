import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import * as fs from 'fs';
import * as pdf from 'pdf-parse';
import { RecursiveCharacterTextSplitter } from '@langchain/textsplitters';
import { KnowledgeBaseFileUploadChunk } from './entities/knowledge-base-file-upload-chunk.entity';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository, In, Not } from 'typeorm';
import { KnowledgeBaseFileUpload } from './entities/knowledge-base-file-upload.entity';
import { ChatGptService } from '../llm/chat-gpt.service';
import { ESRSDatapoint } from 'src/datapoint/entities/esrs-datapoint.entity';
import { ESRSDisclosureRequirement } from './entities/esrs-disclosure-requirement.entity';
import { ESRSTopic } from './entities/esrs-topic.entity';

@Injectable()
export class KnowledgeBaseService {
  constructor(
    @InjectRepository(KnowledgeBaseFileUpload)
    private readonly knowledgeBaseFileUploadRepository: Repository<KnowledgeBaseFileUpload>,
    @InjectRepository(KnowledgeBaseFileUploadChunk)
    private readonly knowledgeBaseFileUploadChunkRepository: Repository<KnowledgeBaseFileUploadChunk>,
    @InjectRepository(ESRSDatapoint)
    private readonly esrsDatapointRepository: Repository<ESRSDatapoint>,
    @InjectRepository(ESRSDisclosureRequirement)
    private readonly esrsDisclosureRequirementRepository: Repository<ESRSDisclosureRequirement>,
    @InjectRepository(ESRSTopic)
    private readonly esrsTopicRepository: Repository<ESRSTopic>,
    @InjectDataSource() private dataSource: DataSource,
    private readonly chatGptService: ChatGptService
  ) {}

  async saveFileWithEmbeddings(
    originalname: string,
    path: string
  ): Promise<void> {
    const fileExists = await this.knowledgeBaseFileUploadRepository.exists({
      where: { name: originalname },
    });

    if (fileExists) {
      fs.unlinkSync(path);
      throw new BadRequestException('File already uploaded');
    }

    const fileUpload = await this.knowledgeBaseFileUploadRepository.save({
      path,
      name: originalname,
    });
    const dataBuffer = fs.readFileSync(path);
    const pdfData = await pdf(dataBuffer);
    const splitter = new RecursiveCharacterTextSplitter({
      chunkSize: 1000,
      chunkOverlap: 200,
    });
    const chunks = await splitter.splitText(pdfData.text);

    const contentEmbeddings =
      await this.chatGptService.createContentEmbeddingPairs(chunks);
    await this.knowledgeBaseFileUploadChunkRepository.save(
      contentEmbeddings.map(({ content, embedding }) => ({
        content,
        embedding,
        fileUploadId: fileUpload.id,
      })) as unknown as KnowledgeBaseFileUploadChunk[]
    );
  }

  async getUploadedFiles() {
    return this.knowledgeBaseFileUploadRepository.find();
  }

  async deleteFile(id: KnowledgeBaseFileUpload['id']): Promise<void> {
    const hasPermission = await this.knowledgeBaseFileUploadRepository.exists({
      where: { id },
    });
    if (!hasPermission) {
      throw new UnauthorizedException(
        'You are not allowed to delete this file'
      );
    }
    const file = await this.knowledgeBaseFileUploadRepository.findOneOrFail({
      where: { id },
    });
    await this.knowledgeBaseFileUploadChunkRepository.delete({
      fileUploadId: id,
    });
    await this.knowledgeBaseFileUploadRepository.delete({ id: id });
    fs.unlinkSync(file.path);
  }

  async getSimilarChunksWithVectorSearch(
    question: string,
    elements: number,
    similarityThreshold: number = 0.8
  ) {
    const embeddingQuestion =
      await this.chatGptService.createEmbedding(question);

    const res = await this.dataSource.query(
      `
          SELECT knowledge_base_file_upload_chunk.content,
                 1 - (knowledge_base_file_upload_chunk.embedding <=> $1) AS similarity,
                 knowledge_base_file_upload_chunk."fileUploadId",
                 knowledge_base_file_upload_chunk.id
          FROM knowledge_base_file_upload_chunk
                   JOIN
               knowledge_base_file_upload
               ON knowledge_base_file_upload_chunk."fileUploadId" = knowledge_base_file_upload.id
          WHERE 1 - (knowledge_base_file_upload_chunk.embedding <=> $1) > $2
          ORDER BY knowledge_base_file_upload_chunk.embedding <=> $1
              LIMIT $3;
      `,
      ['[' + embeddingQuestion + ']', similarityThreshold, elements]
    );

    return res as { content: string; similarity: number }[];
  }

  async getSimilarChunksForMultipleQuestions(
    questions: string[],
    chunksPerQuestion: number
  ) {
    const chunks = (
      await Promise.all(
        questions.map(async (question) => {
          return this.getSimilarChunksWithVectorSearch(
            question,
            chunksPerQuestion
          );
        })
      )
    ).reduce((acc, val) => acc.concat(val), []);

    return chunks.map((chunk) => chunk.content);
  }

  async getEsrsDatapointsByStandard(esrs: string): Promise<ESRSDatapoint[]> {
    const drs =
      esrs === 'all'
        ? await this.esrsDisclosureRequirementRepository.find()
        : await this.esrsDisclosureRequirementRepository.find({
            where: { esrs: esrs },
          });

    const datapoints = await this.esrsDatapointRepository.find({
      where: {
        esrsDisclosureRequirementId: In(drs.map((dr) => dr.id)),
      },
      order: {
        esrsDisclosureRequirement: {
          esrs: 'ASC',
          sort: 'ASC',
        },
        datapointId: 'ASC',
      },
    });

    return datapoints;
  }

  async getEsrsTopics(): Promise<ESRSTopic[]> {
    return this.esrsTopicRepository.find({
      where: { level: 'topic', id: Not(11) },
      relations: ['children.children'],
    });
  }
}
