# Prompt Management Integration Guide

## Overview

This guide explains how to integrate the prompt management system with any existing feature that uses LLM prompts.

## Backend Integration

### 1. Database Setup

Run the migrations to create the prompt management tables:

```bash
npm run migration:run
```

This creates:

- `llm_prompts` table - stores prompt templates for all features
- `llm_prompt_history` table - tracks changes to prompts over time

### 2. Feature Registration

When adding prompts for a new feature, create a migration that seeds the initial prompts:

```typescript
// Example migration structure
export class SeedFeaturePrompts implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const prompts = [
      {
        feature: 'YOUR_FEATURE_NAME',
        chainIdentifier: 'A1',
        prompt: 'Your main prompt template with {{variables}}',
        isActive: true,
        model: 'o4-mini',
      },
      // Add more prompts as needed
    ];

    await queryRunner.manager.save(LlmPrompt, prompts);
  }
}
```

### 3. Chain Identifier System

The chain identifier system organizes prompts into logical sequences. Common patterns:

#### Primary Chain (A Series)

- **A1**: Main prompt with core logic and variables
- **A2-A6**: Conditional additions based on context
  - Additional context prompts
  - Conditional instructions
  - Supplementary data prompts

#### Secondary Chain (B Series)

- **B1-Bn**: Post-processing or refinement prompts
  - Formatting improvements
  - Additional analysis
  - Output transformations

### 4. Service Integration Pattern

```typescript
// 1. Determine feature type based on your business logic
const feature = this.determineFeatureType(context);

// 2. Build unified variables for all prompts
const variables = {
  // Core variables used across all prompts
  primaryData: context.primaryData,
  configuration: context.configuration,
  // Conditional flags for A2-A6
  hasAdditionalContext: context.additionalData?.length > 0,
  needsSpecialHandling: context.isSpecialCase,
};

// 3. Fetch and compile prompts
const prompts = await this.buildPrompts({
  feature,
  variables,
  testPrompts, // For dry run mode
  dryRun,
});

// 4. Execute LLM calls
const response = await this.llmService.handleRequest({
  model: prompts.model,
  messages: prompts.messages,
  temperature: 0,
});

// 5. Optional: Apply post-processing with B series
if (needsPostProcessing) {
  const refinedResponse = await this.applyPostProcessing(response);
}
```

### 5. Building Prompts Helper Method

```typescript
private async buildPrompts({
  feature,
  variables,
  testPrompts,
  dryRun,
}: BuildPromptsParams): Promise<PromptResult> {
  const prompts: ChatCompletionMessageParam[] = [];
  const promptIds: string[] = [];

  // Get A1 (main prompt)
  let mainPrompt: string;
  let promptId: string | null = null;

  if (dryRun && testPrompts?.['A1']) {
    mainPrompt = await this.compilePrompt(testPrompts['A1'].prompt, variables);
  } else {
    const dbPrompt = await this.promptManagementService.findByFeatureAndChain(feature, 'A1');
    promptId = dbPrompt.id;
    mainPrompt = await this.compilePrompt(dbPrompt.prompt, variables);
  }

  prompts.push({ role: 'system', content: mainPrompt });
  if (promptId) promptIds.push(promptId);

  // Conditionally add A2-A6 based on variables
  // Example: Add A2 if additional context exists
  if (variables.hasAdditionalContext) {
    // Similar pattern for A2
  }

  return { prompts, promptIds };
}
```

### 6. Dry Run Support

Enable testing prompts without affecting production:

```typescript
async processWithPrompts({
  context,
  dryRun = false,
  testPrompts,
}: ProcessParams): Promise<ProcessResult | DryRunResult> {
  const timingTracker = dryRun ? new TimingTracker() : null;

  try {
    // Build and execute prompts
    const result = await this.executePrompts(context, testPrompts, dryRun);

    if (dryRun) {
      return {
        output: result.content,
        metadata: {
          promptIds: result.usedPromptIds,
          tokens: result.tokenUsage,
        },
        prompts: result.prompts,
        timings: timingTracker?.getTimings(),
      };
    }

    // Save to database in production mode
    return this.saveResult(result);
  } catch (error) {
    if (dryRun) {
      return {
        output: `Error: ${error.message}`,
        metadata: { error: error.message },
        prompts: [],
      };
    }
    throw error;
  }
}
```

## Frontend Integration

### Admin UI Features

The admin UI at `/admin/prompts` provides:

1. **Feature Selection**: Dropdown with all registered features
2. **Chain Management**: View/edit all prompts in a feature's chain
3. **Variable Validation**: Real-time validation of {{variables}}
4. **Test Mode**:
   - Select test data from current workspace
   - Preview results without saving
   - Compare outputs between prompt versions

### Testing Workflow

1. Navigate to Settings > Super Admin > Prompt Management
2. Select your feature from the dropdown
3. Edit prompts using the rich text editor
4. Required variables are highlighted at the top
5. Select test data relevant to your feature
6. Click "Test Prompt" to preview results
7. Save changes when satisfied

## Variable System

### Variable Format

Variables use the `{{variableName}}` format and support:

- Simple values: `{{userName}}`
- Nested objects: `{{user.profile.name}}`
- Conditionals: `{{#if hasData}}...{{/if}}`
- Loops: `{{#each items}}...{{/each}}`

### Variable Organization

Structure variables by their usage pattern:

```typescript
interface PromptVariables {
  // Core variables (always present)
  core: {
    id: string;
    timestamp: string;
    configuration: Config;
  };

  // Conditional variables (check flags)
  conditional: {
    additionalContext?: string;
    specialInstructions?: string;
  };

  // Feature-specific variables
  feature: {
    [key: string]: any;
  };
}
```

### Conditional Prompt Inclusion

Use flags in variables to control which prompts are included:

```typescript
// In your service
const variables = {
  hasHistoricalData: historicalData.length > 0,
  needsComplexAnalysis: complexity > threshold,
  includeExamples: userPreference.showExamples,
};

// In prompt A2 (only added if hasHistoricalData is true)
('Analyze considering this historical context: {{historicalData}}');
```

## Security & Best Practices

### Access Control

- Only SUPER_ADMIN users can modify prompts
- All changes are tracked with user information
- Test mode runs in isolation without affecting production

### Prompt Versioning

- Every change creates a history entry
- Can rollback to previous versions if needed
- Compare prompt performance across versions

### Testing Guidelines

1. Always test prompts with diverse input data
2. Verify all conditional branches work correctly
3. Check token usage doesn't exceed limits
4. Validate output format matches expectations

### Migration Best Practices

1. **Incremental Migration**: Migrate one feature at a time
2. **Backward Compatibility**: Keep fallback to hardcoded prompts initially
3. **Comprehensive Testing**: Test all edge cases before removing old code
4. **Documentation**: Document all variables and their purposes

## Common Integration Patterns

### 1. Simple Single-Prompt Feature

```typescript
const prompt = await this.promptService.getPrompt('SIMPLE_FEATURE', 'A1');
const compiled = await this.promptService.compile(prompt, { data });
const response = await this.llm.complete(compiled);
```

### 2. Multi-Step Processing

```typescript
// Step 1: Initial processing
const initialResponse = await this.processWithPrompt(
  'FEATURE',
  'A1',
  variables
);

// Step 2: Refinement (if needed)
if (needsRefinement) {
  const refined = await this.processWithPrompt('FEATURE', 'B1', {
    ...variables,
    previousResponse: initialResponse,
  });
}
```

### 3. Conditional Chain Building

```typescript
const chain = ['A1']; // Always include main prompt

if (context.needsAnalysis) chain.push('A2');
if (context.hasComplexData) chain.push('A3');
if (context.userPreferences) chain.push('A4');

const prompts = await this.buildPromptChain('FEATURE', chain, variables);
```

## Troubleshooting

### Common Issues

1. **Missing Variables**: Ensure all {{variables}} in prompts are provided
2. **Chain Order**: Prompts execute in alphabetical order (A1, A2, ..., B1, B2)
3. **Model Compatibility**: Verify prompts work with specified model
4. **Token Limits**: Monitor total prompt length for model limits
