import { Injectable, OnModuleDestroy } from '@nestjs/common';
import { Redis } from 'ioredis';
import { Subject } from 'rxjs';
import { getRedisHost } from '../env-helper';
import { WorkerLogger } from 'src/shared/logger.service';

@Injectable()
export class RedisEventService implements OnModuleDestroy {
  private publisher: Redis;
  private subscriber: Redis;
  private readonly eventSubjects = new Map<string, Subject<any>>();
  private readonly logger = new WorkerLogger(RedisEventService.name);
  private readonly workerId = process.env.INSTANCE_ID || process.pid;

  constructor() {
    const redisConfig = {
      host: getRedisHost(),
      port: 6379,
    };

    // Need separate connections for pub/sub in Redis
    this.publisher = new Redis(redisConfig);
    this.subscriber = new Redis(redisConfig);

    this.publisher.on('connect', () => {
      this.logger.log(`Worker ${this.workerId}: Redis publisher connected`);
    });

    this.subscriber.on('connect', () => {
      this.logger.log(`Worker ${this.workerId}: Redis subscriber connected`);
    });

    this.publisher.on('error', (err) => {
      this.logger.error(
        `Worker ${this.workerId}: Redis publisher error`,
        err.message
      );
    });

    this.subscriber.on('error', (err) => {
      this.logger.error(
        `Worker ${this.workerId}: Redis subscriber error`,
        err.message
      );
    });

    // Handle incoming messages from Redis
    this.subscriber.on('message', (channel, message) => {
      this.logger.debug(`Worker ${this.workerId} received message on channel: ${channel}`);
      
      const subject = this.eventSubjects.get(channel);
      if (subject) {
        try {
          const data = JSON.parse(message);
          subject.next(data);
        } catch (error) {
          this.logger.error('Failed to parse Redis message:', error);
        }
      }
    });
  }

  /**
   * Subscribe to a Redis channel and return a Subject for that channel
   * Multiple subscriptions to the same channel return the same Subject
   */
  subscribe<T>(channel: string): Subject<T> {
    if (!this.eventSubjects.has(channel)) {
      const subject = new Subject<T>();
      this.eventSubjects.set(channel, subject);
      
      this.subscriber.subscribe(channel, (err, count) => {
        if (err) {
          this.logger.error(
            `Failed to subscribe to channel ${channel}:`,
            err.message
          );
        } else {
          this.logger.log(`Worker ${this.workerId} subscribed to channel: ${channel} (${count} total subscriptions)`);
        }
      });
    }

    return this.eventSubjects.get(channel) as Subject<T>;
  }

  /**
   * Publish an event to a Redis channel
   * This will be received by all workers subscribed to this channel
   */
  async publish(channel: string, data: any): Promise<void> {
    try {
      const message = JSON.stringify(data);
      const receivers = await this.publisher.publish(channel, message);
      this.logger.debug(`Worker ${this.workerId} published to channel ${channel}, received by ${receivers} subscribers`);
    } catch (error) {
      this.logger.error(`Failed to publish to channel ${channel}:`, error);
      throw error;
    }
  }

  /**
   * Unsubscribe from a specific channel
   */
  async unsubscribe(channel: string): Promise<void> {
    const subject = this.eventSubjects.get(channel);
    if (subject) {
      subject.complete();
      this.eventSubjects.delete(channel);
      await this.subscriber.unsubscribe(channel);
      this.logger.log(`Worker ${this.workerId} unsubscribed from channel: ${channel}`);
    }
  }

  /**
   * Get the number of subscribers for a channel (across all workers)
   */
  async getSubscriberCount(channel: string): Promise<number> {
    const result = await this.publisher.pubsub('NUMSUB', channel);
    return result[1] as number;
  }

  /**
   * Clean up connections on module destroy
   */
  async onModuleDestroy() {
    this.logger.log(`Worker ${this.workerId}: Cleaning up Redis connections`);
    
    // Complete all subjects
    this.eventSubjects.forEach((subject) => subject.complete());
    this.eventSubjects.clear();

    // Close Redis connections
    await this.publisher.quit();
    await this.subscriber.quit();
  }
} 