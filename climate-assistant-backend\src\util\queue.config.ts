/**
 * Bull Queue Configuration
 * Centralized configuration for all Bull queues in the application
 */

// Job cleanup configuration
export const QUEUE_JOB_CLEANUP_CONFIG = {
  removeOnComplete: {
        age: 1800, // 30 minutes in seconds
        count: 100, // keep max 100 completed jobs
      },
  removeOnFail: {
    age: 259200, // 3 days in seconds
    count: 200, // keep max 200 failed jobs
  },
};

// Default job options for all queues
export const DEFAULT_QUEUE_JOB_OPTIONS = {
  removeOnComplete: QUEUE_JOB_CLEANUP_CONFIG.removeOnComplete,
  removeOnFail: QUEUE_JOB_CLEANUP_CONFIG.removeOnFail,
};

// Default retry configuration
export const DEFAULT_JOB_RETRY_CONFIG = {
  attempts: 5,
  backoff: {
    type: 'exponential' as const,
    delay: 5000,
  },
};

// Combined default job configuration
export const DEFAULT_JOB_CONFIG = {
  ...DEFAULT_JOB_RETRY_CONFIG,
  ...DEFAULT_QUEUE_JOB_OPTIONS,
}; 