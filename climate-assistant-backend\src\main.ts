import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as cookieParser from 'cookie-parser';
import { isProduction, lovableAppRegex } from './env-helper';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.use(cookieParser());
  app.useGlobalPipes(new ValidationPipe());
  app.enableCors({
    credentials: true,
    // origin: process.env.FRONTEND_URL,
    origin: isProduction
      ? [
          'https://app.glacier.eco',
          'https://prototype.glacier.eco',
          lovableAppRegex,
        ]
      : ['http://localhost:5173', 'http://localhost:8080'],
  });

  const config = new DocumentBuilder()
    .setTitle('Glacier API')
    .setDescription('Glacier App API standards')
    .setVersion('1.0')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/swagger', app, document);

  await app.listen(3000);

  // Send ready signal to PM2 when running in cluster mode
  if (process.send) {
    process.send('ready');
  }

  const workerId = process.env.INSTANCE_ID || process.pid;
  console.log(`Application is running on: http://localhost:3000`);
  console.log(`Worker ${workerId} started and ready to handle requests`);

  // Graceful shutdown handling
  const signals = ['SIGTERM', 'SIGINT'];
  signals.forEach((signal) => {
    process.on(signal, async () => {
      console.log(
        `Worker ${process.pid} received ${signal}, closing server...`
      );
      await app.close();
      process.exit(0);
    });
  });
}

bootstrap().catch((err) => {
  console.error('Failed to start application:', err);
  process.exit(1);
});
