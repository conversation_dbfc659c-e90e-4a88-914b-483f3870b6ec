import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DatapointRequestService } from './datapoint-request.service';
import { Workspace } from '../workspace/entities/workspace.entity';
import { DatapointRequestGuard } from './datapoint-request.guard';
import { DatapointRequestController } from './datapoint-request.controller';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { ESRSDatapoint } from './entities/esrs-datapoint.entity';
import { DatapointDocumentChunk } from 'src/datapoint-document-chunk/entities/datapoint-document-chunk.entity';
import { DataRequestModule } from 'src/data-request/data-request.module';
import { PromptModule } from 'src/prompts/prompts.module';
import { ProjectModule } from 'src/project/project.module';
import { UsersModule } from 'src/users/users.module';
import { WorkspaceModule } from 'src/workspace/workspace.module';
import { DatapointGeneration } from './entities/datapoint-generation.entity';
import { ESRSTopicDatapoint } from 'src/knowledge-base/entities/esrs-topic-datapoint.entity';
import { SharedModule } from 'src/shared/shared.module';
import { JobProcessor } from 'src/types/jobs';
import { BullModule } from '@nestjs/bull';
import { DocumentModule } from 'src/document/document.module';
import { PromptManagementModule } from 'src/prompt-management/prompt-management.module';
import { LlmRateLimiterModule } from 'src/llm-rate-limiter/llm-rate-limiter.module';
import { DEFAULT_QUEUE_JOB_OPTIONS } from 'src/util/queue.config';
import { DatapointCitationService } from './datapoint-citation.service';
import { DatapointStatusService } from './datapoint-status.service';
import { DatapointGenerationService } from './datapoint-generation.service';
import { DatapointDocumentService } from './datapoint-document.service';
import { MaterialTopicsService } from './material-topics.service';
import { DatapointTypeHelper } from './utils/datapoint-type.helper';
import { PromptProcessingUtilsService } from './services/prompt-processing-utils.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ESRSDatapoint,
      ESRSTopicDatapoint,
      DatapointRequest,
      Workspace,
      DatapointDocumentChunk,
      DatapointGeneration,
    ]),
    forwardRef(() => DataRequestModule), // this is a circular dependency
    LlmRateLimiterModule,
    PromptModule,
    PromptManagementModule,
    ProjectModule,
    UsersModule,
    WorkspaceModule,
    SharedModule,
    BullModule.registerQueue({
      name: JobProcessor.DatapointGeneration,
      defaultJobOptions: DEFAULT_QUEUE_JOB_OPTIONS,
    }),
    BullModule.registerQueue({
      name: JobProcessor.DatapointReview,
      defaultJobOptions: DEFAULT_QUEUE_JOB_OPTIONS,
    }),
    DataRequestModule,
    DocumentModule,
  ],
  providers: [
    DatapointRequestService,
    DatapointRequestGuard,
    DatapointCitationService,
    DatapointStatusService,
    DatapointGenerationService,
    DatapointDocumentService,
    MaterialTopicsService,
    DatapointTypeHelper,
    PromptProcessingUtilsService,
  ],
  exports: [
    DatapointRequestService,
    DatapointCitationService,
    DatapointStatusService,
    DatapointGenerationService,
    DatapointDocumentService,
    MaterialTopicsService,
    DatapointTypeHelper,
    PromptProcessingUtilsService,
  ],
  controllers: [DatapointRequestController],
})
export class DatapointRequestModule {}
