# PM2 Cluster Monitoring Guide

## Overview

This guide covers comprehensive monitoring setup for the Climate Assistant backend running in PM2 cluster mode with 3 worker processes. The monitoring stack includes Prometheus for metrics collection, Loki for log aggregation, and Graf<PERSON> for visualization and alerting.

## Architecture

```mermaid
graph TB
    subgraph "PM2 Cluster Monitoring Architecture"
        subgraph "Application Layer"
            PM2[PM2 Master]
            W0[Worker 0<br/>Port 3001<br/>Main Worker]
            W1[Worker 1<br/>Port 3002]
            W2[Worker 2<br/>Port 3003]
        end
        
        subgraph "Monitoring Stack"
            PROM[Prometheus<br/>Scrapes all workers]
            LOKI[Loki<br/>Aggregates logs]
            GRAF[Grafana<br/>Dashboards & Alerts]
        end
        
        subgraph "Exporters"
            CAD[cAdvisor<br/>Container metrics]
            NODE[Node Exporter<br/>System metrics]
            PG[Postgres Exporter<br/>DB metrics]
        end
    end
    
    PM2 --> W0
    PM2 --> W1
    PM2 --> W2
    
    W0 -->|metrics :3001/metrics| PROM
    W1 -->|metrics :3002/metrics| PROM
    W2 -->|metrics :3003/metrics| PROM
    
    W0 -->|logs| LOKI
    W1 -->|logs| LOKI
    W2 -->|logs| LOKI
    
    CAD -->|container metrics| PROM
    NODE -->|system metrics| PROM
    PG -->|db metrics| PROM
    
    PROM --> GRAF
    LOKI --> GRAF
    
    GRAF -->|Alerts| ALERT[Alert Manager<br/>Email/Slack]
```

## Key Metrics to Monitor

### Worker-Level Metrics

| Metric | Description | Alert Threshold |
|--------|-------------|-----------------|
| Worker CPU Usage | CPU utilization per worker | > 80% for 5 minutes |
| Worker Memory Usage | Memory consumption per worker | > 1.2GB |
| Worker Restart Count | Number of restarts per worker | > 3 in 10 minutes |
| Worker Response Time | Average response time per worker | > 1s |
| Worker Active Connections | Active HTTP connections per worker | > 500 |
| Worker Queue Jobs | Queue jobs processed per worker | < 1/min (stuck) |

### Cluster-Level Metrics

| Metric | Description | Alert Threshold |
|--------|-------------|-----------------|
| Total Request Rate | Requests/sec across all workers | Depends on baseline |
| Load Distribution | Request distribution variance | > 20% imbalance |
| Total Memory Usage | Combined memory of all workers | > 4GB |
| Database Connections | Total DB connections from all workers | > 20 |
| Queue Processing Rate | Total jobs processed/minute | < 10 (backlog) |

## Implementation

### 1. Application Metrics Exposure

Each worker must expose Prometheus metrics on a unique port:

```typescript
// src/main.ts - Updated for PM2 cluster metrics
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as promClient from 'prom-client';
import * as express from 'express';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Initialize Prometheus metrics
  const register = new promClient.Registry();
  promClient.collectDefaultMetrics({ register });
  
  // Add worker-specific labels
  register.setDefaultLabels({
    worker_id: process.env.INSTANCE_ID || 'unknown',
    worker_pid: process.pid.toString(),
  });
  
  // Custom metrics
  const httpRequestDuration = new promClient.Histogram({
    name: 'http_request_duration_seconds',
    help: 'Duration of HTTP requests in seconds',
    labelNames: ['method', 'route', 'status_code', 'worker_id'],
    registers: [register],
  });
  
  const activeConnections = new promClient.Gauge({
    name: 'worker_active_connections',
    help: 'Number of active connections',
    labelNames: ['worker_id'],
    registers: [register],
  });
  
  const queueJobsProcessed = new promClient.Counter({
    name: 'queue_jobs_processed_total',
    help: 'Total number of queue jobs processed',
    labelNames: ['job_type', 'status', 'worker_id'],
    registers: [register],
  });
  
  // Metrics endpoint (on different port per worker)
  const metricsApp = express();
  const metricsPort = 3001 + parseInt(process.env.INSTANCE_ID || '0');
  
  metricsApp.get('/metrics', async (req, res) => {
    res.set('Content-Type', register.contentType);
    res.end(await register.metrics());
  });
  
  metricsApp.listen(metricsPort, () => {
    console.log(`Worker ${process.env.INSTANCE_ID} metrics on port ${metricsPort}`);
  });
  
  // Main app
  await app.listen(3000);
}
bootstrap();
```

### 2. Prometheus Configuration

Update `prometheus/prometheus.yml` to scrape all workers:

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'backend-workers'
    static_configs:
      - targets: 
          - 'backend:3001'  # Worker 0
          - 'backend:3002'  # Worker 1
          - 'backend:3003'  # Worker 2
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        regex: 'backend:300(\d)'
        target_label: worker_id
        replacement: '${1}'

  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    metrics_path: /metrics

  - job_name: 'loki'
    static_configs:
      - targets: ['loki:3100']
    metrics_path: /metrics

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'pm2'
    static_configs:
      - targets: ['backend:9209']  # PM2 metrics exporter
    metrics_path: /metrics
```

### 3. Loki Configuration for Worker Logs

Update `promtail/promtail-config.yaml` to add worker labels:

```yaml
server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  - job_name: 'backend-workers'
    static_configs:
      - targets:
          - localhost
        labels:
          job: backend
          __path__: /var/log/climate-assistant/*.log
    pipeline_stages:
      - regex:
          expression: '^\[(?P<timestamp>.*?)\] \[(?P<level>\w+)\] \[Worker (?P<worker_id>\d+)\] (?P<message>.*)'
      - labels:
          level:
          worker_id:
      - timestamp:
          source: timestamp
          format: '2006-01-02 15:04:05'
```

## Grafana Dashboards

### PM2 Cluster Overview Dashboard

Key panels to include:

1. **Worker Status Grid**
   - Shows health status of each worker
   - CPU and memory usage per worker
   - Restart count per worker
   - Current connections per worker

2. **Load Distribution**
   - Request distribution across workers
   - Queue job distribution
   - Response time by worker

3. **Resource Usage Timeline**
   - CPU usage per worker over time
   - Memory usage per worker over time
   - Request rate per worker

4. **Cluster Health Metrics**
   - Total cluster CPU usage
   - Total cluster memory usage
   - Average response time
   - Error rate by worker

5. **Queue Monitoring**
   - Jobs processed per worker
   - Queue depth
   - Job processing time by type

### Updated Backend Monitoring Dashboard

See `grafana-dashboards/pm2-cluster-dashboard.json` for the complete dashboard configuration.

## Alert Rules

### Critical Alerts

```yaml
groups:
  - name: pm2_cluster_critical
    interval: 30s
    rules:
      - alert: WorkerDown
        expr: up{job="backend-workers"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Worker {{ $labels.worker_id }} is down"
          description: "Worker {{ $labels.worker_id }} has been down for more than 1 minute"

      - alert: WorkerHighMemory
        expr: worker_memory_usage_bytes > 1.2e9
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Worker {{ $labels.worker_id }} high memory usage"
          description: "Worker {{ $labels.worker_id }} memory usage is {{ $value | humanize }}"

      - alert: WorkerRestartLoop
        expr: rate(pm2_restart_count[5m]) > 0.5
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Worker {{ $labels.worker_id }} restart loop"
          description: "Worker {{ $labels.worker_id }} is restarting frequently"
```

### Warning Alerts

```yaml
  - name: pm2_cluster_warnings
    interval: 1m
    rules:
      - alert: LoadImbalance
        expr: |
          stddev(rate(http_requests_total[5m])) by (worker_id) 
          / avg(rate(http_requests_total[5m])) by (worker_id) > 0.2
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Load imbalance detected across workers"
          description: "Request distribution variance is {{ $value | humanizePercentage }}"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, http_request_duration_seconds) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time on worker {{ $labels.worker_id }}"
          description: "95th percentile response time is {{ $value }}s"

      - alert: DatabaseConnectionsHigh
        expr: sum(pg_stat_activity_count) by (worker_id) > 7
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High DB connections from worker {{ $labels.worker_id }}"
          description: "Worker using {{ $value }} database connections"
```

## Monitoring Best Practices

### 1. Regular Health Checks

- Monitor `/health/cluster` endpoint every 30 seconds
- Check worker distribution every 5 minutes
- Verify cron job execution on Worker 0

### 2. Performance Baselines

Establish baselines for:
- Normal request rate per worker
- Average memory usage per worker
- Queue processing rate
- Database connection count

### 3. Capacity Planning

Monitor trends for:
- Peak request times
- Memory growth over time
- Queue backlog patterns
- Database connection spikes

### 4. Debugging Tools

```bash
# Check worker status
pm2 status

# Monitor real-time metrics
pm2 monit

# Check worker logs
pm2 logs --lines 100

# Verify worker distribution
curl -s http://localhost:3000/health/cluster | jq

# Check Prometheus targets
curl -s http://localhost:9090/api/v1/targets | jq '.data.activeTargets[] | select(.job == "backend-workers")'
```

## Troubleshooting Common Issues

### Issue: Metrics Missing for a Worker

```bash
# Check if worker is exposing metrics
curl http://localhost:3001/metrics  # Worker 0
curl http://localhost:3002/metrics  # Worker 1
curl http://localhost:3003/metrics  # Worker 2

# Verify Prometheus can reach workers
docker exec prometheus wget -O- http://backend:3001/metrics
```

### Issue: Uneven Load Distribution

1. Check Grafana load distribution panel
2. Verify PM2 load balancer:
   ```bash
   pm2 show climate-assistant-backend | grep -i "load\|balance"
   ```
3. Check for long-running requests on specific workers

### Issue: Memory Leaks

1. Monitor memory growth rate per worker
2. Check for correlation with specific job types
3. Review heap snapshots:
   ```bash
   pm2 heapdump <worker_id>
   ```

## Next Steps

1. **Implement Custom Metrics**: Add business-specific metrics
2. **Set Up PagerDuty**: For critical alerts
3. **Create Runbooks**: For each alert type
4. **Performance Testing**: Establish baseline metrics
5. **Disaster Recovery**: Test worker failure scenarios 