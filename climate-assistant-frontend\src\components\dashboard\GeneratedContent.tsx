import { useMemo, useState } from 'react';
import { CopyIcon } from 'lucide-react';
// import * as Accordion from '@radix-ui/react-accordion';

import { TipTapEditor } from '../ui/tiptap/tiptap-editor';
import { Button } from '../ui/button';
import {
  Accordion,
  AccordionItem,
  AccordionContent,
  AccordionTrigger,
} from '../ui/accordion';
import { AssessmentResults } from './AssessmentResults';
import { GenerationRejectionReasonModal } from './GenerationRejectionReasonModal';
import { EventType, generationStatus, IDataGenerations } from '@/types/project';

type GeneratedContentPropsType = {
  generationContents: IDataGenerations[];
  generationType: EventType;
  handleApproveOrReject: (
    id: string,
    status: generationStatus,
    evaluatorComment?: string
  ) => void;
};

export const GeneratedContent = ({
  generationContents,
  generationType,
  handleApproveOrReject,
}: GeneratedContentPropsType) => {
  const [generationToReject, setGenerationToReject] = useState<string | null>(
    null
  );
  const nonApprovedGenerationsCount = generationContents.filter(
    ({ status }) => status === generationStatus.pending
  ).length;

  const sortedGeneratedContent = useMemo(
    () =>
      generationContents.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      ),
    [generationContents]
  );

  return (
    <Accordion
      type="single"
      collapsible
      className="bg-slate-200 p-2 rounded-sm mt-4"
    >
      <AccordionItem value="generations">
        <AccordionTrigger className="text-left w-full">
          <h2 className="text-xl font-bold underline underline-offset-2">
            {nonApprovedGenerationsCount} / {generationContents.length}{' '}
            Generations to Review
          </h2>
        </AccordionTrigger>
        <AccordionContent>
          {sortedGeneratedContent.map(
            ({
              id,
              status,
              data,
              createdAt,
              evaluator,
              evaluatedAt,
              evaluatorComment,
            }: IDataGenerations) => {
              const isApproved = status === generationStatus.approved;
              const isMinorChanges = status === generationStatus.minorChanges;
              const isRejected = status === generationStatus.rejected;
              return (
                <div key={id} className="mt-8">
                  <h3 className="underline">
                    Generation ID: {id}
                    <CopyIcon
                      onClick={() => navigator.clipboard.writeText(id)}
                      className="h-5 w-5 inline ml-1 cursor-pointer"
                    />
                  </h3>
                  <p>
                    Generation Status:{' '}
                    <span
                      className={`font-bold uppercase ${isApproved || isMinorChanges ? 'text-glacier-greendark' : isRejected ? 'text-red-500' : ''}`}
                    >
                      {status}
                    </span>
                  </p>
                  <p>
                    Generation date:{' '}
                    <span className="font-bold">
                      {new Date(createdAt).toDateString()}
                    </span>
                  </p>
                  {evaluator && (
                    <p>
                      Author:{' '}
                      <span className="font-bold">{evaluator.name}</span>
                    </p>
                  )}
                  {evaluatedAt && (
                    <p>
                      Evaluation date:{' '}
                      <span className="font-bold">
                        {new Date(evaluatedAt).toDateString()}
                      </span>
                    </p>
                  )}
                  {evaluatorComment && evaluatorComment.length > 0 && (
                    <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-md">
                      <h4 className="font-semibold text-red-800 mb-2">
                        Evaluation Results:
                      </h4>
                      {(() => {
                        try {
                          const evaluation = JSON.parse(evaluatorComment);
                          return (
                            <div className="space-y-2">
                              {Object.entries(evaluation).map(
                                ([category, data]: [string, any]) => (
                                  <div key={category} className="text-sm">
                                    <span className="font-medium capitalize text-red-700">
                                      {category.replace('_', ' ')}:
                                    </span>{' '}
                                    <span className="text-red-600">
                                      {data.status || data.score || 'N/A'}
                                    </span>
                                    {data.comment && (
                                      <div className="text-red-500 text-xs ml-2 italic">
                                        "{data.comment}"
                                      </div>
                                    )}
                                  </div>
                                )
                              )}
                            </div>
                          );
                        } catch {
                          return (
                            <p className="text-red-600">
                              Rejection Reason: {evaluatorComment}
                            </p>
                          );
                        }
                      })()}
                    </div>
                  )}
                  <p>Generation Text:</p>
                  <TipTapEditor
                    refId={`${id}-generation`}
                    content={data.content}
                    setContent={() => {}}
                    isEditable={false}
                  />
                  <div className="mt-4 flex flex-wrap space-x-2">
                    <Button
                      variant="forest"
                      disabled={isApproved}
                      onClick={() =>
                        handleApproveOrReject(id, generationStatus.approved)
                      }
                    >
                      Approve
                    </Button>
                    <Button
                      variant="secondary"
                      disabled={isMinorChanges}
                      onClick={() =>
                        handleApproveOrReject(id, generationStatus.minorChanges)
                      }
                    >
                      Minor Changes
                    </Button>
                    <Button
                      variant="destructive"
                      disabled={isRejected}
                      onClick={() => setGenerationToReject(id)}
                    >
                      Reject
                    </Button>
                    {generationType === EventType.DatapointRequest && (
                      <AssessmentResults
                        generationId={id}
                        content={data.content}
                      />
                    )}
                  </div>
                </div>
              );
            }
          )}
        </AccordionContent>
      </AccordionItem>

      {generationToReject && (
        <GenerationRejectionReasonModal
          open={!!generationToReject}
          setOpen={() => setGenerationToReject(null)}
          generationId={generationToReject}
          callback={(data, generationId) => {
            handleApproveOrReject(
              generationId,
              generationStatus.rejected,
              data.comment || ''
            );
          }}
        />
      )}
    </Accordion>
  );
};
