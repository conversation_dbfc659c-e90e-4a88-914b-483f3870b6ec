import { LLM_MODELS } from '../constants';

export interface ModelLimits {
  maxTokensPerMinute: number;
  maxRequestsPerMinute: number;
}

export interface TokenUsage {
  tokensUsed: number;
  requestsUsed: number;
  lastReset: number;
}

export const MODEL_LIMITS: Record<LLM_MODELS, ModelLimits> = {
  [LLM_MODELS['gpt-4o']]: {
    maxTokensPerMinute: 445_000,
    maxRequestsPerMinute: 267,
  },
  [LLM_MODELS['o3-mini']]: {
    maxTokensPerMinute: 5_000_000,
    maxRequestsPerMinute: 500,
  },
  [LLM_MODELS['o4-mini']]: {
    maxTokensPerMinute: 7_500_000,
    maxRequestsPerMinute: 500,
  },
  [LLM_MODELS['gpt-4o-mini']]: {
    maxTokensPerMinute: 90_000,
    maxRequestsPerMinute: 700,
  },
  [LLM_MODELS['deepseek-r1']]: {
    maxTokensPerMinute: 400_000,
    maxRequestsPerMinute: 700,
  },
  [LLM_MODELS['o3']]: {
    maxTokensPerMinute: 400_000,
    maxRequestsPerMinute: 700,
  },
};

export const RATE_LIMIT_CONFIG = {
  RESET_INTERVAL: 80 * 1000, // 80 seconds in milliseconds
  REDIS_KEY_PREFIX: 'llm_rate_limit',
  REDIS_KEY_EXPIRY: 120, // 120 seconds
} as const;

/**
 * Get Redis key for rate limiting data
 */
export function getRedisKey(
  model: LLM_MODELS,
  field: 'tokens' | 'requests' | 'lastReset'
): string {
  return `${RATE_LIMIT_CONFIG.REDIS_KEY_PREFIX}:${model}:${field}`;
}

/**
 * Get model status based on usage percentages
 */
export function getModelStatus(
  tokensUsed: number,
  requestsUsed: number,
  limits: ModelLimits
): string {
  const tokenPercentage = (tokensUsed / limits.maxTokensPerMinute) * 100;
  const requestPercentage = (requestsUsed / limits.maxRequestsPerMinute) * 100;
  const maxPercentage = Math.max(tokenPercentage, requestPercentage);

  if (maxPercentage >= 95) return 'critical';
  if (maxPercentage >= 80) return 'warning';
  if (maxPercentage >= 50) return 'moderate';
  if (maxPercentage > 0) return 'active';
  return 'idle';
}

/**
 * Calculate usage percentages for a model
 */
export function calculateUsagePercentages(
  tokensUsed: number,
  requestsUsed: number,
  limits: ModelLimits
): { tokensPercentage: number; requestsPercentage: number } {
  return {
    tokensPercentage: Math.round(
      (tokensUsed / limits.maxTokensPerMinute) * 100
    ),
    requestsPercentage: Math.round(
      (requestsUsed / limits.maxRequestsPerMinute) * 100
    ),
  };
}
