import { useState } from 'react';
import { FileI<PERSON>, ChevronDown } from 'lucide-react';

import { Button } from '../ui/button';
import { toast } from '../ui/use-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';

import { 
  downloadDataRequestReport,
  downloadProjectDatapointGaps,
  downloadProjectDrGaps
} from '@/api/project-settings/project-settings.api';
import { Project } from '@/types/project';

type ExportType = 'report' | 'dr-gaps' | 'datapoint-gaps';

export const ExportGapsDropdown = ({ project }: { project: Project }) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleExport = async (exportType: ExportType) => {
    setIsLoading(true);
    try {
      let blob: any;
      let filename: string;
      let mimeType: string;

      switch (exportType) {
        case 'report':
          blob = await downloadDataRequestReport(project.id);
          filename = `${project.name} Reporttext.docx`;
          mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
          break;
        case 'dr-gaps':
          blob = await downloadProjectDrGaps(project.id);
          filename = `${project.name} DR Gaps.xlsx`;
          mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          break;
        case 'datapoint-gaps':
          blob = await downloadProjectDatapointGaps(project.id);
          filename = `${project.name} Datapoint Gaps.xlsx`;
          mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          break;
      }

      const url = window.URL.createObjectURL(new Blob([blob], { type: mimeType }));
      const link = document.createElement('a');

      link.href = url;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();

      // Clean up
      link.parentNode?.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: 'Download Started',
        variant: 'success',
      });
    } catch (error: any) {
      toast({
        title: 'Error exporting file',
        description:
          error.response?.status === 404
            ? 'No data found for this export type'
            : error.message,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" disabled={isLoading}>
          <FileIcon className="w-4 h-4 mr-2" />
          Export
          <ChevronDown className="w-4 h-4 ml-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuItem 
          onClick={() => handleExport('report')}
          className="cursor-pointer"
        >
          <FileIcon className="w-4 h-4 mr-2" />
          Export Report
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => handleExport('dr-gaps')}
          className="cursor-pointer"
        >
          <FileIcon className="w-4 h-4 mr-2" />
          Export Disclosure Requirements GAPs
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => handleExport('datapoint-gaps')}
          className="cursor-pointer"
        >
          <FileIcon className="w-4 h-4 mr-2" />
          Export Datapoint GAPs
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}; 