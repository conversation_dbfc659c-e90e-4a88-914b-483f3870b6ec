import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

const savePromptSchema = z.object({
  comment: z.string().optional(),
});

export type SavePromptFormData = z.infer<typeof savePromptSchema>;

interface SavePromptConfirmModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  onConfirm: (data: SavePromptFormData) => void;
  promptsCount?: number;
  promptName?: string;
}

export function SavePromptConfirmModal({
  open,
  setOpen,
  onConfirm,
  promptsCount,
  promptName,
}: SavePromptConfirmModalProps) {
  const { register, handleSubmit, reset } = useForm<SavePromptFormData>({
    defaultValues: {
      comment: '',
    },
    resolver: zodResolver(savePromptSchema),
  });

  const onSubmit = async (data: SavePromptFormData) => {
    onConfirm(data);
    setOpen(false);
    reset();
  };

  const handleCancel = () => {
    setOpen(false);
    reset();
  };

  const isSinglePrompt = promptsCount === 1 || promptName;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[625px]">
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-6">
          <DialogHeader>
            <DialogTitle>Save Prompt Changes</DialogTitle>
            <DialogDescription>
              {isSinglePrompt ? (
                <>
                  You are about to save changes to{' '}
                  {promptName ? (
                    <span className="font-medium">{promptName}</span>
                  ) : (
                    'this prompt'
                  )}
                  . A new version will be created with a unique ID. Please
                  provide a comment describing the changes (optional).
                </>
              ) : (
                <>
                  You are about to save changes to {promptsCount} prompt
                  {promptsCount && promptsCount > 1 ? 's' : ''}. Each prompt
                  will be replaced with a new version that has a unique ID.
                  Please provide a comment describing the changes (optional).
                </>
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-2">
            <Label htmlFor="comment">Change Comment (Optional)</Label>
            <Textarea
              id="comment"
              placeholder="Describe the changes you made to the prompt(s)..."
              autoCapitalize="none"
              autoCorrect="off"
              rows={4}
              {...register('comment')}
            />
          </div>

          <DialogFooter>
            <Button type="submit">Save Changes</Button>
            <Button type="button" onClick={handleCancel} variant="secondary">
              Cancel
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
