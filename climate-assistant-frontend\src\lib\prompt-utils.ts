/**
 * Utility functions for handling Handlebars prompt templates
 * Provides consistent variable extraction and validation logic
 */

// Handlebars helper names - kept in sync with backend constants
export const HELPER_NAMES = {
  JSON: 'json',
  LOOKUP: 'lookup',
  CONTAINS: 'contains',
  EQ: 'eq',
  GENERATION_LANGUAGE: 'generationLanguage',
  IS_TRUTHY: 'isTruthy',
  LANGUAGE_NAME: 'languageName',
} as const;

// Define regex patterns with descriptive names and detailed comments
const REGEX_PATTERNS = {
  // Matches: {{#if variableName}} - Captures conditional block openers
  // Input: "{{#if user}} Welcome {{/if}}"
  // Output: Captures "user" as conditional variable
  conditionalIfOpener: /\{\{#if\s+(\w+)\}\}/g,

  // Matches: {{#if variable}}content{{/if}} - Captures entire conditional blocks
  // Input: "{{#if isActive}}User is active: {{userName}}{{/if}}"
  // Output: Captures "isActive" and "User is active: {{userName}}" as content
  conditionalIfBlock: /\{\{#if\s+(\w+)\}\}([\s\S]*?)\{\{\/if\}\}/g,

  // Matches: {{variable}} - Simple variable references
  // Input: "Hello {{userName}}"
  // Output: Captures "userName"
  simpleVariable: /\{\{(\w+)\}\}/g,

  // Matches: {{helper arg1 arg2}} - Helper function calls with arguments
  // Input: "{{lookup user 'name'}}" or "{{eq status 'active'}}"
  // Output: Captures helper name and arguments
  helperWithArgs: /\{\{(?!#|\/)\s*(\w+)\s+([^}]+)\}\}/g,

  // Matches: {{#unless}} {{#each}} {{#with}} - Other block helpers
  // Input: "{{#each items}}" or "{{#unless isEmpty}}"
  // Output: Captures the variable being operated on
  blockHelpers: /\{\{#(?:unless|each|with)\s+(\w+)/g,

  // Matches: numbers, booleans, null - Literal values to filter out
  // Input: "123", "true", "false", "null"
  // Output: Used to identify non-variable tokens
  literalValues: /^\d+$|^(true|false|null)$/,

  // Matches: word tokens in helper arguments
  // Input: "user 'name' active"
  // Output: ["user", "name", "active"]
  wordTokens: /\w+/g,
};

// Export commonly used regex patterns for syntax highlighting and other UI features
export const SYNTAX_PATTERNS = {
  // Matches all handlebars expressions for syntax highlighting
  // Input: "Hello {{userName}} and {{#if active}}welcome{{/if}}"
  // Output: Matches "{{userName}}" and "{{#if active}}" and "{{/if}}"
  allHandlebarsExpressions: /\{\{[^}]+\}\}/g,

  // Matches block helper opening/closing tags and else statements
  // Input: "{{#if user}}", "{{/if}}", "{{else}}"
  // Output: Matches these control flow elements
  blockTags: /\{\{(?:#|\/)?(?:if|unless|each|with|else)[^}]*\}\}/g,

  // Matches complete conditional blocks with their content
  // Input: "{{#if user}}Hello {{userName}}{{/if}}"
  // Output: Matches the entire conditional block
  conditionalBlocks: (
    blockHelpers: string[] = ['if', 'unless', 'each', 'with']
  ) =>
    new RegExp(
      `\\{\\{#(${blockHelpers.join('|')})\\s+[^}]+\\}\\}[\\s\\S]*?\\{\\{\\/(\\1)\\}\\}`,
      'g'
    ),
} as const;

export interface PromptVariableExtractionResult {
  required: Set<string>;
  optional: Map<string, { content: string; hasVariable: boolean }>;
}

/**
 * Extracts variables from a Handlebars prompt template
 * Categorizes variables as required (always needed) or optional (only needed in certain conditions)
 *
 * @param prompt - The Handlebars template string to analyze
 * @returns Object containing required and optional variable sets
 *
 * @example
 * const prompt = "Hello {{userName}}{{#if isVip}}, you have {{vipPoints}} points{{/if}}";
 * const result = extractPromptVariables(prompt);
 * // result.required contains: ["userName", "isVip"]
 * // result.optional contains: ["vipPoints"] (only used when isVip is true)
 */
export const extractPromptVariables = (
  prompt: string
): PromptVariableExtractionResult => {
  const required = new Set<string>();
  const optional = new Map<string, { content: string; hasVariable: boolean }>();

  // Known Handlebars helpers - used consistently with backend
  const knownHelpers: string[] = Object.values(HELPER_NAMES);

  // Step 1: Extract conditional variables as REQUIRED
  // These are variables that control if/unless conditions and must be provided
  const conditionalMatches = prompt.matchAll(
    REGEX_PATTERNS.conditionalIfOpener
  );
  for (const match of conditionalMatches) {
    const condVar = match[1];
    required.add(condVar);
  }

  // Step 2: Extract variables from INSIDE conditional blocks as OPTIONAL
  // These variables are only used when certain conditions are met
  const blockMatches = prompt.matchAll(REGEX_PATTERNS.conditionalIfBlock);
  for (const match of blockMatches) {
    const [, , content] = match; // condVar not needed here, only content
    const varsInBlock = new Set<string>();

    // Extract simple variables from block content
    // Example: {{userName}} -> "userName"
    const simpleVarMatches = content.matchAll(REGEX_PATTERNS.simpleVariable);
    for (const varMatch of simpleVarMatches) {
      varsInBlock.add(varMatch[1]);
    }

    // Extract variables from helper calls within the block
    // Example: {{lookup user 'name'}} -> "user" (and potentially "name" if it's a variable)
    const helperMatches = content.matchAll(REGEX_PATTERNS.helperWithArgs);
    for (const helperMatch of helperMatches) {
      const [, , args] = helperMatch; // helper not needed for this extraction
      const tokens = args.match(REGEX_PATTERNS.wordTokens) || [];

      tokens
        .filter((token) => !REGEX_PATTERNS.literalValues.test(token))
        .forEach((token) => varsInBlock.add(token));
    }

    // Mark all variables found in conditional blocks as optional
    varsInBlock.forEach((v) =>
      optional.set(v, { content: content.trim(), hasVariable: true })
    );
  }

  // Step 3: Extract required variables from outside conditionals
  // Remove all conditional blocks to get the "always executed" part of the template
  const promptWithoutConditionals = prompt.replace(
    REGEX_PATTERNS.conditionalIfBlock,
    ''
  );

  // Extract simple variables from non-conditional parts
  const requiredSimpleMatches = promptWithoutConditionals.matchAll(
    REGEX_PATTERNS.simpleVariable
  );
  for (const match of requiredSimpleMatches) {
    const variable = match[1];
    if (!optional.has(variable)) {
      required.add(variable);
    }
  }

  // Extract variables from helper calls in non-conditional parts
  const requiredHelperMatches = promptWithoutConditionals.matchAll(
    REGEX_PATTERNS.helperWithArgs
  );
  for (const match of requiredHelperMatches) {
    const [, helper, args] = match;
    const tokens = args.match(REGEX_PATTERNS.wordTokens) || [];

    tokens.forEach((token, idx) => {
      if (!REGEX_PATTERNS.literalValues.test(token) && !optional.has(token)) {
        // Special handling for 'lookup' helper: second argument (idx 1) is usually a property name
        // For other helpers, all non-literal tokens are considered variables
        if (
          (helper === HELPER_NAMES.LOOKUP && idx === 1) ||
          !knownHelpers.includes(helper) ||
          (knownHelpers.includes(helper) && helper !== HELPER_NAMES.LOOKUP)
        ) {
          required.add(token);
        }
      }
    });
  }

  // Extract variables from other block helpers (unless, each, with)
  const blockHelperMatches = promptWithoutConditionals.matchAll(
    REGEX_PATTERNS.blockHelpers
  );
  for (const match of blockHelperMatches) {
    const variable = match[1];
    if (!optional.has(variable)) {
      required.add(variable);
    }
  }

  return { required, optional };
};

/**
 * Identifies missing variables from a prompt template
 * Compares the variables found in the prompt against the provided required variables
 *
 * @param prompt - The Handlebars template string to analyze
 * @param requiredVariables - Object containing the expected variables
 * @returns Array of variable names that are expected but not found in the prompt
 *
 * @example
 * const prompt = "Hello {{userName}}";
 * const required = { userName: "string", email: "string" };
 * const missing = getMissingVariables(prompt, required);
 * // missing = ["email"] - email is expected but not found in prompt
 */
export const getMissingVariables = (
  prompt: string,
  requiredVariables: Record<string, string>
): string[] => {
  const { required, optional } = extractPromptVariables(prompt);
  return Object.keys(requiredVariables || {}).filter(
    (v) => !required.has(v) && !optional.has(v)
  );
};

/**
 * Validates if a prompt template contains all the required variables
 *
 * @param prompt - The Handlebars template string to validate
 * @param requiredVariables - Object containing the expected variables
 * @returns true if all required variables are present, false otherwise
 */
export const validatePromptVariables = (
  prompt: string,
  requiredVariables: Record<string, string>
): boolean => {
  const missingVars = getMissingVariables(prompt, requiredVariables);
  return missingVars.length === 0;
};

/**
 * Gets detailed information about prompt variables for debugging/display purposes
 *
 * @param prompt - The Handlebars template string to analyze
 * @returns Object with arrays of required and optional variables with metadata
 */
export const getPromptVariableDetails = (prompt: string) => {
  const { required, optional } = extractPromptVariables(prompt);

  return {
    required: Array.from(required),
    optional: Array.from(optional.entries()).map(([name, data]) => ({
      name,
      content: data.content,
      hasVariable: data.hasVariable,
    })),
  };
};
