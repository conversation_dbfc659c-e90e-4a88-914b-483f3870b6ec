import { Modu<PERSON> } from '@nestjs/common';
import { ChatService } from './chat.service';
import { Chat<PERSON>ontroller } from './chat.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ChatHistory } from './entities/chat-history.entity';
import { ChatMessage } from './entities/chat.message.entity';
import { UsersModule } from '../users/users.module';
import { ChatGptService } from '../llm/chat-gpt.service';
import { KnowledgeBaseModule } from '../knowledge-base/knowledge-base.module';
import { PerplexityService } from '../util/perplexity.service';
import { InitiativeSuggestionService } from './initiative-suggestion.service';
import { MultiQuestionSearchEngine } from '../util/multi-question-search-engine.service';
import { InitiativeDetailService } from './initiative-detail.service';
import { CsrdReportingService } from './csrd-reporting.service';
import { SearchEngineTool } from '../util/search-engine.tool';

@Module({
  imports: [
    TypeOrmModule.forFeature([ChatHistory, ChatMessage]),
    UsersModule,
    KnowledgeBaseModule,
  ],
  providers: [
    ChatService,
    ChatGptService,
    PerplexityService,
    InitiativeSuggestionService,
    MultiQuestionSearchEngine,
    InitiativeDetailService,
    CsrdReportingService,
    SearchEngineTool,
  ],
  controllers: [ChatController],
})
export class ChatModule {}
