import {
  Injectable,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { RegisterWithCompanyDto } from './auth.dto';
import { Token } from 'src/users/entities/token.entity';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService
  ) {}

  async login(email: string, password: string): Promise<string> {
    const user = await this.usersService.findByEmailWithPassword(email);

    if (user === null) {
      throw new UnauthorizedException({
        message: 'E-Mail oder Passwort ist inkorrekt',
      });
    }

    const isPasswordCorrect = await bcrypt.compare(password, user?.password);
    if (!isPasswordCorrect) {
      throw new UnauthorizedException({
        message: 'E-Mail oder Passwort ist inkorrekt',
      });
    }

    const workspace = await this.usersService.findFirstWorkspaceIdByUser(user);

    const payload = {
      sub: user.id,
      id: user.id,
      email: user.email,
      workspaceId: workspace.workspaceId,
    };
    return await this.jwtService.signAsync(payload);
  }

  async registerWithCompany(
    registerDto: RegisterWithCompanyDto
  ): Promise<string> {
    const { email, password, companyName } = registerDto;

    // Check if the user already exists
    const existingUser = await this.usersService.findByEmail(email);
    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create the user, company, and workspace (you need to implement this method)
    const register = await this.usersService.createUserWithCompanyAndWorkspace({
      email,
      password: hashedPassword,
      companyName,
    });

    const payload = {
      sub: register.user.id,
      id: register.user.id,
      email: email,
      workspaceId: register.workspace.id,
    };
    return await this.jwtService.signAsync(payload);
  }

  async resetPassword(password: string, token: string, fullName?: string) {
    const userToken = await this.usersService.validateToken(token);
    const user = await this.usersService.resetUserPassword(
      userToken,
      password,
      fullName
    );
    const payload = {
      sub: user.id,
      id: user.id,
      email: user.email,
      workspaceId: user.userWorkspaces[0].workspaceId,
    };
    return await this.jwtService.signAsync(payload);
  }

  async sendPasswordResetEmail(email: string, origin: string): Promise<void> {
    await this.usersService.sendPasswordResetEmail({
      email,
      origin,
      shouldSendEmail: origin.endsWith('.glacier.eco'),
    });
  }

  async validateToken(token: string): Promise<Token> {
    return this.usersService.validateToken(token);
  }

  async switchUserWorkspace(
    userId: string,
    workspaceId: string
  ): Promise<string> {
    const user = await this.usersService.switchWorkspace(userId, workspaceId);

    const payload = {
      sub: user.id,
      id: user.id,
      email: user.email,
      workspaceId: workspaceId,
    };

    return await this.jwtService.signAsync(payload);
  }
}
