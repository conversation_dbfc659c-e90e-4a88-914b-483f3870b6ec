import { Injectable } from '@nestjs/common';
import { User } from '../users/entities/user.entity';
import { PostmarkService } from './postmark.service';
import { WorkerLogger } from 'src/shared/logger.service';

const TEMPLATE_ALIAS = {
  RESET_PASSWORD: 'ai-reset-password',
  INVITE_USER: 'ai-invite-user',
};

@Injectable()
export class EmailService {
  constructor(private readonly postmarkService: PostmarkService) {}

  private readonly logger = new WorkerLogger(EmailService.name);

  async sendPasswordReset({
    email,
    userName,
    resetToken,
    origin,
  }: {
    email: string;
    userName: string;
    resetToken: string;
    origin: string;
  }): Promise<void> {
    this.logger.log(`Sending password reset email to: ${email}`);
    this.logger.log(`Reset token: ${resetToken}`);

    await this.postmarkService.sendTemplatedEmail({
      templateAlias: TEMPLATE_ALIAS.RESET_PASSWORD,
      to: email,
      templateModel: {
        name: userName,
        reset_link: `${origin}/reset-password?token=${resetToken}`,
      },
    });

    this.logger.log('Password reset email sent successfully.');
  }

  async inviteUser({
    token,
    invitingUser,
    email,
    origin,
  }: {
    token: string;
    invitingUser: User;
    email: string;
    origin: string;
  }): Promise<void> {
    this.logger.log(`Sending invite user email to: ${email}`);

    await this.postmarkService.sendTemplatedEmail({
      templateAlias: TEMPLATE_ALIAS.INVITE_USER,
      to: email,
      templateModel: {
        inviter_name: invitingUser.name || invitingUser.email,
        invite_link: `${origin}/reset-password?token=${token}&invite=true`,
      },
    });

    this.logger.log('Email sent successfully.');
  }
}
