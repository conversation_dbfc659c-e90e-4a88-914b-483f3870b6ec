```json
{
  "promptTechniques": [
    {
      "id": 1,
      "technique": "Role Prompting",
      "promptTemplate": "You are a senior {language} developer. Review this function for {goal}.",
      "purpose": "Simulate expert-level code review, debugging, or refactoring"
    },
    {
      "id": 2,
      "technique": "Explicit Context Setup",
      "promptTemplate": "Here’s the problem: {summary}. The code is below. It should do {expected behavior}, but instead it’s doing {actual behavior}. Why?",
      "purpose": "Frame the problem clearly to avoid generic, surface-level responses"
    },
    {
      "id": 3,
      "technique": "Input/Output Examples",
      "promptTemplate": "This function should return {expected output} when given {input}. Can you write or fix the code?",
      "purpose": "Guide the assistant by showing intent through examples"
    },
    {
      "id": 4,
      "technique": "Iterative Chaining",
      "promptTemplate": "First, generate a skeleton of the component. Next, we’ll add state. Then handle API calls.",
      "purpose": "Break larger tasks into steps to avoid overwhelming or vague prompts"
    },
    {
      "id": 5,
      "technique": "Debug with Simulation",
      "promptTemplate": "Walk through the function line by line. What are the variable values? Where might it break?",
      "purpose": "Get the assistant to simulate runtime behavior and surface hidden bugs"
    },
    {
      "id": 6,
      "technique": "Feature Blueprinting",
      "promptTemplate": "I’m building {feature}. Requirements: {bullets}. Using: {tech stack}. Please scaffold the initial component and explain your choices.",
      "purpose": "Kick off feature development with AI-led planning and scaffolding"
    },
    {
      "id": 7,
      "technique": "Code Refactor Guidance",
      "promptTemplate": "Refactor this code to improve {goal}, such as {e.g., readability, performance, idiomatic style}. Use comments to explain changes.",
      "purpose": "Make AI refactors align with your goals, not arbitrary changes"
    },
    {
      "id": 8,
      "technique": "Ask for Alternatives",
      "promptTemplate": "Can you rewrite this using a functional style? What would a recursive version look like?",
      "purpose": "Explore multiple implementation paths and expand your toolbox"
    },
    {
      "id": 9,
      "technique": "Rubber Ducking",
      "promptTemplate": "Here’s what I think this function does: {your explanation}. Am I missing anything? Does this reveal any bugs?",
      "purpose": "Let the AI challenge your understanding and spot inconsistencies"
    },
    {
      "id": 10,
      "technique": "Constraint Anchoring",
      "promptTemplate": "Please avoid {e.g., recursion} and stick to {e.g., ES6 syntax, no external libraries}. Optimize for {e.g., memory}. Here’s the function:",
      "purpose": "Prevent the AI from over-reaching or introducing incompatible patterns"
    }
  ],

  "foundationalPrinciples": [
    {
      "principle": "Provide rich context",
      "description": "Assume the AI knows nothing about your project—include language, framework, code, and exact errors."
    },
    {
      "principle": "Be specific about the goal",
      "description": "Replace vague asks with explicit questions or desired optimisations."
    },
    {
      "principle": "Break down complex tasks",
      "description": "Tackle multi-step work in smaller, iterative prompts."
    },
    {
      "principle": "Include input/output examples",
      "description": "Show concrete examples to reduce ambiguity (few-shot prompting)."
    },
    {
      "principle": "Leverage roles or personas",
      "description": "Prime the assistant by asking it to act as a senior dev, reviewer, security analyst, etc."
    },
    {
      "principle": "Iterate and refine",
      "description": "Treat prompting as a dialogue—review, nudge, and correct the AI step-by-step."
    },
    {
      "principle": "Maintain code clarity",
      "description": "Clean, consistent code and comments improve both human and AI comprehension."
    }
  ],

  "promptPatterns": {
    "debugging": {
      "corePatterns": [
        "Describe the problem & symptoms (code + error + expected vs. actual)",
        "Walk through code line-by-line / simulate execution",
        "Provide a minimal reproducible example (MRE)",
        "Ask focused follow-ups (e.g. fix + explanation)"
      ],
      "additionalTactics": [
        "Brainstorm possible causes",
        "\"Ask the Rubber Duck\" – explain code and let AI critique",
        "Ask AI to generate edge-case test cases",
        "Role-play a meticulous code reviewer"
      ]
    },

    "refactoring": {
      "corePatterns": [
        "State refactor goals explicitly (readability, performance, paradigm, etc.)",
        "Give full code context and any version / environment constraints",
        "Request explanations of each change",
        "Role-play a senior engineer to raise the bar"
      ],
      "tips": [
        "Refactor in steps for large codebases",
        "Request alternative approaches for comparison",
        "Combine refactor with unit-test generation for validation"
      ]
    },

    "featureImplementation": {
      "corePatterns": [
        "Start with a high-level plan, then drill down task-by-task",
        "Supply project-specific context or reference code",
        "Use inline comments / TODOs in IDE-based prompting",
        "Show expected I/O or usage examples to lock in behaviour",
        "Refine prompts when output misses constraints"
      ],
      "additionalTips": [
        "Let AI scaffold boilerplate, then you fill specifics",
        "Ask AI to list edge cases before coding",
        "Documentation-driven development – write docstring first, code second"
      ]
    }
  },

  "antiPatterns": [
    {
      "name": "Vague Prompt",
      "description": "Too little context → generic answers",
      "fix": "Add code, error, expected vs. actual behaviour"
    },
    {
      "name": "Overloaded Prompt",
      "description": "Too many tasks in one ask",
      "fix": "Split work into sequential prompts"
    },
    {
      "name": "Missing the Question",
      "description": "No clear ask after dumping code",
      "fix": "End with a specific instruction or question"
    },
    {
      "name": "Vague Success Criteria",
      "description": "Unclear definition of 'better' or 'faster'",
      "fix": "Quantify or qualify goals (e.g., O(n) time, remove globals)"
    },
    {
      "name": "Ignoring AI Clarifications",
      "description": "Skipping model's follow-up questions",
      "fix": "Answer clarifications or rephrase prompt with new info"
    },
    {
      "name": "Inconsistent Style",
      "description": "Mixed paradigms or ambiguous references like 'above code'",
      "fix": "Keep style consistent; explicitly re-quote code when needed"
    }
  ]
}
```
