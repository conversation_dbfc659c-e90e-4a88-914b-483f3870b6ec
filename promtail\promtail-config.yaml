server:
  http_listen_port: 9080

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  - job_name: container_logs
    docker_sd_configs:
      - host: unix:///var/run/docker.sock
        refresh_interval: 5s
    relabel_configs:
      - source_labels: ['__meta_docker_container_name']
        target_label: 'container'
      - source_labels: ['__meta_docker_container_log_stream']
        target_label: 'stream'
    pipeline_stages:
      - docker: {}

      # Backend container logs with NestJS timestamps and worker parsing
      - match:
          selector: '{container=~".*backend.*"}'
          stages:
            # Match NestJS formatted logs with worker ID: [Nest] PID - MM/DD/YYYY, HH:MM:SS AM/PM LOG [Context] [worker-X] message
            - regex:
                expression: '\[Nest\]\s+(?P<pid>\d+)\s+-\s+(?P<nestjs_timestamp>\d{2}/\d{2}/\d{4},\s+\d{1,2}:\d{2}:\d{2}\s+[AP]M)\s+(?P<level>[A-Z]+)\s+\[(?P<context>[^\]]+)\]\s+\[(?P<worker_id>worker-\d+|pid-\d+)\]\s+(?P<message>.*)'
            - labels:
                level:
                worker_id:
                context:
                pid:
            - timestamp:
                source: nestjs_timestamp
                format: '01/02/2006, 3:04:05 PM'

            # Fallback for logs without worker ID but with NestJS format
            - regex:
                expression: '\[Nest\]\s+(?P<pid>\d+)\s+-\s+(?P<nestjs_timestamp>\d{2}/\d{2}/\d{4},\s+\d{1,2}:\d{2}:\d{2}\s+[AP]M)\s+(?P<level>[A-Z]+)\s+\[(?P<context>[^\]]+)\]\s+(?P<message>.*)'
            - labels:
                level:
                context:
                pid:

            # Extract error types
            - regex:
                expression: '(?P<error_type>Error|Exception|Failed|Timeout|Refused|Rejected)'
            - labels:
                error_type:

            # Extract queue job types
            - regex:
                expression: 'Processing queue job: (?P<queue_job>[^ ]+)'
            - labels:
                queue_job:

            # Extract HTTP status codes
            - regex:
                expression: 'HTTP/\d+\.\d+"\s+(?P<status_code>\d{3})'
            - labels:
                status_code:

      # PM2 logs from the PM2 daemon
      - match:
          selector: '{container=~".*backend.*", stream="stderr"}'
          stages:
            - regex:
                expression: 'PM2\s+\|\s+(?P<timestamp>[^\|]+)\s+\|\s+(?P<level>[A-Z]+)\s+\|\s+(?P<message>.*)'
            - labels:
                level:
                source: pm2
            - timestamp:
                source: timestamp
                format: '2006-01-02 15:04:05'

      # Redis logs
      - match:
          selector: '{container="redis"}'
          stages:
            - regex:
                expression: '(?P<timestamp>\d+:\d+:\d+.\d+)\s+(?P<level>[\.\*\#\-])\s+(?P<message>.*)'
            - labels:
                level:
                timestamp:

      # PostgreSQL logs
      - match:
          selector: '{container=~".*postgres.*"}'
          stages:
            - regex:
                expression: '(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}.\d{3} [A-Z]{3})\s+\[(?P<pid>\d+)\]\s+(?P<level>[A-Z]+):\s+(?P<message>.*)'
            - labels:
                level:
                pid:
            - timestamp:
                source: timestamp
                format: '2006-01-02 15:04:05.000 MST'
