// src/auth/guards/permission.guard.ts
import {
  Injectable,
  CanActivate,
  ExecutionContext,
  Inject,
  ForbiddenException,
} from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { Reflector } from '@nestjs/core';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RolePermission } from '../entities/role-permission.entity';
import { PERMISSIONS_KEY } from '../decorators/permissions.decorator';
import { UserWorkspace } from 'src/users/entities/user-workspace.entity';
import { SystemPermissions } from '../../constants';
import { WorkerLogger } from 'src/shared/logger.service';

@Injectable()
export class PermissionGuard implements CanActivate {
  private readonly logger = new WorkerLogger(PermissionGuard.name);
  constructor(
    private reflector: Reflector,
    @InjectRepository(RolePermission)
    private rolePermissionRepository: Repository<RolePermission>,
    @InjectRepository(UserWorkspace)
    private userWorkspaceRepository: Repository<UserWorkspace>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermissions = this.reflector.get<string[]>(
      PERMISSIONS_KEY,
      context.getHandler()
    );

    if (!requiredPermissions?.length) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const { id: userId, workspaceId } = request.user;

    try {
      // Try to get permissions from cache
      const cacheKey = `permissions:${userId}:${workspaceId}`;
      let userPermissions =
        await this.cacheManager.get<SystemPermissions[]>(cacheKey);

      if (!userPermissions) {
        this.logger.debug('Cache miss, fetching permissions from database');

        const userWorkspace = await this.userWorkspaceRepository.findOne({
          where: {
            userId,
            workspaceId,
          },
          relations: ['role'],
        });

        if (!userWorkspace?.role?.id) {
          this.logger.warn(
            `No role found for user ${userId} in workspace ${workspaceId}`
          );
          return false;
        }

        const rolePermissions = await this.rolePermissionRepository.find({
          where: { roleId: userWorkspace.role.id },
          relations: ['permission'],
          select: {
            permission: {
              name: true,
            },
          },
        });

        userPermissions = rolePermissions.map(
          (rp) => rp.permission.name as SystemPermissions
        );

        // Store in cache
        await this.cacheManager.set(
          cacheKey,
          JSON.stringify(userPermissions),
          600
        );
        this.logger.debug(`Cached permissions for user ${userId}`);
      } else {
        userPermissions = JSON.parse(userPermissions as unknown as string);
      }

      // Check if user has all required permissions
      const hasPermissions = requiredPermissions.every(
        (permission: SystemPermissions) => userPermissions?.includes(permission)
      );

      if (!hasPermissions) {
        throw new ForbiddenException('Insufficient permissions');
      }

      return true;
    } catch (error) {
      this.logger.error(
        `Error checking permissions: ${error.message}`,
        error.stack
      );
      return false;
    }
  }
}
