import { useState } from 'react';

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface StatusOption<T> {
  value: T;
  label: string;
  icon?: React.FC<any>;
  color?: string;
}

interface StatusSelectorProps<T> {
  currentStatus: T;
  statuses: StatusOption<T>[];
  onStatusChange: (status: T) => void | Promise<void>;
  disabled?: boolean;
}

export function StatusSelector<T extends string>({
  currentStatus,
  statuses,
  onStatusChange,
  disabled = false,
}: StatusSelectorProps<T>) {
  const [isOpen, setIsOpen] = useState(false);
  const currentStatusData = statuses.find((s) => s.value === currentStatus);

  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span>
              {' '}
              {/* Wrapper needed because button is disabled */}
              <Button
                onClick={() => setIsOpen(true)}
                style={{ color: currentStatusData?.color }}
                variant="outline"
                disabled={disabled}
              >
                {currentStatusData?.label}
              </Button>
            </span>
          </TooltipTrigger>
          {disabled && (
            <TooltipContent>
              <p>You don't have permission to perform this action</p>
            </TooltipContent>
          )}
        </Tooltip>
      </TooltipProvider>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Change Status</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4">
            {statuses.map((status) => (
              <Button
                key={status.value}
                onClick={() => {
                  onStatusChange(status.value);
                  setIsOpen(false);
                }}
                className={cn('w-full justify-start')}
                style={{
                  color: status.color,
                  backgroundColor: `${status.value === currentStatus ? status.color + '1A' : ''}`,
                }}
                variant={'outline'}
              >
                {status.label}
              </Button>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
