import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { CronService } from './cron.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { DocumentModule } from 'src/document/document.module';
import { Document } from '../document/entities/document.entity';
import { JobProcessor } from 'src/types/jobs';
import { DEFAULT_QUEUE_JOB_OPTIONS } from 'src/util/queue.config';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    DocumentModule,
    TypeOrmModule.forFeature([Document]),
    BullModule.registerQueue(
      { 
        name: JobProcessor.ChunkExtraction,
        defaultJobOptions: DEFAULT_QUEUE_JOB_OPTIONS,
      },
      { 
        name: JobProcessor.ChunkDpLinking,
        defaultJobOptions: DEFAULT_QUEUE_JOB_OPTIONS,
      }
    ),
  ],
  providers: [CronService],
})
export class CronModule {}
