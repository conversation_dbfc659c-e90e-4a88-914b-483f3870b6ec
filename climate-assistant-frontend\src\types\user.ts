import { USER_ROLE } from '@/constants/workspaceConstants';

export enum USER_STATUS_VARIANTS {
  'ACTIVE' = 'ACTIVE',
  'INVITED' = 'INVITED',
  'INACTIVE' = 'INACTIVE',
}

export interface UserWorkspace {
  jointedAt: Date;
  role: {
    name: USER_ROLE;
  };
  workspaceId: string;
}

export interface IUser {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
  userWorkspaces: UserWorkspace[];
  role: USER_ROLE;
}

export interface IUserWithStatus extends IUser {
  status: USER_STATUS_VARIANTS;
}
