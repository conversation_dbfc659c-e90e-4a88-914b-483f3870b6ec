import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Request,
  Res,
  UseGuards,
} from '@nestjs/common';
import { ChatService } from './chat.service';
import { Response } from 'express';
import { HistoryCreationDto } from './entities/history-creation.dto';
import { ChatMessageDto, HistoryUpdateDto } from './entities/chat.message.dto';
import { InitiativeDetailService } from './initiative-detail.service';
import { CsrdReportingService } from './csrd-reporting.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AuthGuard } from 'src/auth/auth.guard';

@ApiTags('chats')
@UseGuards(AuthGuard)
@Controller('chats')
export class ChatController {
  constructor(
    private readonly chatService: ChatService,
    private readonly initiativeDetailService: InitiativeDetailService,
    private readonly csrdReportingService: CsrdReportingService,
  ) {}

  @Get('')
  @ApiOperation({ summary: 'Get all chat histories for the user' })
  @ApiResponse({
    status: 200,
    description: 'Chat histories retrieved successfully',
  })
  getChats(@Request() req) {
    const userId = req.user.id;
    return this.chatService.getChats(userId);
  }

  @Post('create-empty-history')
  @ApiOperation({ summary: 'Create an empty chat history' })
  @ApiResponse({
    status: 201,
    description: 'Empty chat history created successfully',
  })
  async createEmptyHistory(
    @Request() req,
    @Body() payload: HistoryCreationDto,
  ) {
    const userId = req.user.id;
    return this.chatService.createEmptyHistory(userId, payload.type);
  }

  @Post('send-message')
  @ApiOperation({ summary: 'Send a message in a chat history' })
  @ApiResponse({ status: 200, description: 'Message sent successfully' })
  async sendMessage(
    @Request() req,
    @Res() res: Response,
    @Body()
    payload: {
      historyId: string;
      messages: ChatMessageDto[];
      internalProcessRequest: {
        [key: string]: string;
      } | null;
    },
  ) {
    setSSEHeaders(res);

    const userId = req.user.id;
    const { messages, historyId, internalProcessRequest } = payload;
    if (internalProcessRequest?.key) {
      const systemPrompt = this.csrdReportingService.createCsrdReportingPrompt(
        internalProcessRequest,
      );
      messages.push(systemPrompt);
    }

    await this.chatService.addMessageToHistory(historyId, [...messages]);
    const content = await this.chatService.createMessageStream({
      messages,
      onMessage: (chunk) => {
        res.write(chunk);
      },
      userId,
    });

    await this.chatService.addMessageToHistory(historyId, [
      { role: 'assistant', content },
    ]);

    const history = await this.chatService.getChatHistory(historyId);
    if (history.title == null) {
      if (payload.internalProcessRequest?.key) {
        await this.chatService.storeTitle(
          historyId,
          payload.internalProcessRequest.key,
        );
      } else {
        await this.chatService.generateHistoryTitle(
          historyId,
          messages[messages.length - 1]?.content ?? '',
          content,
        );
      }
    }

    res.end();
  }

  @Post('vector-query')
  @ApiOperation({ summary: 'Query vectors for chat' })
  @ApiResponse({
    status: 200,
    description: 'Vector query executed successfully',
  })
  async getVectorQuery(@Request() req) {
    const userId = req.user.id;
    if (req.body.database === 'company') {
      return await this.chatService.queryCompanyRelatedVectors({
        userId,
        query: req.body.query,
        threshold: req.body.threshold,
        count: req.body.count,
      });
    } else {
      return await this.chatService.queryInternalRelatedVectors({
        query: req.body.query,
        threshold: req.body.threshold,
        count: req.body.count,
      });
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific chat history by ID' })
  @ApiResponse({
    status: 200,
    description: 'Chat history retrieved successfully',
  })
  getChat(@Param('id') id: string) {
    return this.chatService.getChatHistory(id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a specific chat history by ID' })
  @ApiResponse({
    status: 200,
    description: 'Chat history deleted successfully',
  })
  deleteChat(@Param('id') id: string) {
    return this.chatService.deleteChatHistory(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update the title of a chat history' })
  @ApiResponse({
    status: 200,
    description: 'Chat history title updated successfully',
  })
  updateHistoryTitle(
    @Param('id') id: string,
    @Body() updatedHistory: HistoryUpdateDto,
  ) {
    return this.chatService.updateChatHistory(id, updatedHistory);
  }
}

function setSSEHeaders(res: Response) {
  // These are needed in combination with nginx headers so that chunking is working
  res.header('Content-Type', 'text/event/stream');
  res.header('Cache-control', 'no-cache');
  res.header('X-Accel-Buffering', 'no');
}
