import { format } from 'date-fns';
import { FileClock } from 'lucide-react';
import { useState } from 'react';

import { TipTapEditor } from '../ui/tiptap/tiptap-editor';

import ConfirmDialog from '@/components/ConfirmDialog';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { IUser } from '@/types/user';

export interface VersionHistoryItem {
  id: string;
  event: string;
  versionData: {
    doneBy: string;
    data: any;
  };
  createdAt: string;
  ref?: string;
}

interface VersionHistoryModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  versionHistory: VersionHistoryItem[];
  users: Pick<IUser, 'id' | 'name'>[];
  handleRestoreVersion: (versionId: string) => Promise<void>;
  contentVersion?: string;
}

export function VersionHistoryModal({
  open,
  onOpenChange,
  versionHistory,
  users,
  handleRestoreVersion,
  contentVersion,
}: VersionHistoryModalProps) {
  const [selectedVersion, setSelectedVersion] =
    useState<VersionHistoryItem | null>(null);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);

  const handleVersionSelect = (
    version: VersionHistoryItem,
    action: 'view' | 'restore'
  ) => {
    setSelectedVersion(version);
    if (action === 'view') {
      setIsViewDialogOpen(true);
    } else {
      setIsConfirmDialogOpen(true);
    }
  };

  const handleRestore = async () => {
    if (!selectedVersion) return;
    await handleRestoreVersion(selectedVersion.id);
    setIsConfirmDialogOpen(false);
    setSelectedVersion(null);
  };

  const handleCloseDialogs = () => {
    setIsViewDialogOpen(false);
    setIsConfirmDialogOpen(false);
    setSelectedVersion(null);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-3xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>Version History</DialogTitle>
          </DialogHeader>
          <div className="h-[500px] w-full pr-4 overflow-y-auto">
            {versionHistory.length === 0 ? (
              <div className="h-full flex flex-col items-center justify-center text-muted-foreground">
                <FileClock className="h-12 w-12 mb-4" />
                <p className="text-lg font-medium">No version history</p>
                <p className="text-sm">
                  Changes to this datapoint will appear here
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {versionHistory.map((version) => {
                  const currentVersion = version.id === contentVersion;
                  return (
                    <div
                      key={version.id}
                      className={`flex items-center justify-between p-3 border rounded-lg ${
                        currentVersion ? 'bg-blue-50' : ''
                      }`}
                    >
                      <div className="flex items-center gap-4">
                        <span className="text-sm font-medium">
                          {users.find(
                            (user) => user.id === version.versionData.doneBy
                          )?.name || 'N/A'}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          {format(new Date(version.createdAt), 'PPpp')}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          disabled={
                            !version.versionData.data.content || currentVersion
                          }
                          onClick={() => handleVersionSelect(version, 'view')}
                        >
                          View
                        </Button>
                        <Button
                          variant="outline"
                          disabled={
                            !version.versionData.data.content || currentVersion
                          }
                          onClick={() =>
                            handleVersionSelect(version, 'restore')
                          }
                        >
                          Restore
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Version Content Modal */}
      <Dialog open={isViewDialogOpen} onOpenChange={handleCloseDialogs}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Version Details</DialogTitle>
          </DialogHeader>
          {selectedVersion && (
            <div className="space-y-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  <span className="font-medium">Time:</span>{' '}
                  {format(new Date(selectedVersion.createdAt), 'PPpp')}
                </p>
                <p className="text-sm text-muted-foreground">
                  <span className="font-medium">Modified by:</span>{' '}
                  {users.find(
                    (user) => user.id === selectedVersion.versionData.doneBy
                  )?.name || 'Unknown User'}
                </p>
              </div>
              <div className="border-t pt-4">
                <h3 className="text-lg font-semibold mb-4">Content</h3>
                <div className="prose prose-sm max-w-none overflow-y-auto max-h-[60vh]">
                  <TipTapEditor
                    content={selectedVersion.versionData.data.content}
                    isEditable={false}
                    setContent={() => {}}
                    shouldShowCitation={false}
                  />
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      <ConfirmDialog
        open={isConfirmDialogOpen}
        onClose={handleCloseDialogs}
        onConfirm={handleRestore}
        title="Confirm Restore"
        description="Are you sure you want to revert to this version? This will overwrite the current datapoint."
      />
    </>
  );
}
