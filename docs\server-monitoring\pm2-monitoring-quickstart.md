# PM2 Cluster Monitoring Quick Start Guide

## Overview

This guide provides quick steps to monitor the PM2 cluster deployment with 3 workers.

## Key Metrics & Endpoints

### Health Check Endpoints

```bash
# Check overall cluster health
curl http://localhost:3000/health/cluster

# Check individual worker metrics (requires port exposure)
curl http://localhost:3001/metrics  # Worker 0
curl http://localhost:3002/metrics  # Worker 1
curl http://localhost:3003/metrics  # Worker 2
```

### Grafana Dashboards

1. **PM2 Cluster Monitoring** (`http://localhost:3000/d/pm2-cluster`)
   - Worker health status
   - CPU & Memory per worker
   - Request distribution
   - Response times

2. **Backend Application Monitoring** (`http://localhost:3000/d/backend-app`)
   - Container-level metrics
   - Application logs
   - Error tracking

3. **Container Resource Monitoring** (`http://localhost:3000/d/container-resources`)
   - System resource usage
   - Network I/O
   - Disk usage

## Critical Alerts

### Immediate Action Required

| Alert | Description | Action |
|-------|-------------|--------|
| **WorkerDown** | Worker offline > 1 min | Check logs: `pm2 logs <worker_id>` |
| **AllWorkersDown** | Complete outage | Check container: `docker logs climate-assistant-backend-1` |
| **WorkerHighMemory** | Memory > 1.3GB | Restart worker: `pm2 restart <worker_id>` |
| **WorkerRestartLoop** | > 2 restarts in 5 min | Check error logs, review recent deployments |

### Warning Alerts

| Alert | Description | Investigation |
|-------|-------------|---------------|
| **LoadImbalance** | Uneven request distribution | Check long-running requests |
| **HighResponseTime** | P95 > 1s | Review slow queries, check CPU |
| **DatabaseConnectionsHigh** | > 7 connections/worker | Check for connection leaks |
| **QueueProcessingStuck** | No jobs processed | Check Redis, review queue errors |

## Quick Debugging Commands

### Inside Container

```bash
# Access container
docker exec -it climate-assistant-backend-1 sh

# PM2 status
pm2 status
pm2 monit
pm2 show climate-assistant-backend

# Worker-specific logs
pm2 logs 0 --lines 100  # Main worker
pm2 logs 1 --lines 100  # Worker 1
pm2 logs 2 --lines 100  # Worker 2

# Restart workers
pm2 restart 0  # Restart main worker
pm2 restart all  # Restart all workers
pm2 reload all  # Zero-downtime reload
```

### From Host

```bash
# Check worker distribution
for i in {1..10}; do
  curl -s http://localhost:3000/health/cluster | jq -r '.worker.id'
done | sort | uniq -c

# Monitor logs with worker ID
docker logs climate-assistant-backend-1 --follow | grep "Worker"

# Check database connections
docker exec climate-assistant-postgres-1 psql -U postgres -d climate_assistant -c \
  "SELECT count(*) as connections, state FROM pg_stat_activity GROUP BY state;"

# Redis queue status
docker exec climate-assistant-redis-1 redis-cli INFO | grep connected_clients
```

## Prometheus Queries

### Worker Health

```promql
# Worker status
up{job="backend-workers"}

# CPU usage by worker
rate(process_cpu_seconds_total{job="backend-workers"}[5m]) * 100

# Memory usage by worker
process_resident_memory_bytes{job="backend-workers"}

# Request rate by worker
sum(rate(http_requests_total{job="backend-workers"}[5m])) by (worker_id)
```

### Load Distribution

```promql
# Request distribution variance
stddev(rate(http_requests_total{job="backend-workers"}[5m])) / avg(rate(http_requests_total{job="backend-workers"}[5m]))

# Response time P95 by worker
histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="backend-workers"}[5m]))
```

### Database & Queues

```promql
# DB connections per worker
pg_stat_activity_count{state="active"} * on(instance) group_left(worker_id) up{job="backend-workers"}

# Queue jobs processed
sum(rate(queue_jobs_processed_total{job="backend-workers"}[5m])) by (worker_id, job_type)
```

## Loki Log Queries

### Error Investigation

```logql
# Errors by worker
{container=~".*backend.*"} |~ "error|exception" | regexp "Worker (?P<worker_id>\\d+)"

# Worker-specific logs
{container=~".*backend.*", worker_id="0"} |= "ERROR"

# Queue processing errors
{container=~".*backend.*"} |~ "queue.*error|failed.*job"

# Database connection errors
{container=~".*backend.*"} |~ "ECONNREFUSED|connection.*timeout|too many connections"
```

### Performance Analysis

```logql
# Slow queries
{container=~".*backend.*"} |~ "query.*slow|execution.*time" | regexp "duration: (?P<duration>\\d+)ms"

# Request timing
{container=~".*backend.*"} | regexp "HTTP.*(?P<status>\\d{3}).*(?P<duration>\\d+)ms"
```

## Grafana Alert Configuration

### Setting Up Notifications

1. **Configure Alert Channels**:
   - Navigate to Alerting → Notification channels
   - Add Slack/Email/PagerDuty integration

2. **Alert Routing**:
   - Critical alerts → PagerDuty
   - Warning alerts → Slack #backend-alerts
   - Info alerts → Grafana logs only

3. **Silence Alerts During Maintenance**:
   ```bash
   # Via Grafana UI
   Alerting → Silences → New Silence
   
   # Or via API
   curl -X POST http://localhost:3000/api/alertmanager/grafana/api/v2/silences \
     -H "Authorization: Bearer $GRAFANA_API_KEY" \
     -d '{"matchers":[{"name":"alertname","value":"WorkerRestarted"}],"startsAt":"2024-01-01T00:00:00Z","endsAt":"2024-01-01T01:00:00Z"}'
   ```

## Common Issues & Solutions

### Issue: Metrics Missing for Worker

```bash
# Check if worker is exposing metrics
docker exec climate-assistant-backend-1 wget -O- http://localhost:3001/metrics

# Verify Prometheus can reach worker
curl http://localhost:9090/api/v1/targets | jq '.data.activeTargets[] | select(.job == "backend-workers")'
```

### Issue: High Memory Usage

```bash
# Check memory details
docker exec climate-assistant-backend-1 pm2 show <worker_id> | grep -E "memory|heap"

# Force garbage collection (if enabled)
docker exec climate-assistant-backend-1 pm2 trigger climate-assistant-backend gc

# Take heap snapshot
docker exec climate-assistant-backend-1 pm2 heapdump <worker_id>
```

### Issue: Uneven Load Distribution

```bash
# Check PM2 load balancer mode
docker exec climate-assistant-backend-1 pm2 show climate-assistant-backend | grep exec_mode

# Monitor request distribution
watch -n 1 'curl -s http://localhost:3000/health/cluster | jq .worker.id'
```

## Performance Baselines

Expected values under normal load:

| Metric | Normal Range | Action Threshold |
|--------|--------------|------------------|
| CPU Usage | 20-40% | > 80% |
| Memory Usage | 400-800MB | > 1.2GB |
| Response Time P95 | 100-300ms | > 1s |
| DB Connections | 2-5 per worker | > 7 per worker |
| Queue Processing | 10-50 jobs/min | < 5 jobs/min |
| Error Rate | < 0.1% | > 1% |

## Maintenance Tasks

### Daily
- Review error logs
- Check memory trends
- Verify cron job execution

### Weekly
- Analyze performance trends
- Review alert fatigue
- Update baseline metrics

### Monthly
- Capacity planning review
- Alert threshold tuning
- Database connection pool optimization 