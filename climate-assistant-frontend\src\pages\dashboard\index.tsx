import { Settings2Icon } from 'lucide-react';
import { FunctionComponent } from 'react';
import { Link } from 'react-router-dom';

import {
  dashboardColumns,
  dataRequestStatuses,
  esrsStandards,
} from '@/components/DashboardTableConfig';

import { DataTable } from '@/components/data-table/DataTable';
import { DataTableFacetedFilter } from '@/components/data-table/DataTableFacetedFilter';
import { MainLayout } from '@/components/MainLayout';
import { buttonVariants } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { TABLE_TYPES } from '@/constants/table-constants';
import { useDashboard } from '@/hooks/useDashboard';
import { ExportGapsDropdown } from '@/components/dashboard/ExportGapsDropdown';

export const Dashboard: FunctionComponent = () => {
  const { dataRequests, project, loading, progress, totalDRs } = useDashboard();

  return (
    <MainLayout>
      <div
        className={`flex flex-col items-center flex-1 justify-center pb-60 max-w-[90vw]`}
      >
        <div className="flex justify-between w-full items-center mt-8 mb-6">
          <div className="flex gap-4 w-full items-center">
            <h1 className={`text-4xl font-bold`}>
              {project?.name || 'Dashboard'}
            </h1>
          </div>
          <div className="flex gap-3">
            <Link
              to="/dashboard/materiality-settings"
              className={buttonVariants({
                variant: 'outline',
                className: '!rounded-full px-4 flex items-center',
              })}
            >
              <Settings2Icon className="w-4 h-4 mr-2" />
              Material Topics
            </Link>
            {project && <ExportGapsDropdown project={project} />}
          </div>
        </div>
        {totalDRs > 0 && (
          <div className="flex items-center gap-5 w-full mb-8">
            <Progress value={(progress / totalDRs) * 100} className="" />
            <span className="min-w-fit">{`${progress} / ${totalDRs}`}</span>
          </div>
        )}
        <DataTable
          data={dataRequests}
          columns={dashboardColumns}
          loading={loading}
          tableId={TABLE_TYPES.DASHBOARD}
          columnActions={[
            {
              columnName: 'status',
              actions: (column) => (
                <DataTableFacetedFilter
                  column={column}
                  title="Status"
                  options={dataRequestStatuses}
                />
              ),
            },
            {
              columnName: 'esrs',
              actions: (column) => (
                <DataTableFacetedFilter
                  column={column}
                  title="ESRS"
                  options={esrsStandards}
                />
              ),
            },
            // This is a hidden filter for proper state cleanup on reset
            {
              columnName: 'name',
              actions: (column) => (
                <DataTableFacetedFilter
                  column={column}
                  title="Name"
                  options={esrsStandards}
                  hidden
                />
              ),
            },
          ]}
        />
      </div>
    </MainLayout>
  );
};
