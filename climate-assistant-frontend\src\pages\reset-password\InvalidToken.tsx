import { useState } from 'react';
import { LoaderCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

import { Input } from '@/components/ui/input.tsx';
import { Label } from '@/components/ui/label.tsx';
import { Button } from '@/components/ui/button.tsx';
import { useAuthentication } from '@/api/authentication/authentication.query.ts';

const InvalidToken = () => {
  const [email, setEmail] = useState('');
  const navigate = useNavigate();
  const { sendPasswordResetEmail, sendPasswordResetEmailLoading } =
    useAuthentication();
  return (
    <div className={`flex flex-col w-full max-w-[380px]`}>
      <div className={`font-semibold text-3xl text-center mb-8`}>
        Password Reset Token Expired.
        <p className="text-xl text-gray-400">
          To reset your password, send a new one
        </p>
      </div>
      <div className="mb-4">
        <Label htmlFor="email">Email</Label>
        <Input
          className="mt-2"
          id="email"
          type="email"
          value={email}
          placeholder={`Email`}
          onChange={(e) => setEmail(e.target.value)}
        ></Input>
      </div>

      <div className="text-center">
        <Button
          disabled={sendPasswordResetEmailLoading}
          variant="default"
          onClick={() => sendPasswordResetEmail(email)}
          className="mr-3 w-full rounded-3xl"
          style={{ backgroundColor: '#143560' }}
        >
          {sendPasswordResetEmailLoading && (
            <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
          )}
          Send password reset link
        </Button>
      </div>
      <div className="text-center">
        <Button
          variant="link"
          onClick={() => {
            navigate('/login');
          }}
          className="text-sm"
        >
          Back to Log in
        </Button>
      </div>
    </div>
  );
};

export default InvalidToken;
