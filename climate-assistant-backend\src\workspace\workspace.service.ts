import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Workspace } from './entities/workspace.entity';
import { UserWorkspace } from '../users/entities/user-workspace.entity';
import { User } from '../users/entities/user.entity';
import { Token, TokenType } from '../users/entities/token.entity';
import { EmailService } from '../external/email.service';
import {
  IVersionHistory,
  VersionHistory,
} from './entities/version-history.entity';
import { Company } from './entities/company.entity';
import { USER_ROLES } from 'src/constants';
import { Role } from '../users/entities/role.entity';

@Injectable()
export class WorkspaceService {
  constructor(
    @InjectRepository(Workspace)
    private readonly workspaceRepository: Repository<Workspace>,
    @InjectRepository(Company)
    private readonly companyRepository: Repository<Company>,
    @InjectRepository(UserWorkspace)
    private readonly userWorkspaceRepository: Repository<UserWorkspace>,
    @InjectRepository(Token)
    private tokenRepository: Repository<Token>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(VersionHistory)
    private readonly versionHistoryRepository: Repository<VersionHistory>,
    private emailService: EmailService,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>
  ) {}

  async findById(id: Workspace['id']): Promise<Workspace | undefined> {
    return this.workspaceRepository.findOne({
      where: { id },
      relations: ['companies'],
    });
  }

  async updateById(id: string, updates: Partial<Workspace>) {
    const workspace = await this.workspaceRepository.findOneBy({ id });

    if (!workspace) {
      throw new Error('Workspace not found');
    }

    // Update the workspace fields
    Object.assign(workspace, updates);

    // Save the updated workspace
    return this.workspaceRepository.save(workspace);
  }

  async getUsersByWorkspace(workspaceId: string): Promise<User[]> {
    return this.userWorkspaceRepository
      .find({
        where: { workspaceId },
        relations: ['user', 'user.tokens', 'role'],
      })
      .then((userWorkspaces) =>
        userWorkspaces.map((uw) => {
          let status = 'INACTIVE';
          if (!!this.isUserActive(uw.user)) {
            status = 'ACTIVE';
          } else {
            const token = uw.user.tokens?.at(-1); //get the last token
            if (token && token.type === TokenType.WorkspaceInvite) {
              status = 'INVITED';
            }
          }
          delete uw.user.password;
          delete uw.user.tokens;
          return {
            ...uw.user,
            role: uw.role.name,
            status,
          };
        })
      );
  }

  async addUserToWorkspace({
    workspaceId,
    userId,
    role,
  }: {
    workspaceId: string;
    userId: string;
    role: USER_ROLES;
  }) {
    const roleEntity = await this.findRoleByName(role);

    if (!roleEntity) {
      throw new Error(`Role ${role} not found`);
    }

    const userWorkspace = this.userWorkspaceRepository.create({
      workspaceId,
      userId,
      roleId: roleEntity.id,
      joinedAt: new Date(),
    });
    return this.userWorkspaceRepository.save(userWorkspace);
  }

  async getUserWorkspace({
    workspaceId,
    userId,
  }: {
    workspaceId: string;
    userId: string;
  }): Promise<UserWorkspace | undefined> {
    return this.userWorkspaceRepository.findOne({
      where: { workspaceId, userId },
    });
  }

  async removeUserFromWorkspace({
    workspaceId,
    userId,
  }: {
    workspaceId: string;
    userId: string;
  }): Promise<void> {
    await this.userWorkspaceRepository.delete({ workspaceId, userId });
  }

  async inviteUserToWorkspace({
    inviteeEmail,
    origin,
    workspaceId,
    email,
    role,
    shouldSendEmail = true,
  }: {
    inviteeEmail: string;
    origin: string;
    workspaceId: string;
    email: string;
    role: USER_ROLES;
    shouldSendEmail: boolean;
  }) {
    let user = await this.userRepository.findOne({
      where: { email },
      relations: ['userWorkspaces'],
    });
    const invitingUser = await this.userRepository.findOne({
      where: { email: inviteeEmail },
    });
    if (!user) {
      user = this.userRepository.create({ email });
      await this.userRepository.save(user);
    }
    if (this.isUserActive(user)) {
      throw new Error(`${email} is already an active user in the workspace`);
    }

    const roleEntity = await this.findRoleByName(role);

    if (!user.userWorkspaces) {
      const userWorkspace = this.userWorkspaceRepository.create({
        workspaceId,
        userId: user.id,
        roleId: roleEntity.id,
      });
      await this.userWorkspaceRepository.save(userWorkspace);
    }

    const inviteToken = crypto.randomUUID();

    const store = await this.tokenRepository.create({
      token: inviteToken,
      user,
      type: TokenType.WorkspaceInvite,
      expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 3),
    });

    await this.tokenRepository.save(store);

    if (shouldSendEmail) {
      await this.emailService.inviteUser({
        token: inviteToken,
        invitingUser,
        email,
        origin,
      });
    }

    return user;
  }

  async acceptInvitation({
    workspaceId,
    userId,
  }: {
    workspaceId: string;
    userId: string;
  }) {
    const userWorkspace = await this.getUserWorkspace({ workspaceId, userId });
    if (userWorkspace) {
      const joinedAt = new Date();
      await this.userWorkspaceRepository.update(userWorkspace, { joinedAt });
      return userWorkspace;
    }
    throw new Error('Invitation not found');
  }

  async storeActionHistory({
    workspaceId,
    event,
    ref,
    versionData,
  }: {
    workspaceId: string;
    event: string;
    ref: string;
    versionData: IVersionHistory;
  }): Promise<string> {
    const history = this.versionHistoryRepository.create({
      workspaceId,
      event,
      ref,
      versionData,
    });
    const savedHistory = await this.versionHistoryRepository.save(history);
    return savedHistory.id;
  }

  async canInviteUser(invitedRole: USER_ROLES, user: User) {
    const userWorkspace = await this.userWorkspaceRepository.findOne({
      where: { userId: user.id },
      relations: ['role'],
    });
    const currentUserRole = userWorkspace?.role?.name;
    if (currentUserRole === USER_ROLES.SuperAdmin) return true;
    if (
      currentUserRole === USER_ROLES.AiContributor &&
      [USER_ROLES.WorkspaceAdmin, USER_ROLES.Contributor].includes(invitedRole)
    ) {
      return true;
    }
    if (
      currentUserRole === USER_ROLES.WorkspaceAdmin &&
      [USER_ROLES.WorkspaceAdmin, USER_ROLES.Contributor].includes(invitedRole)
    ) {
      return true;
    }
    return false;
  }

  isUserActive(user: User) {
    return !!user.password;
  }

  async getAllWorkspaces(): Promise<Workspace[]> {
    return this.workspaceRepository.find({
      // where: {
      //   createdAt: MoreThanOrEqual(new Date('2025-01-01')),
      // },
      order: {
        name: 'ASC',
      },
    });
  }

  async getCompanyDetailFromWorkspaceId(workspaceId: string) {
    return this.companyRepository.findOne({
      where: { workspaceId },
    });
  }

  async updateCompanyDetail(
    workspaceId: string,
    companyDetail: Company
  ): Promise<Company> {
    const company = await this.companyRepository.findOne({
      where: { workspaceId },
    });
    if (!company) {
      throw new Error(`Company not found for workspaceId: ${workspaceId}`);
    }
    return this.companyRepository.save({ id: company.id, ...companyDetail });
  }

  async getVersionHistoryByVersionId({
    workspaceId,
    versionId,
  }: {
    workspaceId: string;
    versionId: string;
  }): Promise<VersionHistory | undefined> {
    return this.versionHistoryRepository.findOne({
      where: { id: versionId, workspaceId },
    });
  }

  async getVersionHistoryByRefId(
    workspaceId: string,
    id: string
  ): Promise<VersionHistory[]> {
    return await this.versionHistoryRepository
      .createQueryBuilder('history')
      .where({
        ref: id,
        workspaceId,
        event: In(['data_request_updated', 'datapoint_request_updated']),
      })
      .orderBy('history.createdAt', 'DESC')
      .getMany();
  }

  async findRoleByName(role: USER_ROLES): Promise<Role> {
    const roleEntity = await this.roleRepository.findOne({
      where: { name: role },
    });
    if (!roleEntity) {
      throw new Error(`Role ${role} not found`);
    }
    return roleEntity;
  }
}
