import {
  IsEnum,
  IsString,
  IsObject,
  IsOptional,
  IsBoolean,
  IsNotEmpty,
  Matches,
  IsUUID,
} from 'class-validator';
import { LLM_MODELS } from 'src/constants';

export class CreatePromptDto {
  @IsString()
  @IsNotEmpty()
  feature: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^[A-Z]\d+$/, {
    message: 'Chain identifier must be in format like A1, A2, B1, etc.',
  })
  chainIdentifier: string;

  @IsString()
  @IsNotEmpty()
  prompt: string;

  @IsEnum(LLM_MODELS)
  @IsNotEmpty()
  model: LLM_MODELS;

  @IsObject()
  @IsOptional()
  requiredVariables?: Record<string, string>;

  @IsString()
  @IsNotEmpty()
  endpoint: string;

  @IsString()
  @IsOptional()
  description?: string;
}

export class UpdatePromptDto {
  @IsString()
  @IsOptional()
  prompt?: string;

  @IsEnum(LLM_MODELS)
  @IsOptional()
  model?: LLM_MODELS;

  @IsObject()
  @IsOptional()
  requiredVariables?: Record<string, string>;

  @IsString()
  @IsOptional()
  endpoint?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsString()
  @IsOptional()
  comment?: string;
}

export class TestPromptDto {
  @IsUUID()
  datapointRequestId: string;

  @IsObject()
  prompts: Record<
    string,
    { prompt: string; chainIdentifier: string; model: LLM_MODELS }
  >;

  @IsString()
  feature: string;

  @IsBoolean()
  @IsOptional()
  useExistingReportTextForReference?: boolean;

  @IsObject()
  @IsOptional()
  variables?: Record<string, any>;
}

export class TestGapAnalysisDto {
  @IsUUID()
  datapointRequestId: string;

  @IsObject()
  prompts: Record<
    string,
    { prompt: string; chainIdentifier: string; model: LLM_MODELS }
  >;

  @IsString()
  feature: string;

  @IsObject()
  @IsOptional()
  variables?: Record<string, any>;
}
