module.exports = {
  apps: [
    {
      name: 'climate-assistant-backend',
      script: './dist/main.js',
      // Allocate 3 CPUs in production using cluster mode
      instances: 3,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'development',
        NODE_OPTIONS: '--max-old-space-size=2048',
      },
      env_production: {
        NODE_ENV: 'production',
        NODE_OPTIONS: '--max-old-space-size=5120',
      },
      // Memory management
      max_memory_restart: '1500M',

      // Logging configuration
      error_file: './logs/err.log',
      out_file: './logs/out.log',
      log_file: './logs/combined.log',
      merge_logs: true,
      time: false,

      // Process management
      autorestart: true,
      max_restarts: 10,
      min_uptime: '10s',
      restart_delay: 4000,

      // Development features
      watch: false,
      ignore_watch: ['node_modules', 'logs', '.git', 'dist'],

      // Graceful shutdown
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000,

      // Cluster settings
      instance_var: 'INSTANCE_ID',

      // Additional production optimizations
      node_args:
        '--optimize_for_size --max_old_space_size=5120 --gc_interval=100',

      // Health check
      cron_restart: '0 3 * * *', // Daily restart at 3 AM to prevent memory leaks
    },
  ],
};
