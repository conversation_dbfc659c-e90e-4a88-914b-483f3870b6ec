## 🔧 Step-by-Step Installation Guide 

### 1. Update System Packages

```bash
sudo apt update && sudo apt upgrade -y
```

### 2. Install Git

```bash
sudo apt install git -y
git --version
```

### 3. Install Docker Engine (Latest Version)

Remove any old Docker packages to avoid conflicts:

```bash
for pkg in docker.io docker-doc docker-compose docker-compose-v2 podman-docker containerd runc; do
  sudo apt remove -y $pkg
done
sudo apt autoremove -y
```

Set up Docker's official repository:

```bash
sudo apt install -y ca-certificates curl gnupg lsb-release
sudo mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | \
  sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] \
  https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt update
```

Install Docker Engine and related components:

```bash
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
```

Verify Docker installation:

```bash
docker --version
```

To manage Docker as a non-root user:

```bash
sudo usermod -aG docker $USER
newgrp docker
```

### 4. Install Docker Compose (v2 Plugin)

Docker Compose v2 is included as a plugin with Docker Engine. Verify its installation:

```bash
docker compose version
```

If you prefer the standalone Docker Compose (v1), you can install it manually:

```bash
sudo curl -L "https://github.com/docker/compose/releases/download/v2.3.3/docker-compose-$(uname -s)-$(uname -m)" \
  -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
docker-compose --version
```

*Note: Replace `v2.3.3` with the desired version number.*

---

## 🚀 Deploying Docker Compose Projects on GCP

Once Docker and Docker Compose are set up, you can deploy your applications:

1. **Create a `docker-compose.yml` file** defining your services.

2. **Start your application**:

   ```bash
   docker compose up -d
   ```

3. **Verify running containers**:

   ```bash
   docker compose ps
   ```


