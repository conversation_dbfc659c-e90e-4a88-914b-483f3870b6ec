# WorkerLogger Documentation

## Overview

`WorkerLogger` is a custom extension of NestJS's built-in `Logger` class that provides worker-aware logging. Our backend application runs in a cluster of 3 instances of this nestjs app - each instance called a worker. This is managed by PM2. Each log message is automatically prefixed with a worker-specific identifier, making it easier to trace logs back to the instance or process that emitted them. This is especially useful in clustered or multi-process environments like PM2.

---

## Location

```ts
src/shared/logger.service.ts
```

---

## Key Features

### Unique Worker Identifier

* Uses `INSTANCE_ID` if provided by the environment (e.g. via PM2)
* Falls back to `process.pid` when `INSTANCE_ID` is undefined

### Consistent Log Prefixing

* Prefixes all log levels (`log`, `warn`, `error`, `debug`, `verbose`) with `[worker-<id>]` or `[pid-<pid>]`

### Drop-in Replacement for NestJS Logger

* All methods available in NestJS Logger are overridden to include prefixing logic
* No change needed in consuming code other than replacing the `Logger` import

---

## Usage

### 1. Import in Service

```ts
import { WorkerLogger } from 'src/shared/logger.service';
```

### 2. Initialize Logger

```ts
private readonly logger = new WorkerLogger(MyService.name);
```

This sets the log context to the class name and ensures every log from that service is prefixed with its worker identity.

---

## Integration with PM2

In cluster mode, PM2 automatically sets a unique instance ID for each forked process. Use this ID for `INSTANCE_ID` to help distinguish logs between workers.

```env
INSTANCE_ID=$PM2_INSTANCE_ID
```

PM2 automatically injects an INSTANCE_ID for each instance, ensuring each logger has a distinct tag.

---

## Code Summary

### Class Definition

```ts
export class WorkerLogger extends Logger {
  private workerId: string;

  constructor(context?: string) {
    super(context);
    this.workerId = this.getWorkerId();
  }
```

### Worker ID Generation

```ts
private getWorkerId(): string {
  const instanceId = process.env.INSTANCE_ID;
  const pid = process.pid;

  return instanceId !== undefined ? `worker-${instanceId}` : `pid-${pid}`;
}
```

### Format Message with Prefix

```ts
private formatMessage(message: any): string {
  const prefix = `[${this.workerId}]`;
  return typeof message === 'string'
    ? `${prefix} ${message}`
    : `${prefix} ${JSON.stringify(message)}`;
}
```

---

## Comparison: Logger vs WorkerLogger

| Feature               | Logger          | WorkerLogger    |
| --------------------- | --------------- | --------------- |
| Supports Worker ID    | ❌ No            | ✅ Yes           |
| Drop-in Compatibility | ✅ Yes           | ✅ Yes           |
| Log Formatting        | Basic           | Worker-aware    |
| Production Visibility | ❌ Hard to trace | ✅ Easy to trace |

---

## Best Practices

* ✅ Replace all instances of `new Logger(...)` with `new WorkerLogger(...)`
* ✅ Pass class context as `ClassName.name`
* ✅ Use in services, queues, processors, and controllers for unified logs

---

## Troubleshooting

### Missing Worker ID in Logs

* Ensure `INSTANCE_ID` is set in your environment or container if using PM2
* Verify the `WorkerLogger` is used instead of the default `Logger`

### Log Prefix Not Appearing

* Check if `formatMessage` is being called in overridden methods
* Ensure no old `Logger` instances remain in your services

---

## Summary

The `WorkerLogger` provides a reliable and structured way to trace logs across multiple worker processes. It improves visibility, simplifies debugging, and is especially helpful in PM2 and multi-instance environments. Replacing the base `Logger` with `WorkerLogger` throughout your services is a small but high-leverage change to improve observability in production systems.
