import { encoding_for_model } from '@dqbd/tiktoken';

/**
 * Utility functions for document parsing
 */
export class DocumentParsingUtils {
  private static tokenizer = encoding_for_model('gpt-3.5-turbo');

  /**
   * Count tokens in text using tiktoken
   */
  static countTokens(text: string): number {
    return this.tokenizer.encode(text).length;
  }

  /**
   * Escape pipe characters and newlines in table cells
   */
  static escapeCell(cell: any): string {
    let cellStr = String(cell);

    // Escape pipe characters
    cellStr = cellStr.replace(/\|/g, '\\|');

    // Replace newline characters with a space
    cellStr = cellStr.replace(/\r?\n|\r/g, ' ');

    // Trim whitespace
    cellStr = cellStr.trim();

    return cellStr;
  }

  /**
   * Check if a value is likely a date
   */
  static isLikelyDate(value: any): boolean {
    if (typeof value !== 'string') return false;

    // Check common date formats
    const datePatterns = [
      /^\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4}$/, // MM/DD/YYYY, DD/MM/YYYY
      /^\d{4}[-\/]\d{1,2}[-\/]\d{1,2}$/, // YYYY/MM/DD
      /^\d{1,2}[-\/][A-Za-z]{3,9}[-\/]\d{2,4}$/, // DD-Mon-YYYY
      /^[A-Za-z]{3,9}[-\/]\d{1,2}[-\/]\d{2,4}$/, // Mon-DD-YYYY
    ];

    for (const pattern of datePatterns) {
      if (pattern.test(value)) return true;
    }

    // Try to parse as date
    const date = new Date(value);
    return !isNaN(date.getTime());
  }

  /**
   * Get the data type of a cell
   */
  static getCellDataType(cell: any): string {
    if (cell === undefined || cell === null || String(cell).trim() === '') {
      return 'empty';
    }

    if (typeof cell === 'number' || !isNaN(Number(cell))) {
      return 'number';
    }

    if (cell instanceof Date || this.isLikelyDate(cell)) {
      return 'date';
    }

    if (typeof cell === 'boolean') {
      return 'boolean';
    }

    // String type - could be further refined if needed
    return 'text';
  }

  /**
   * Generate page number metadata from a set of page numbers
   */
  static generatePageNumberMetadata(pageNumbers: Set<number>): string {
    const pageNumbersArray = Array.from(pageNumbers).sort((a, b) => a - b);

    if (pageNumbersArray.length === 1) {
      return `${pageNumbersArray[0]}`;
    }

    const isConsecutive = pageNumbersArray.every(
      (num, idx) => idx === 0 || num === pageNumbersArray[idx - 1] + 1
    );

    if (isConsecutive) {
      return `${pageNumbersArray[0]}-${pageNumbersArray[pageNumbersArray.length - 1]}`;
    }

    return pageNumbersArray.join(',');
  }

  /**
   * Calculate how similar a row is to being a header row (0-1 score)
   */
  static calculateHeaderLikelihood(row: any[]): number {
    if (!row || row.length === 0) return 0;

    let textCells = 0;
    let nonEmptyCells = 0;
    let shortTextCells = 0;

    for (const cell of row) {
      if (cell !== undefined && cell !== null && String(cell).trim() !== '') {
        nonEmptyCells++;

        if (typeof cell === 'string') {
          textCells++;

          // Headers often have shorter text
          if (String(cell).length < 50) {
            shortTextCells++;
          }
        }
      }
    }

    if (nonEmptyCells === 0) return 0;

    // Headers typically have:
    // 1. High proportion of text cells
    // 2. High proportion of short text cells
    // 3. Good overall cell coverage

    const textRatio = textCells / nonEmptyCells;
    const shortTextRatio = shortTextCells / nonEmptyCells;
    const coverageRatio = nonEmptyCells / row.length;

    return textRatio * 0.4 + shortTextRatio * 0.4 + coverageRatio * 0.2;
  }

  /**
   * Calculate how similar a row is to being a data row (0-1 score)
   */
  static calculateDataRowLikelihood(row: any[]): number {
    if (!row || row.length === 0) return 0;

    let numericCells = 0;
    let dateCells = 0;
    let nonEmptyCells = 0;

    for (const cell of row) {
      if (cell !== undefined && cell !== null && String(cell).trim() !== '') {
        nonEmptyCells++;

        if (typeof cell === 'number' || !isNaN(Number(cell))) {
          numericCells++;
        } else if (cell instanceof Date || this.isLikelyDate(cell)) {
          dateCells++;
        }
      }
    }

    if (nonEmptyCells === 0) return 0;

    // Data rows often have:
    // 1. More numeric/date values
    // 2. More varied cell types

    const numericRatio = (numericCells + dateCells) / nonEmptyCells;
    const coverageRatio = nonEmptyCells / row.length;

    return numericRatio * 0.6 + coverageRatio * 0.4;
  }

  /**
   * Calculate similarity between row patterns (0-1 score)
   */
  static calculateRowPatternSimilarity(rowA: any[], rowB: any[]): number {
    if (!rowA || !rowB || rowA.length !== rowB.length) return 0;

    let matchingCellTypes = 0;
    let cellsCompared = 0;

    for (let c = 0; c < rowA.length; c++) {
      const cellA = rowA[c];
      const cellB = rowB[c];

      const typeA = this.getCellDataType(cellA);
      const typeB = this.getCellDataType(cellB);

      // Only compare non-empty cells
      if (typeA !== 'empty' || typeB !== 'empty') {
        cellsCompared++;

        if (typeA === typeB) {
          matchingCellTypes++;
        }
      }
    }

    return cellsCompared > 0 ? matchingCellTypes / cellsCompared : 0;
  }

  /**
   * Calculate data density for the sheet
   */
  static calculateDataDensity(data: any[][]): number {
    if (data.length === 0) return 0;

    const rowCount = data.length;
    const colCount = data[0].length;
    let nonEmptyCells = 0;
    const totalCells = rowCount * colCount;

    for (let r = 0; r < rowCount; r++) {
      for (let c = 0; c < colCount; c++) {
        const value = data[r][c];
        if (
          value !== undefined &&
          value !== null &&
          String(value).trim() !== ''
        ) {
          nonEmptyCells++;
        }
      }
    }

    return nonEmptyCells / totalCells;
  }

  /**
   * Parse Excel cell reference (e.g., 'Sheet1!$A$1:$D$10') into row/column indices
   */
  static parseExcelReference(ref: string): {
    s: { r: number; c: number };
    e: { r: number; c: number };
  } | null {
    // Import XLSX only when needed to avoid circular dependencies
    const XLSX = require('xlsx');

    // Extract just the range part (remove sheet name if present)
    const rangePart = ref.includes('!') ? ref.split('!')[1] : ref;

    // Remove any $ signs (absolute references)
    const cleanRange = rangePart.replace(/\$/g, '');

    // Split into start and end references
    const [start, end] = cleanRange.split(':');

    if (!start || !end) return null;

    try {
      // Use XLSX utility to decode cell references
      const s = XLSX.utils.decode_cell(start);
      const e = XLSX.utils.decode_cell(end);

      return { s, e };
    } catch (error) {
      console.warn(`Invalid cell reference: ${ref}`);
      return null;
    }
  }

  /**
   * Calculate horizontal overlap between two regions
   */
  static calculateHorizontalOverlap(regionA: any, regionB: any): number {
    const aLeft = regionA.startCol;
    const aRight = regionA.startCol + regionA.width;
    const bLeft = regionB.startCol;
    const bRight = regionB.startCol + regionB.width;

    // Calculate overlap width
    const overlapStart = Math.max(aLeft, bLeft);
    const overlapEnd = Math.min(aRight, bRight);
    const overlapWidth = Math.max(0, overlapEnd - overlapStart);

    // Calculate total span
    const aWidth = aRight - aLeft;
    const bWidth = bRight - bLeft;
    const totalWidth = Math.max(aWidth, bWidth);

    return totalWidth > 0 ? overlapWidth / totalWidth : 0;
  }

  /**
   * Analyze column type consistency after headers
   */
  static analyzeColumnTypeConsistency(
    data: any[][],
    headerRows: number
  ): number {
    if (data.length <= headerRows) return 0;

    const colCount = data[0].length;
    let totalConsistency = 0;
    let columnsAnalyzed = 0;

    for (let c = 0; c < colCount; c++) {
      // Skip columns that are completely empty
      let hasData = false;

      for (let r = headerRows; r < data.length; r++) {
        if (DocumentParsingUtils.getCellDataType(data[r][c]) !== 'empty') {
          hasData = true;
          break;
        }
      }

      if (!hasData) continue;

      // Analyze data type consistency for this column
      const typeCounts: Record<string, number> = {};
      let totalCells = 0;

      for (let r = headerRows; r < data.length; r++) {
        const cellType = DocumentParsingUtils.getCellDataType(data[r][c]);

        if (cellType !== 'empty') {
          typeCounts[cellType] = (typeCounts[cellType] || 0) + 1;
          totalCells++;
        }
      }

      // Find most common type
      let maxTypeCount = 0;
      for (const count of Object.values(typeCounts)) {
        maxTypeCount = Math.max(maxTypeCount, count);
      }

      // Calculate consistency score for this column
      const columnConsistency = totalCells > 0 ? maxTypeCount / totalCells : 0;

      totalConsistency += columnConsistency;
      columnsAnalyzed++;
    }

    return columnsAnalyzed > 0 ? totalConsistency / columnsAnalyzed : 0;
  }
}
