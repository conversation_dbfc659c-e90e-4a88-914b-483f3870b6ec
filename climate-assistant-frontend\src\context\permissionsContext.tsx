import {
  createContext,
  useContext,
  useState,
  useCallback,
  useMemo,
} from 'react';

import { SystemPermissions } from '@/constants/userRoleConstants';

interface PermissionsContextProps {
  permissions: SystemPermissions[];
  setPermissions: (permissions: SystemPermissions[]) => void;
  hasPermission: (permission: SystemPermissions) => boolean;
  userPermissions: {
    canViewVersionHistory: boolean;
    canPerformAiGenerateOrReviewOnDr: boolean;
    canUpdateAiGenerationStatusOnDr: boolean;
    canInviteUser: boolean;
    canUpdateProject: boolean;
    canAssignDr: boolean;
    canPerformBulkOperationsOnDr: boolean;
    canEditDr: boolean;
    canEditDp: boolean;
    canApproveDp: boolean;
    canApproveDr: boolean;
    canAddComments: boolean;
    canEditComments: boolean;
    canDownloadDocuments: boolean;
    hasSuperAdminPermission: boolean;
    canPerformAiGenerateOrReviewOnDp: boolean;
    canUpdateAiGenerationStatusOnDp: boolean;
    canViewWorkspaceSettings: boolean;
  };
}

const PermissionsContext = createContext<PermissionsContextProps | undefined>(
  undefined
);

type PermissionMapping = {
  [K in keyof PermissionsContextProps['userPermissions']]: SystemPermissions;
};

const PERMISSION_MAPPING: PermissionMapping = {
  canViewVersionHistory: SystemPermissions.ACCESS_VERSION_HISTORY,
  canPerformAiGenerateOrReviewOnDr: SystemPermissions.GAP_ANALYSIS_DRS,
  canUpdateAiGenerationStatusOnDr: SystemPermissions.GAP_ANALYSIS_DRS_REVIEW,
  canInviteUser: SystemPermissions.INVITE_USERS,
  canUpdateProject: SystemPermissions.EDIT_PROJECT_SETTINGS,
  canAssignDr: SystemPermissions.ASSIGN_USERS_DRS,
  canPerformBulkOperationsOnDr: SystemPermissions.GAP_ANALYSIS_DRS_REVIEW,
  canEditDr: SystemPermissions.EDIT_DRS,
  canEditDp: SystemPermissions.EDIT_DATAPOINTS,
  canApproveDp: SystemPermissions.APPROVE_DATAPOINTS,
  canApproveDr: SystemPermissions.APPROVE_DRS,
  canAddComments: SystemPermissions.CREATE_COMMENTS,
  canEditComments: SystemPermissions.EDIT_COMMENTS,
  canDownloadDocuments: SystemPermissions.EXPORT_FINAL_REPORT, //TODO: check if we need another permission for this
  hasSuperAdminPermission: SystemPermissions.SWITCH_WORKSPACE,
  canPerformAiGenerateOrReviewOnDp: SystemPermissions.GAP_ANALYSIS_DATAPOINTS,
  canViewWorkspaceSettings: SystemPermissions.EDIT_WORKSPACE_SETTINGS,
  canUpdateAiGenerationStatusOnDp:
    SystemPermissions.GAP_ANALYSIS_DATAPOINTS_REVIEW,
};

export const PermissionsProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [permissions, setPermissions] = useState<SystemPermissions[]>([]);

  const hasPermission = useCallback(
    (permission: SystemPermissions) => permissions.includes(permission),
    [permissions]
  );

  const userPermissions = useMemo(() => {
    return Object.entries(PERMISSION_MAPPING).reduce(
      (acc, [key, permission]) => ({
        ...acc,
        [key]: hasPermission(permission),
      }),
      {}
    ) as PermissionsContextProps['userPermissions'];
  }, [hasPermission]);

  return (
    <PermissionsContext.Provider
      value={{
        permissions,
        setPermissions,
        hasPermission,
        userPermissions,
      }}
    >
      {children}
    </PermissionsContext.Provider>
  );
};

export const usePermissions = () => {
  const context = useContext(PermissionsContext);
  if (!context) {
    throw new Error('usePermissions must be used within a PermissionsProvider');
  }
  return context;
};
