import { Injectable } from '@nestjs/common';
import { WorkerLogger } from 'src/shared/logger.service';

@Injectable()
export class HtmlProcessingService {
  private readonly logger = new WorkerLogger(HtmlProcessingService.name);

  /**
   * Enhances HTML elements with CSS classes for better rendering
   * @param text The HTML content to enhance
   * @returns The HTML content with added CSS classes
   */
  enhanceHtmlWithStyles(text: string): string {
    this.logger.log(`Enhancing HTML with styles, input length: ${text.length}`);
    const result = text
      .replace(/<ul(?!\s+class=)/g, '<ul class="ml-6 list-disc"')
      .replace(/<ol(?!\s+class=)/g, '<ol class="ml-6 list-decimal"')
      .replace(
        /<table(?!\s+class=)/g,
        '<table class="table-auto border-collapse w-full text-left"'
      )
      .replace(/<thead(?!\s+class=)/g, '<thead class="bg-slate-200"')
      .replace(
        /<th(?!\s+class=|\s+colspan|\s+rowspan)/g,
        '<th class="px-4 py-2 font-medium text-slate-700"'
      )
      .replace(/<tbody(?!\s+class=)/g, '<tbody class="bg-white"')
      .replace(
        /<td(?!\s+class=|\s+colspan|\s+rowspan)/g,
        '<td class="border px-4 py-2"'
      );

    this.logger.log(
      `HTML enhanced with styles, output length: ${result.length}`
    );
    return result;
  }

  /**
   * Analyze HTML tables in a text chunk
   */
  analyzeHtmlTables(text: string): {
    hasTables: boolean;
    tableCount: number;
    tablePercentage: number;
    tableRanges: Array<{ start: number; end: number }>;
  } {
    this.logger.log(`Analyzing HTML tables in text of length: ${text.length}`);

    const result = {
      hasTables: false,
      tableCount: 0,
      tablePercentage: 0,
      tableRanges: [] as Array<{ start: number; end: number }>,
    };

    // Find all table opening and closing tags
    const openTags: number[] = [];
    const tableRanges: Array<{ start: number; end: number }> = [];

    let tableOpens = 0;
    let tableCloses = 0;

    // Use regex to find all opening and closing table tags
    const openTagRegex = /<table[^>]*>/gi;
    const closeTagRegex = /<\/table>/gi;

    let match: RegExpExecArray;
    while ((match = openTagRegex.exec(text)) !== null) {
      openTags.push(match.index);
      tableOpens++;
    }

    while ((match = closeTagRegex.exec(text)) !== null) {
      if (openTags.length > 0) {
        const start = openTags.shift()!;
        const end = match.index + 8; // Length of </table>
        tableRanges.push({ start, end });
        tableCloses++;
      }
    }

    // Calculate the total length of text covered by tables
    let totalTableLength = 0;
    for (const range of tableRanges) {
      totalTableLength += range.end - range.start;
    }

    result.hasTables = tableRanges.length > 0;
    result.tableCount = tableRanges.length;
    result.tablePercentage =
      text.length > 0 ? totalTableLength / text.length : 0;
    result.tableRanges = tableRanges;

    this.logger.log(
      `Table analysis complete: found ${result.tableCount} tables, coverage: ${(result.tablePercentage * 100).toFixed(2)}%`
    );
    return result;
  }

  /**
   * Pre-processes text lines to ensure HTML tables stay together
   * @param lines Array of text lines
   * @returns Array of processed lines with tables joined
   */
  joinLinesWithHtmlTables(lines: string[]): string[] {
    this.logger.log(
      `Joining lines with HTML tables, processing ${lines.length} lines`
    );

    const joinedLines: string[] = [];
    let currentTableLines: string[] = [];
    let tableLevel = 0;

    for (const line of lines) {
      // Count opening table tags in this line
      const openTagMatches = line.match(/<table[^>]*>/gi);
      const openTagCount = openTagMatches ? openTagMatches.length : 0;

      // Count closing table tags in this line
      const closeTagMatches = line.match(/<\/table>/gi);
      const closeTagCount = closeTagMatches ? closeTagMatches.length : 0;

      // Update table nesting level
      tableLevel += openTagCount - closeTagCount;

      // Ensure tableLevel doesn't go below 0 (malformed HTML)
      tableLevel = Math.max(0, tableLevel);

      // Add the line to current table collection or directly to output
      if (tableLevel > 0 || openTagCount > 0) {
        // We're inside a table or just started one
        currentTableLines.push(line);

        // If we've just exited all tables, join and add the combined lines
        if (tableLevel === 0) {
          joinedLines.push(currentTableLines.join('\n'));
          currentTableLines = [];
        }
      } else {
        // Regular line, not in a table
        joinedLines.push(line);
      }
    }

    // Handle any remaining table content (in case of unbalanced tags)
    if (currentTableLines.length > 0) {
      joinedLines.push(currentTableLines.join('\n'));
    }

    this.logger.log(
      `Joined HTML tables, resulting in ${joinedLines.length} lines`
    );
    return joinedLines;
  }
}
