import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1730186187615 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 51 WHERE "datapointId" = 'S1-10_01';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 51 WHERE "datapointId" = 'S1-10_02';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 51 WHERE "datapointId" = 'S1-10_03';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 51 WHERE "datapointId" = 'S1-10_04';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 52 WHERE "datapointId" = 'S1-11_01';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 52 WHERE "datapointId" = 'S1-11_02';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 52 WHERE "datapointId" = 'S1-11_03';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 52 WHERE "datapointId" = 'S1-11_04';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 52 WHERE "datapointId" = 'S1-11_05';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 52 WHERE "datapointId" = 'S1-11_06';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 52 WHERE "datapointId" = 'S1-11_07';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 52 WHERE "datapointId" = 'S1-11_08';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 52 WHERE "datapointId" = 'S1-11_09';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 52 WHERE "datapointId" = 'S1-11_10';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 52 WHERE "datapointId" = 'S1-11_11';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 53 WHERE "datapointId" = 'S1-12_01';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 53 WHERE "datapointId" = 'S1-12_02';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 53 WHERE "datapointId" = 'S1-12_03';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 54 WHERE "datapointId" = 'S1-13_01';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 54 WHERE "datapointId" = 'S1-13_02';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 54 WHERE "datapointId" = 'S1-13_03';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 54 WHERE "datapointId" = 'S1-13_04';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 54 WHERE "datapointId" = 'S1-13_05';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 54 WHERE "datapointId" = 'S1-13_06';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 54 WHERE "datapointId" = 'S1-13_07';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 55 WHERE "datapointId" = 'S1-14_01';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 55 WHERE "datapointId" = 'S1-14_02';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 55 WHERE "datapointId" = 'S1-14_03';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 55 WHERE "datapointId" = 'S1-14_04';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 55 WHERE "datapointId" = 'S1-14_05';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 55 WHERE "datapointId" = 'S1-14_06';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 55 WHERE "datapointId" = 'S1-14_07';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 55 WHERE "datapointId" = 'S1-14_08';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 55 WHERE "datapointId" = 'S1-14_09';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 55 WHERE "datapointId" = 'S1-14_10';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 55 WHERE "datapointId" = 'S1-14_11';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 55 WHERE "datapointId" = 'S1-14_12';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 56 WHERE "datapointId" = 'S1-15_01';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 56 WHERE "datapointId" = 'S1-15_02';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 56 WHERE "datapointId" = 'S1-15_03';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 56 WHERE "datapointId" = 'S1-15_04';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 57 WHERE "datapointId" = 'S1-16_01';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 57 WHERE "datapointId" = 'S1-16_02';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 57 WHERE "datapointId" = 'S1-16_03';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 57 WHERE "datapointId" = 'S1-16_04';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 57 WHERE "datapointId" = 'S1-16_05';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 57 WHERE "datapointId" = 'S1-16_06';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 57 WHERE "datapointId" = 'S1-16_07';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 58 WHERE "datapointId" = 'S1-17_01';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 58 WHERE "datapointId" = 'S1-17_02';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 58 WHERE "datapointId" = 'S1-17_03';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 58 WHERE "datapointId" = 'S1-17_04';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 58 WHERE "datapointId" = 'S1-17_05';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 58 WHERE "datapointId" = 'S1-17_06';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 58 WHERE "datapointId" = 'S1-17_07';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 58 WHERE "datapointId" = 'S1-17_08';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 58 WHERE "datapointId" = 'S1-17_09';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 58 WHERE "datapointId" = 'S1-17_10';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 58 WHERE "datapointId" = 'S1-17_11';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 58 WHERE "datapointId" = 'S1-17_12';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 58 WHERE "datapointId" = 'S1-17_13';
            UPDATE esrs_datapoint SET "esrsDisclosureRequirementId" = 58 WHERE "datapointId" = 'S1-17_14';
            `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
