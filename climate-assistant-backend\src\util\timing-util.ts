export interface ProcessTiming {
  name: string;
  duration: number; // in milliseconds
  startTime: number;
  endTime: number;
}

export class TimingTracker {
  private timings: ProcessTiming[] = [];
  private activeTimers: Map<string, number> = new Map();

  start(processName: string): void {
    this.activeTimers.set(processName, Date.now());
  }

  end(processName: string): void {
    const startTime = this.activeTimers.get(processName);
    if (!startTime) {
      console.warn(`No active timer found for process: ${processName}`);
      return;
    }

    const endTime = Date.now();
    const duration = endTime - startTime;

    this.timings.push({
      name: processName,
      duration,
      startTime,
      endTime,
    });

    this.activeTimers.delete(processName);
  }

  getTimings(): ProcessTiming[] {
    return this.timings.sort((a, b) => a.startTime - b.startTime);
  }

  getTotalDuration(): number {
    if (this.timings.length === 0) return 0;
    const firstStart = Math.min(...this.timings.map((t) => t.startTime));
    const lastEnd = Math.max(...this.timings.map((t) => t.endTime));
    return lastEnd - firstStart;
  }
}
