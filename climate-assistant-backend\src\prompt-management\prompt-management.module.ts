import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LlmPrompt } from './entities/llm-prompt.entity';
import { LlmPromptHistory } from './entities/llm-prompt-history.entity';
import { PromptManagementService } from './prompt-management.service';
import { PromptManagementController } from './prompt-management.controller';
import { UsersModule } from 'src/users/users.module';
import { DatapointRequestModule } from 'src/datapoint/datapoint-request.module';
import { RolePermission } from 'src/auth/entities/role-permission.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([LlmPrompt, LlmPromptHistory, RolePermission]),
    UsersModule,
    forwardRef(() => DatapointRequestModule),
  ],
  controllers: [PromptManagementController],
  providers: [PromptManagementService],
  exports: [PromptManagementService],
})
export class PromptManagementModule {}
