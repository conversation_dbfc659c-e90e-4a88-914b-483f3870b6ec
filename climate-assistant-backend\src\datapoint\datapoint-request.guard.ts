import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { DatapointRequestService } from './datapoint-request.service';
import { DataRequestService } from '../data-request/data-request.service';
import { Reflector } from '@nestjs/core';
import {
  isRequestForDataGenerationType,
  getGenerationIdFromRequestId,
} from 'src/util/common-util';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DatapointGeneration } from './entities/datapoint-generation.entity';

@Injectable()
export class DatapointRequestGuard implements CanActivate {
  constructor(
    private readonly dataRequestService: DataRequestService,
    private readonly datapointRequestService: DatapointRequestService,
    private readonly reflector: Reflector,
    @InjectRepository(DatapointGeneration)
    private readonly datapointGenerationRepository: Repository<DatapointGeneration>
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    let datapointRequestId = request.params.datapointRequestId;
    const workspaceId = request.user.workspaceId;

    if (isRequestForDataGenerationType(datapointRequestId)) {
      const generationId = getGenerationIdFromRequestId(datapointRequestId);
      const datapointGeneration =
        await this.datapointGenerationRepository.findOne({
          where: { id: generationId },
        });

      if (!datapointGeneration) {
        throw new UnauthorizedException(
          `Datapoint generation with ID ${generationId} not found`
        );
      }

      request.datapointGeneration = datapointGeneration;
      datapointRequestId = datapointGeneration.datapointRequestId;
    }

    const datapointRequest =
      await this.datapointRequestService.findById(datapointRequestId);

    const dataRequestId = datapointRequest.dataRequestId;

    const dataRequest =
      await this.dataRequestService.findProject(dataRequestId);
    const project = dataRequest.project;

    if (project.workspaceId !== workspaceId) {
      throw new UnauthorizedException(`Project is not from this workspace`);
    }

    request.datapointRequest = datapointRequest;

    return true;
  }
}
