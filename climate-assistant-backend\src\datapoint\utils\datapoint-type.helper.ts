import { Injectable } from '@nestjs/common';

@Injectable()
export class DatapointTypeHelper {
  isNumericDataPoint(dataType?: string) {
    if (!dataType) {
      return false;
    }
    // prettier-ignore
    const numericDataTypes = [
      'monetary', 'gyear', 'date', 'ghgemissions', 'energy', 'intensity', 'integer', 'decimal', 'volume', 'area', 'percent', 'mass'
    ].map((type) => type.toLowerCase());
    const dataTypeList = dataType.split('/');
    return dataTypeList.some((type) =>
      numericDataTypes.includes(type.toLowerCase())
    );
  }
}
