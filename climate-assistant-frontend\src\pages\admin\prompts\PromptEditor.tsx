import React, { useEffect, useState } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Plugin } from '@tiptap/pm/state';
import { Decoration, DecorationSet } from '@tiptap/pm/view';
import { getMissingVariables, SYNTAX_PATTERNS } from '@/lib/prompt-utils';

// Plugin for handlebars syntax highlighting
const handlebarsHighlightPlugin = () => {
  return new Plugin({
    props: {
      decorations(state) {
        const decorations: Decoration[] = [];
        const doc = state.doc;

        // Regex for all handlebars expressions (variables, helpers, etc.)
        // Using shared pattern for consistency across the application
        const handlebarExpressionRegex =
          SYNTAX_PATTERNS.allHandlebarsExpressions;

        // Regex for conditional blocks (if, unless, each, with) including their closing tags
        const blockHelpers = ['if', 'unless', 'each', 'with'];
        const blockPattern = SYNTAX_PATTERNS.conditionalBlocks(blockHelpers);

        // Regex for block opening/closing tags and else statements
        const blockTagPattern = SYNTAX_PATTERNS.blockTags;

        doc.descendants((node, pos) => {
          if (node.isText) {
            const text = node.text || '';
            let match;

            // First, collect all conditional block ranges
            const conditionalRanges: Array<{ start: number; end: number }> = [];
            blockPattern.lastIndex = 0;
            while ((match = blockPattern.exec(text)) !== null) {
              conditionalRanges.push({
                start: match.index,
                end: match.index + match[0].length,
              });
            }

            // Then highlight all handlebars expressions
            handlebarExpressionRegex.lastIndex = 0;
            while ((match = handlebarExpressionRegex.exec(text)) !== null) {
              const from = pos + match.index;
              const to = from + match[0].length;
              const localStart = match.index;
              const localEnd = localStart + match[0].length;

              // Check if this expression is part of a conditional block structure
              let isBlockTag = false;
              blockTagPattern.lastIndex = 0;
              let blockTagMatch;
              while ((blockTagMatch = blockTagPattern.exec(text)) !== null) {
                if (blockTagMatch.index === match.index) {
                  isBlockTag = true;
                  break;
                }
              }

              // Check if this expression is inside a conditional block
              let isInsideConditional = false;
              for (const range of conditionalRanges) {
                if (localStart >= range.start && localEnd <= range.end) {
                  isInsideConditional = true;
                  break;
                }
              }

              // Apply appropriate highlighting
              if (isBlockTag || isInsideConditional) {
                decorations.push(
                  Decoration.inline(from, to, {
                    class: 'handlebars-conditional-highlight',
                  })
                );
              } else {
                decorations.push(
                  Decoration.inline(from, to, {
                    class: 'handlebars-highlight',
                  })
                );
              }
            }
          }
        });

        return DecorationSet.create(doc, decorations);
      },
    },
  });
};

interface PromptEditorProps {
  promptId: string;
  initialContent: string;
  requiredVariables?: Record<string, string>;
  onChange: (content: string) => void;
}

export const PromptEditor: React.FC<PromptEditorProps> = ({
  promptId,
  initialContent,
  requiredVariables,
  onChange,
}) => {
  const [_content, setContent] = useState(initialContent);

  // Update content when promptId or initialContent changes
  useEffect(() => {
    setContent(initialContent);
  }, [promptId, initialContent]);

  // Convert plain text with line breaks to HTML
  const textToHtml = (text: string): string => {
    // Split by line breaks and wrap each line in a paragraph
    const lines = text.split('\n');
    const paragraphs = lines.map((line, _index) => {
      // Empty lines should still create a paragraph for spacing
      const content = line || '<br>';
      return `<p>${content}</p>`;
    });

    return paragraphs.join('');
  };

  // Convert HTML back to plain text
  const htmlToText = (html: string): string => {
    // Create a temporary element to parse HTML
    const temp = document.createElement('div');
    temp.innerHTML = html;

    // Get all paragraphs
    const paragraphs = temp.querySelectorAll('p');
    const lines: string[] = [];

    paragraphs.forEach((p, _index) => {
      const text = p.textContent || '';
      // Don't add empty line if it's just a <br>
      if (text && text !== '\n') {
        lines.push(text);
      } else {
        lines.push('');
      }
    });

    return lines.join('\n');
  };

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        paragraph: {
          HTMLAttributes: {
            class: 'editor-paragraph',
          },
        },
      }),
    ],
    editorProps: {
      attributes: {
        class: 'prompt-editor',
      },
    },
    content: textToHtml(initialContent),
    onCreate: ({ editor }) => {
      // Add the handlebars highlighting plugin
      editor.registerPlugin(handlebarsHighlightPlugin());
    },
    onUpdate: ({ editor }) => {
      const editorHtml = editor.getHTML();
      const editorContent = htmlToText(editorHtml);

      onChange(editorContent);
      validateVariables(editorContent);
    },
  });

  const validateVariables = (content: string) => {
    // Use the shared utility function for consistent variable validation
    // Note: We don't store missing variables locally since the parent component
    // handles variable validation and display
    getMissingVariables(content, requiredVariables || {});
  };

  useEffect(() => {
    if (editor && initialContent) {
      const currentText = htmlToText(editor.getHTML());
      if (currentText !== initialContent) {
        editor.commands.setContent(textToHtml(initialContent));
        validateVariables(initialContent);
      }
    }
  }, [initialContent, editor]);

  useEffect(() => {
    return () => {
      editor?.destroy();
    };
  }, [editor]);

  return (
    <div className="space-y-4">
      <style>
        {`
          .handlebars-highlight {
            color: #0969da;
            background-color: #ddf4ff;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-family: monospace;
            font-size: 0.875em;
          }
          
          .handlebars-conditional-highlight {
            color: #6f42c1;
            background-color: #e7dcf8;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-family: monospace;
            font-size: 0.875em;
          }
          
          .prompt-editor {
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
            font-size: 0.875rem;
            line-height: 1.5;
            min-height: 200px;
          }
          
          .prompt-editor p.editor-paragraph {
            margin: 0;
            min-height: 1.5em;
          }
          
          .prompt-editor p.editor-paragraph:empty::before {
            content: '\u200B'; /* Zero-width space to maintain height */
          }
        `}
      </style>

      <div className="border rounded-lg p-4 min-h-[200px] bg-muted/30">
        <EditorContent editor={editor} className="prose prose-sm max-w-none" />
      </div>
    </div>
  );
};
