import { Language } from './project/entities/project.entity';

export enum LLM_MODELS {
  'gpt-4o' = 'gpt-4o',
  'gpt-4o-mini' = 'gpt-4o-mini',
  'o3-mini' = 'o3-mini',
  'deepseek-r1' = 'deepseek-r1',
  'o3' = 'o3',
  'o4-mini' = 'o4-mini',
}

export const LANGUAGE_MAP: Record<Language, string> = {
  [Language.BG]: 'Bulgarian',
  [Language.HR]: 'Croatian',
  [Language.CS]: 'Czech',
  [Language.DA]: 'Danish',
  [Language.NL]: 'Dutch',
  [Language.EN]: 'English',
  [Language.ET]: 'Estonian',
  [Language.FI]: 'Finnish',
  [Language.FR]: 'French',
  [Language.DE]: 'German',
  [Language.EL]: 'Greek',
  [Language.HU]: 'Hungarian',
  [Language.GA]: 'Irish',
  [Language.IT]: 'Italian',
  [Language.LV]: 'Latvian',
  [Language.LT]: 'Lithuanian',
  [Language.MT]: 'Maltese',
  [Language.PL]: 'Polish',
  [Language.PT]: 'Portuguese',
  [Language.RO]: 'Romanian',
  [Language.SK]: 'Slovak',
  [Language.SL]: 'Slovene',
  [Language.ES]: 'Spanish',
  [Language.SV]: 'Swedish',
};

export enum USER_ROLES {
  SuperAdmin = 'SUPER_ADMIN',
  WorkspaceAdmin = 'WORKSPACE_ADMIN',
  AiContributor = 'AI_CONTRIBUTOR',
  AiReviewer = 'AI_ONLY_REVIEW',
  Contributor = 'CONTRIBUTOR',
}

//Make sure to sync this with the frontend USER_ROLES_MAPPING_FOR_UI
export enum SystemPermissions {
  // Datapoints
  CREATE_DATAPOINTS = 'CREATE_DATAPOINTS',
  EDIT_DATAPOINTS = 'EDIT_DATAPOINTS',
  VIEW_ALL_DATAPOINTS = 'VIEW_ALL_DATAPOINTS',
  APPROVE_DATAPOINTS = 'APPROVE_DATAPOINTS',
  MARK_DATAPOINTS = 'MARK_DATAPOINTS',
  EXPORT_DATAPOINTS_EXCEL = 'EXPORT_DATAPOINTS_EXCEL',

  // Disclosure Requirements
  CREATE_DRS = 'CREATE_DRS',
  EDIT_DRS = 'EDIT_DRS',
  APPROVE_DRS = 'APPROVE_DRS',
  MARK_DRS = 'MARK_DRS',
  ASSIGN_USERS_DRS = 'ASSIGN_USERS_DRS',
  EXPORT_DR_EXCEL = 'EXPORT_DR_EXCEL',

  // GAP Analysis
  GAP_ANALYSIS_DATAPOINTS = 'GAP_ANALYSIS_DATAPOINTS',
  GAP_ANALYSIS_DATAPOINTS_REVIEW = 'GAP_ANALYSIS_DATAPOINTS_REVIEW',
  GAP_ANALYSIS_DRS = 'GAP_ANALYSIS_DRS',
  GAP_ANALYSIS_DRS_REVIEW = 'GAP_ANALYSIS_DRS_REVIEW',

  // Comments
  CREATE_COMMENTS = 'CREATE_COMMENTS',
  EDIT_COMMENTS = 'EDIT_COMMENTS',

  // Administrative & Workspace
  CREATE_WORKSPACE = 'CREATE_WORKSPACE',
  SWITCH_WORKSPACE = 'SWITCH_WORKSPACE',
  EDIT_WORKSPACE_SETTINGS = 'EDIT_WORKSPACE_SETTINGS',
  EDIT_PROJECT_SETTINGS = 'EDIT_PROJECT_SETTINGS',
  CHANGE_USER_ROLES = 'CHANGE_USER_ROLES',
  INVITE_USERS = 'INVITE_USERS',

  // General System Access
  ACCESS_LEGAL_TEXT = 'ACCESS_LEGAL_TEXT',
  EXPORT_FINAL_REPORT = 'EXPORT_FINAL_REPORT',

  // Materiality Setting
  CHANGE_MATERIALITY_SETTING = 'CHANGE_MATERIALITY_SETTING',
  ACCESS_MATERIALITY_SETTING = 'ACCESS_MATERIALITY_SETTING',

  // Version History
  ACCESS_VERSION_HISTORY = 'ACCESS_VERSION_HISTORY',
  RESTORE_VERSION_HISTORY = 'RESTORE_VERSION_HISTORY',

  // Prompt Management
  VIEW_ALL_PROMPTS = 'VIEW_ALL_PROMPTS',
  CREATE_PROMPTS = 'CREATE_PROMPTS',
  EDIT_PROMPTS = 'EDIT_PROMPTS',
  VIEW_PROMPT_DETAILS = 'VIEW_PROMPT_DETAILS',
  VIEW_PROMPT_METADATA = 'VIEW_PROMPT_METADATA',
  VIEW_PROMPT_HISTORY = 'VIEW_PROMPT_HISTORY',
  TEST_PROMPTS = 'TEST_PROMPTS',
  VIEW_PROMPT_MODELS = 'VIEW_PROMPT_MODELS',
  VIEW_PROMPTS_BY_FEATURE = 'VIEW_PROMPTS_BY_FEATURE',
}
