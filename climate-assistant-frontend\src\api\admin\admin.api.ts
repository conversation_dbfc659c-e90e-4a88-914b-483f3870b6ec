import axios from 'axios';

import { API_URL } from '@/api/apiConstants';

// LLM Models enum (should match backend)
export enum LLM_MODELS {
  'gpt-4o' = 'gpt-4o',
  'gpt-4o-mini' = 'gpt-4o-mini',
  'o3-mini' = 'o3-mini',
  'deepseek-r1' = 'deepseek-r1',
  'o3' = 'o3',
  'o4-mini' = 'o4-mini',
}

// Interfaces for prompt management
export interface Prompt {
  id: string;
  feature: string;
  chainIdentifier: string;
  prompt: string;
  model: LLM_MODELS;
  requiredVariables: Record<string, string>;
  endpoint: string;
  description?: string;
  isActive: boolean;
}

export interface TestPromptRequest {
  datapointRequestId: string;
  prompts: Record<
    string,
    { prompt: string; chainIdentifier: string; model?: LLM_MODELS }
  >;
  feature: string;
  useExistingReportTextForReference?: boolean;
  variables?: Record<string, any>;
}

export interface DPGenerationPromptResult {
  content: string;
  metadata?: any;
  error?: string;
  prompts?: Array<{
    role: string;
    content: string;
  }>;
}
export interface DPGapAnalysisPromptResult {
  text?: string;
  gaps?: any;
  gap?: any;
  error?: string;
  prompts?: Array<{
    role: string;
    content: string;
  }>;
}

export interface UpdatePromptRequest {
  prompt?: string;
  model?: LLM_MODELS;
  comment?: string;
}

export interface DatapointForTesting {
  id: string;
  esrsDatapoint?: {
    datapointId: string;
  };
  status: string;
}

export interface PromptMetadata {
  prompt: Prompt;
  variables: {
    required: string[];
    optional: Array<{
      name: string;
      content: string;
      hasVariable: boolean;
    }>;
  };
}

export interface PromptHistory {
  id: string;
  promptId: string;
  oldPrompt: string;
  newPrompt: string;
  changes: Record<string, any>;
  changedBy: {
    id: string;
    email: string;
    name?: string;
  };
  createdAt: string;
}

// Prompt Management API functions
export const fetchAllPrompts = async (): Promise<Prompt[]> => {
  const response = await axios.get<Prompt[]>(`${API_URL}/admin/prompts`);
  return response.data;
};

export const fetchPromptsByFeature = async (
  feature: string
): Promise<Prompt[]> => {
  const response = await axios.get<Prompt[]>(
    `${API_URL}/admin/prompts/feature/${feature}`
  );
  return response.data;
};

export const fetchPromptById = async (id: string): Promise<Prompt> => {
  const response = await axios.get<Prompt>(`${API_URL}/admin/prompts/${id}`);
  return response.data;
};

export const fetchPromptMetadata = async (
  id: string
): Promise<PromptMetadata> => {
  const response = await axios.get<PromptMetadata>(
    `${API_URL}/admin/prompts/${id}/metadata`
  );
  return response.data;
};

export const updatePrompt = async (
  id: string,
  data: UpdatePromptRequest
): Promise<Prompt> => {
  const response = await axios.put<Prompt>(
    `${API_URL}/admin/prompts/${id}`,
    data
  );
  return response.data;
};

export const testDPGenerationPrompt = async (
  data: TestPromptRequest
): Promise<DPGenerationPromptResult> => {
  const response = await axios.post<DPGenerationPromptResult>(
    `${API_URL}/admin/prompts/test`,
    data
  );
  return response.data;
};

export const fetchDatapointsForTesting = async (): Promise<
  DatapointForTesting[]
> => {
  const response = await axios.get<DatapointForTesting[]>(
    `${API_URL}/admin/prompts/all/workspace`
  );
  return response.data;
};

export const fetchPromptHistory = async (
  promptId: string
): Promise<PromptHistory[]> => {
  const response = await axios.get<PromptHistory[]>(
    `${API_URL}/admin/prompts/${promptId}/history`
  );
  return response.data;
};

export const fetchAvailableModels = async (): Promise<LLM_MODELS[]> => {
  const response = await axios.get<LLM_MODELS[]>(
    `${API_URL}/admin/prompts/models`
  );
  return response.data;
};

export const testDPGapAnalysisPrompt = async (
  data: TestPromptRequest
): Promise<DPGapAnalysisPromptResult> => {
  const response = await axios.post<DPGapAnalysisPromptResult>(
    `${API_URL}/admin/prompts/test-dp-gaps`,
    data
  );
  return response.data;
};
