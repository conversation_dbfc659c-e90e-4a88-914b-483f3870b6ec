import { ReactMultiEmail } from 'react-multi-email';
import { ChevronDownIcon, LoaderCircle } from 'lucide-react';
import 'react-multi-email/dist/style.css';
import { useState } from 'react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
  DialogHeader,
} from '@/components/ui/dialog';
import {
  getRolesAllowedToImport,
  USER_ROLE,
  WORKSPACE_ROLES_MAPPING_FOR_UI,
} from '@/constants/workspaceConstants';

function AddUserDialogue({
  isDialogueOpen,
  setIsDialogueOpen,
  handleInviteUser,
  isInviteUsersInProgress,
}: {
  isDialogueOpen: boolean;
  setIsDialogueOpen: (value: boolean) => void;
  handleInviteUser: (emails: string[], role: USER_ROLE) => void;
  isInviteUsersInProgress: boolean;
}) {
  const [emails, setEmails] = useState<string[]>([]);
  const [role, setRole] = useState<USER_ROLE>(USER_ROLE.Contributor);
  return (
    <Dialog open={isDialogueOpen} onOpenChange={setIsDialogueOpen}>
      <DialogContent className="max-w-xl">
        <DialogHeader>
          <DialogTitle>Invite new users to workspace</DialogTitle>
          <DialogDescription>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                handleInviteUser(emails, role);
                setEmails([]);
              }}
            >
              <div className="flex gap-4">
                <ReactMultiEmail
                  placeholder="Invite via e-mail"
                  emails={emails}
                  onChange={(_emails: string[]) => {
                    setEmails(_emails);
                  }}
                  autoFocus={true}
                  className="flex-1"
                  getLabel={(email, index, removeEmail) => {
                    return (
                      <div data-tag key={index}>
                        <div data-tag-item>{email}</div>
                        <span
                          data-tag-handle
                          onClick={() => removeEmail(index)}
                        >
                          ×
                        </span>
                      </div>
                    );
                  }}
                />
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="secondary"
                      className="rounded-sm flex gap-2"
                    >
                      {WORKSPACE_ROLES_MAPPING_FOR_UI[role]}
                      <ChevronDownIcon className="w-3 h-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[160px]">
                    {getRolesAllowedToImport()?.map((role) => {
                      return (
                        <DropdownMenuItem
                          className="cursor-pointer"
                          key={role}
                          onClick={() => setRole(role)}
                        >
                          {WORKSPACE_ROLES_MAPPING_FOR_UI[role]}
                        </DropdownMenuItem>
                      );
                    })}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              <div className="flex gap-4">
                <Button
                  variant="default"
                  className="mt-4"
                  type="submit"
                  disabled={!emails.length || isInviteUsersInProgress}
                >
                  {isInviteUsersInProgress && (
                    <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Invite
                </Button>
                <Button
                  variant="outline"
                  onClick={(e) => {
                    e.preventDefault();
                    setEmails([]);
                  }}
                  className="mt-4"
                >
                  Clear
                </Button>
              </div>
            </form>
          </DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}

export default AddUserDialogue;
