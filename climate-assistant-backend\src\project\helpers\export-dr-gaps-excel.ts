import * as ExcelJS from 'exceljs';
import * as cheerio from 'cheerio';
import {
  CITATION_CLIENT_REGEX,
  MERGED_CELL_REGEX,
} from 'src/util/llm-response-util';
import { marked } from 'marked';
import { convertHtmlToPlainText } from './export-project-excel';

interface DrGapInfo {
  esrs: string;
  drId: string;
  drTitle: string;
  drText: string; // This is the text of the disclosure requirement
  drDescription: string;
  drObjective: string;
  lawText: string;
  lawTextAR: string;
  drGaps: string;
}

const spanRegex = /<span[^>]*>(.*?)<\/span>/g;

export async function generateDrGapsExcelReport(
  drGaps: DrGapInfo[]
): Promise<Buffer> {
  try {
    // Create Excel workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'ESG Data Tool';
    workbook.lastModifiedBy = 'ESG Data Tool';
    workbook.created = new Date();
    workbook.modified = new Date();

    const worksheet = workbook.addWorksheet('DR Gaps Report', {
      views: [{ state: 'frozen', xSplit: 0, ySplit: 1 }], // Freeze the header row
    });

    // Define columns with appropriate widths
    worksheet.columns = [
      { header: 'ESRS Topic', key: 'esrs', width: 12 },
      { header: 'Disclosure Requirement ID', key: 'drId', width: 20 },
      { header: 'Disclosure Requirement Title', key: 'drTitle', width: 40 },
      { header: 'Report Text', key: 'drText', width: 50 },
      // TODO: Uncomment these columns when law texts are available
      // { header: 'Disclosure Requirement Description', key: 'drDescription', width: 50, },
      // { header: 'Disclosure Requirement Objective', key: 'drObjective', width: 40, },
      // { header: 'Law Text', key: 'lawText', width: 40 },
      // { header: 'Law Text AR', key: 'lawTextAR', width: 40 },
      { header: 'GAP Heading', key: 'gapHeading', width: 30 },
      { header: 'GAP Description', key: 'gapDescription', width: 40 },
      { header: 'Recommended Actions', key: 'recommendedActions', width: 40 },
      { header: 'Example Text', key: 'exampleText', width: 40 },
      { header: 'Disclaimer', key: 'disclaimer', width: 40 },
    ];

    // Style header row
    const headerRow = worksheet.getRow(1);
    headerRow.height = 25;
    headerRow.font = { bold: true, size: 12 };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFD3D3D3' }, // Light gray background
    };
    headerRow.alignment = { vertical: 'middle', horizontal: 'center' };

    // Add borders to headers
    headerRow.eachCell((cell) => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });

    // Process each DR gap row
    for (let i = 0; i < drGaps.length; i++) {
      const drGap = drGaps[i] as DrGapInfo;
      const rowIndex = i + 2; // Row 1 is header

      // Extract GAP information
      let dtText = '';
      let gapHeading = '';
      let gapDescription = '';
      let recommendedActions = '';
      let exampleText = '';
      let disclaimer = '';

      let cleanedText = drGap.drText
        .replace(CITATION_CLIENT_REGEX, '$3')
        .replace(MERGED_CELL_REGEX, '')
        .trim();
      const htmlContent =
        '<meta charset="UTF-8">' + (await marked(cleanedText));
      dtText = convertHtmlToPlainText(htmlContent);

      if (drGap.drGaps && drGap.drGaps !== '') {
        try {
          // Parse the gap content to extract different sections
          const gapData = parseGapContent(drGap.drGaps);
          gapHeading = gapData.heading;
          gapDescription = gapData.description;
          recommendedActions = gapData.recommendedActions;
          exampleText = gapData.exampleText;
          disclaimer = gapData.disclaimer;
        } catch (error) {
          console.error('Error extracting DR gap information:', error);
          // Fallback to raw content
          gapDescription = drGap.drGaps;
        }
      }

      // Add row to worksheet
      const row = worksheet.addRow({
        esrs: drGap.esrs || '',
        drId: drGap.drId || '',
        drTitle: drGap.drTitle || '',
        drText: dtText || '',
        // TODO: Uncomment these fields when law texts are available
        // drDescription: drGap.drDescription || '',
        // drObjective: drGap.drObjective || '',
        // lawText: drGap.lawText || '',
        // lawTextAR: drGap.lawTextAR || '',
        gapHeading,
        gapDescription,
        recommendedActions,
        exampleText,
        disclaimer,
      });

      // Apply row styling
      row.height = 120; // Set a reasonable height for multi-line content

      // Apply cell styling
      row.eachCell((cell, colNumber) => {
        // Add borders
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        // Apply text wrapping and vertical alignment
        cell.alignment = {
          wrapText: true,
          vertical: 'top',
        };

        const INFO_COLUMNS = 4; // Number of info columns before gap columns (ESRS Topic, DR ID, DR Title)

        // Apply specific styling based on column
        if (colNumber === INFO_COLUMNS + 1 || colNumber === INFO_COLUMNS + 2) {
          // GAP Heading and Description columns
          // Light yellow background for gap information
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFFFF2CC' },
          };
        } else if (colNumber === INFO_COLUMNS + 3) {
          // Recommended Actions column
          // Light blue background for recommendations
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFD9E1F2' },
          };
        } else if (colNumber === INFO_COLUMNS + 4) {
          // Example Text column
          // Light green background for examples
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE2EFDA' },
          };
        } else if (colNumber === INFO_COLUMNS + 5) {
          // Disclaimer column
          // Light red background for disclaimers
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFFCE4D6' },
          };
        }
      });
    }

    // Generate buffer and return
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer as Buffer;
  } catch (error) {
    console.error('Error generating DR gaps Excel report:', error);
    throw new Error(
      `Failed to generate DR gaps Excel report: ${error.message}`
    );
  }
}

// Helper function to parse gap content
function parseGapContent(gapHtml: string): {
  heading: string;
  description: string;
  recommendedActions: string;
  exampleText: string;
  disclaimer: string;
} {
  try {
    const $ = cheerio.load(gapHtml);

    // Initialize result object
    const result = {
      heading: '',
      description: '',
      recommendedActions: '',
      exampleText: '',
      disclaimer: '',
    };

    // Extract heading (usually the first strong or h1/h2/h3 element)
    const headingElement = $('h1, h2, h3, strong').first();
    if (headingElement.length > 0) {
      result.heading = headingElement.text().trim();
    }

    // Extract Gap Description
    const gapP = $(
      'p:contains("Gap Identified:") p, p:contains("Gap Description:") p'
    );
    if (gapP.length > 0) {
      result.description = gapP.text().trim();
    } else {
      // Fallback: Get the first paragraph after heading
      const firstP = $('p').not(':has(strong)').first();
      if (firstP.length > 0) {
        result.description = firstP.text().trim();
      }
    }

    // Extract Recommended Actions
    const actionItems = $(
      'p:contains("Recommended Actions:") + ul li p, p:contains("Recommendations:") + ul li p'
    );
    if (actionItems.length > 0) {
      const items = actionItems
        .map((_, el) => `• ${$(el).text().trim()}`)
        .get();
      result.recommendedActions = items.join('\n');
    }

    // Extract Example Text
    const exampleP = $(
      'p:contains("Example Text:") + p, p:contains("Example:") + p'
    );
    if (exampleP.length > 0) {
      result.exampleText = exampleP.text().trim();
    }

    // Extract Disclaimer
    const disclaimerP = $('p:contains("Disclaimer:") p');
    if (disclaimerP.length > 0) {
      result.disclaimer = disclaimerP.text().trim();
    }

    // Clean up any remaining HTML tags
    Object.keys(result).forEach((key) => {
      result[key] = result[key].replace(spanRegex, (match, p1) => p1).trim();
    });

    return result;
  } catch (error) {
    console.error('Error parsing gap content:', error);
    // Return the raw content as description if parsing fails
    return {
      heading: '',
      description: gapHtml.replace(/<[^>]*>/g, '').trim(),
      recommendedActions: '',
      exampleText: '',
      disclaimer: '',
    };
  }
}
