# Redis Pub/Sub Implementation for SSE Events

## Implementation Changes

### 1. Created Redis Event Service
**File**: `src/events/redis-event.service.ts`
- Manages Redis pub/sub connections
- Provides cross-worker event broadcasting
- Handles connection lifecycle

### 2. Created Events Module
**File**: `src/events/events.module.ts`
- Global module that provides RedisEventService
- Imported in app.module.ts

### 3. Updated DataRequestService
**File**: `src/data-request/data-request.service.ts`

**Before**:
```typescript
private readonly eventSubject = new Subject<DatapointGenerationEvent>();
public readonly events$ = this.eventSubject.asObservable();

emitSseEvents(event: DatapointGenerationEvent) {
  this.eventSubject.next(eventPayload);
}
```

**After**:
```typescript
private readonly CHANNEL = 'datapoint-generation-events';

constructor(
  // ... other dependencies
  private readonly redisEventService: RedisEventService
) {
  this.events$ = this.redisEventService
    .subscribe<DatapointGenerationEvent>(this.CHANNEL)
    .asObservable();
}

async emitSseEvents(event: DatapointGenerationEvent) {
  await this.redisEventService.publish(this.CHANNEL, eventPayload);
}
```

### 4. Updated Queue Processors
**File**: `src/process-queue/queue.service.ts`
- Made all `emitSseEvents` calls async with `await`

### 5. Added ioredis Package
**File**: `package.json`
- Added `"ioredis": "^5.3.2"` dependency

## How It Works

### Event Flow
```
1. Queue Processor (Worker 1) completes a job
   ↓
2. Calls await dataRequestService.emitSseEvents(event)
   ↓
3. RedisEventService publishes to 'datapoint-generation-events' channel
   ↓
4. All 3 workers receive the event via Redis subscription
   ↓
5. Each worker's event$ observable emits the event
   ↓
6. All SSE clients (regardless of worker) receive the event
```

### Architecture
```
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│  Worker 0   │ │  Worker 1   │ │  Worker 2   │
│             │ │             │ │             │
│ SSE Client  │ │ Queue Job   │ │ SSE Client  │
│ Connected   │ │ Processes   │ │ Connected   │
└──────┬──────┘ └──────┬──────┘ └──────┬──────┘
       │               │               │
       │               ▼               │
       │        Emit Event            │
       │               │               │
       ▼               ▼               ▼
┌────────────────────────────────────────────┐
│              Redis Pub/Sub                  │
│         'datapoint-generation-events'       │
└────────────────────────────────────────────┘
       │               │               │
       ▼               ▼               ▼
   Receive         Receive         Receive
    Event           Event           Event
       │               │               │
       ▼               ▼               ▼
  SSE Client           -          SSE Client
   Notified                        Notified
```

## Testing

### 1. Test Redis Pub/Sub
```bash
cd climate-assistant-backend
./scripts/test-redis-pubsub.sh
```

### 2. Test with PM2
```bash
# Build and start with PM2
npm run build
pm2 start ecosystem.config.js --env production

# Open 3 SSE connections (likely to different workers)
for i in {1..3}; do
  curl -N http://localhost:3000/data-request/events/datapoint/test-id &
done

# Trigger an event through the API
# All 3 connections should receive the event
```

### 3. Monitor Redis Activity
```bash
# In one terminal
redis-cli MONITOR | grep -E "(PUBLISH|SUBSCRIBE)"

# In another terminal, trigger events
```

## Benefits

1. **Cross-Worker Communication**: Events reach all SSE clients regardless of which worker they're connected to
2. **Scalability**: Can scale to multiple servers (not just workers)
3. **Reliability**: Redis handles message delivery
4. **Minimal Changes**: Drop-in replacement for existing Subject-based implementation

## Rollback Plan

If issues arise, to rollback:

1. Revert DataRequestService to use local Subject
2. Remove RedisEventService dependency
3. Remove EventsModule from app.module.ts
4. Change emitSseEvents back to synchronous

## Production Monitoring

Watch for:
- Redis connection errors in logs
- Event delivery latency
- Redis memory usage
- Number of subscribers per channel

Use health check endpoint to verify Redis connectivity:
```bash
curl http://localhost:3000/health
``` 