import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { WorkspaceService } from './workspace.service';
import { SystemPermissions, USER_ROLES } from 'src/constants';
import { PermissionGuard } from '../auth/guard/permission.guard';
import { Permissions } from '../auth/decorators/permissions.decorator';
import { DatapointDataRequestSharedService } from '../shared/shared-datapoint-datarequest.service';
import { VersionHistory } from './entities/version-history.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@ApiTags('workspace')
@UseGuards(PermissionGuard)
@Controller('workspace')
export class WorkspaceController {
  constructor(
    private readonly workspaceService: WorkspaceService,
    private readonly sharedService: DatapointDataRequestSharedService,
    @InjectRepository(VersionHistory)
    private readonly versionHistoryRepository: Repository<VersionHistory>
  ) {}

  @Get('/')
  async getWorkspaceDetails(@Request() req) {
    const workspaceId = req.user.workspaceId;
    return await this.workspaceService.findById(workspaceId);
  }

  @Get('/users')
  async getUsersByWorkspace(@Request() req) {
    const workspaceId = req.user.workspaceId;
    return await this.workspaceService.getUsersByWorkspace(workspaceId);
  }

  // @Roles(USER_ROLES.SuperAdmin)
  @Permissions(SystemPermissions.SWITCH_WORKSPACE)
  @Get('/all-workspaces')
  async getAllWorkspaces() {
    return await this.workspaceService.getAllWorkspaces();
  }

  @Permissions(SystemPermissions.INVITE_USERS)
  @Post('/inviteUsers')
  async addUserToWorkspace(
    @Request() req,
    @Body() body: { emails: string[]; role: USER_ROLES }
  ): Promise<{ success?: string; failure?: string }> {
    const workspaceId = req.user.workspaceId;
    const { emails, role } = body;
    const canInviteUser = await this.workspaceService.canInviteUser(
      role,
      req.user
    );
    if (!canInviteUser) {
      throw new Error('You are not allowed to invite users for this role');
    }
    const origin = req.headers.origin;
    const failedEmails: string[] = [];
    const inviteAllUsersPromise = emails.map(
      (email) =>
        new Promise(async (resolve, reject) => {
          try {
            const user = await this.workspaceService.inviteUserToWorkspace({
              inviteeEmail: req.user.email,
              origin,
              workspaceId,
              email: email.toLowerCase(),
              role,
              shouldSendEmail: origin.endsWith('.glacier.eco'), //Should send email
            });
            resolve(user);
          } catch (e) {
            failedEmails.push(email);
            reject(e);
          }
        })
    );
    await Promise.allSettled(inviteAllUsersPromise);
    if (failedEmails.length === emails.length) {
      return {
        failure: `Failed to invite ${failedEmails.length > 1 ? 'all' : ''} user${failedEmails.length > 1 ? 's' : ''}: ${failedEmails.join(' , ')}`,
      };
    }
    if (failedEmails.length > 0) {
      const invitedUsers = emails.length - failedEmails.length;
      return {
        success: `${invitedUsers} user${invitedUsers > 1 ? 's' : ''} invited successfully`,
        failure: `Failed to invite few users: ${failedEmails.join(' , ')} `,
      };
    }
    return {
      success: `${emails.length > 1 ? 'All' : ''} ${emails.length > 1 ? 'u' : 'U'}ser${emails.length > 1 ? 's' : ''} invited successfully`,
    };
  }

  @Permissions(SystemPermissions.ACCESS_VERSION_HISTORY)
  @Get('/version-history')
  async getVersionHistory(@Request() req, @Query('refId') refId: string) {
    const workspaceId = req.user.workspaceId;
    return await this.workspaceService.getVersionHistoryByRefId(
      workspaceId,
      refId
    );
  }

  @Permissions(SystemPermissions.RESTORE_VERSION_HISTORY)
  @Post('/version-history/restore')
  async restoreVersion(@Request() req, @Body() body: { versionId: string }) {
    const workspaceId = req.user.workspaceId;
    const { versionId } = body;

    const version = await this.workspaceService.getVersionHistoryByVersionId({
      workspaceId,
      versionId,
    });

    if (!version) {
      throw new Error(`Version with ID ${versionId} not found`);
    }

    const content = version.versionData.data.content;
    const event = version.event;

    if (!content) {
      throw new Error('No content to restore');
    }

    const data = await this.sharedService.restoreVersion({
      versionId: version.id,
      requestId: version.ref,
      content,
      event,
    });

    await this.workspaceService.storeActionHistory({
      workspaceId,
      event: 'version_restored',
      ref: data.id,
      versionData: {
        doneBy: req.user.id,
        data: {
          content: data.content,
        },
        event: 'version_restored',
      },
    });

    return data;
  }

  @Get('/:workspaceId')
  async getWorkspaceById(@Param('workspaceId') workspaceId: string) {
    const workspace = await this.workspaceService.findById(workspaceId);
    return workspace;
  }

  @Permissions(SystemPermissions.EDIT_WORKSPACE_SETTINGS)
  @Put('/')
  async updateWorkspaceById(@Request() req, @Body() body: any) {
    const workspaceId = req.user.workspaceId;
    const updatedWorkspace = await this.workspaceService.updateById(
      workspaceId,
      body
    );

    return updatedWorkspace;
  }

  @Permissions(SystemPermissions.EDIT_WORKSPACE_SETTINGS)
  @Put('/company')
  async updateCompanyDetail(@Request() req, @Body() body: any) {
    const workspaceId = req.user.workspaceId;
    //TODO: replace this with company service
    const updatedWorkspace = await this.workspaceService.updateCompanyDetail(
      workspaceId,
      body
    );

    return updatedWorkspace;
  }
}
