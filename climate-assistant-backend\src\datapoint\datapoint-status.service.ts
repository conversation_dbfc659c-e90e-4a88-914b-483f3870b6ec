import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Not, Repository } from 'typeorm';
import {
  DatapointQueueStatus,
  DatapointRequest,
  DatapointRequestStatus,
} from './entities/datapoint-request.entity';
import {
  DatapointGeneration,
  datapointGenerationStatus,
} from './entities/datapoint-generation.entity';
import { User } from 'src/users/entities/user.entity';

@Injectable()
export class DatapointStatusService {
  constructor(
    @InjectRepository(DatapointRequest)
    private readonly datapointRequestRepository: Repository<DatapointRequest>,
    @InjectRepository(DatapointGeneration)
    private readonly datapointGenerationRepository: Repository<DatapointGeneration>
  ) {}

  async updateStatus({
    id,
    status,
  }: {
    id: string;
    status: DatapointRequestStatus;
  }) {
    await this.datapointRequestRepository.update({ id }, { status });
  }

  async updateQueueStatus({
    datapointRequestId,
    queueStatus,
  }: {
    datapointRequestId: string;
    queueStatus: DatapointQueueStatus | null;
  }) {
    await this.datapointRequestRepository.update(
      { id: datapointRequestId },
      { queueStatus }
    );
  }

  async updateGenerationStatus({
    datapointGenerationId,
    status,
    userId,
    workspaceId,
    evaluatorComment,
  }: {
    datapointGenerationId: string;
    status: datapointGenerationStatus;
    userId: string;
    workspaceId: string;
    evaluatorComment?: string;
  }) {
    try {
      let responseData: {
        content?: string;
        status: datapointGenerationStatus;
        evaluator?: User;
        evaluatedAt?: Date;
        evaluatorComment?: string;
      } = { status }; // Same status goes back to the client
      await this.datapointGenerationRepository.update(
        {
          id: datapointGenerationId,
        },
        {
          status,
          evaluatorId: userId,
          evaluatedAt: new Date(),
          evaluatorComment: evaluatorComment || null,
        }
      );
      const generation = await this.datapointGenerationRepository.findOne({
        where: { id: datapointGenerationId },
        relations: ['datapointRequest', 'evaluator'],
      });

      // Include evaluator information in response
      responseData.evaluator = generation.evaluator;
      responseData.evaluatedAt = generation.evaluatedAt;
      responseData.evaluatorComment = generation.evaluatorComment;

      const existingDatapoint = await this.datapointRequestRepository.findOne({
        where: { id: generation.datapointRequest.id },
      });
      if (
        status === datapointGenerationStatus.Approved ||
        status === datapointGenerationStatus.MinorChanges
      ) {
        existingDatapoint.content = generation.data.content;
        existingDatapoint.metadata = generation.data.metadata;
        await this.datapointRequestRepository.save(existingDatapoint);
        responseData = { ...responseData, content: generation.data.content };
      }
      return responseData;
    } catch (err) {
      console.log(err);
      throw new Error(`Error while updating generation status: ${err.message}`);
    }
  }

  async findDatapointRequestsExcludingStatus({
    dataRequestId,
    status,
  }: {
    dataRequestId: string;
    status: DatapointRequestStatus[];
  }): Promise<DatapointRequest[]> {
    return await this.datapointRequestRepository.find({
      where: {
        dataRequestId,
        status: Not(In(status)),
      },
    });
  }

  async findDatapointRequestsByStatus({
    dataRequestId,
    status,
  }: {
    dataRequestId: string;
    status: DatapointRequestStatus[];
  }): Promise<DatapointRequest[]> {
    return await this.datapointRequestRepository.find({
      where: {
        dataRequestId,
        status: In(status),
      },
      relations: {
        datapointDocumentChunkMap: {
          documentChunk: true,
        },
        datapointGenerations: true,
      },
    });
  }

  async assessScore({
    datapointGenerationId,
    textContent,
  }: {
    datapointGenerationId: string;
    textContent: string;
  }) {
    try {
      // Get the datapoint generation with only required fields for assessment
      const generation = await this.datapointGenerationRepository
        .createQueryBuilder('generation')
        .leftJoinAndSelect('generation.datapointRequest', 'datapointRequest')
        .leftJoinAndSelect('datapointRequest.dataRequest', 'dataRequest')
        .leftJoinAndSelect('dataRequest.project', 'project')
        .leftJoinAndSelect('project.workspace', 'workspace')
        .leftJoinAndSelect('workspace.companies', 'company')
        .leftJoinAndSelect('datapointRequest.esrsDatapoint', 'esrsDatapoint')
        .select([
          'generation.id',
          'datapointRequest.id',
          'dataRequest.id',
          'project.id',
          'project.reportingYear',
          'workspace.id',
          'company.id',
          'company.name',
          'esrsDatapoint.id',
          'esrsDatapoint.datapointId',
          'esrsDatapoint.paragraph',
          'esrsDatapoint.lawText',
        ])
        .where('generation.id = :datapointGenerationId', {
          datapointGenerationId,
        })
        .getOne();

      if (!generation) {
        throw new Error('Datapoint generation not found');
      }

      const { datapointRequest } = generation;
      const { dataRequest } = datapointRequest;
      const { project } = dataRequest;
      const { workspace } = project;
      const { esrsDatapoint } = datapointRequest;

      // Get the first company from workspace (assuming one company per workspace)
      const company = workspace.companies?.[0];
      if (!company) {
        throw new Error('No company found in workspace');
      }

      // Prepare the payload for the assessment API
      const assessmentPayload = {
        text_content: textContent,
        esrs_id: esrsDatapoint.datapointId,
        paragraph_reference: esrsDatapoint.paragraph || '',
        legal_text: esrsDatapoint.lawText || '',
        company_id: company.name, // Using company name as company_id
        reporting_year:
          parseInt(project.reportingYear) || new Date().getFullYear(),
      };

      // Make the API call to the assessment service
      const response = await fetch('http://34.91.10.27/api/v1/evaluate/csrd', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(assessmentPayload),
      });

      if (!response.ok) {
        throw new Error(
          `Assessment API error: ${response.status} ${response.statusText}`
        );
      }

      const assessmentResult = await response.json();
      return assessmentResult;
    } catch (error) {
      console.error('Error assessing score:', error);
      throw new Error(`Error while assessing score: ${error.message}`);
    }
  }
}
