import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DocumentService } from './document.service';
import { DocumentChunk } from './entities/document-chunk.entity';
import { Document } from './entities/document.entity';
import { DocumentController } from './document.controller';
import { WorkspaceService } from 'src/workspace/workspace.service';
import { Workspace } from 'src/workspace/entities/workspace.entity';
import { DocumentParserService } from './document-parser.service';
import { DatapointDocumentChunk } from 'src/datapoint-document-chunk/entities/datapoint-document-chunk.entity';
import { PromptService } from 'src/prompts/prompts.service';
import { WorkspaceModule } from 'src/workspace/workspace.module';
import { DatapointDocumentChunkModule } from 'src/datapoint-document-chunk/datapoint-document-chunk.module';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { BullModule } from '@nestjs/bull';
import { JobProcessor } from 'src/types/jobs';
import { LlmRateLimiterModule } from 'src/llm-rate-limiter/llm-rate-limiter.module';
import { DEFAULT_QUEUE_JOB_OPTIONS } from 'src/util/queue.config';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DocumentChunk,
      Document,
      Workspace,
      DatapointRequest,
      DatapointDocumentChunk,
    ]),
    WorkspaceModule,
    LlmRateLimiterModule,
    DatapointDocumentChunkModule,
    BullModule.registerQueue({ 
      name: JobProcessor.ChunkExtraction,
      defaultJobOptions: DEFAULT_QUEUE_JOB_OPTIONS,
    }),
  ],
  providers: [
    DocumentService,
    WorkspaceService,
    DocumentParserService,
    PromptService,
  ],
  exports: [DocumentService],
  controllers: [DocumentController],
})
export class DocumentModule {}
