import { useState } from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface Datapoint {
  id: string;
  status: string;
  esrsDatapoint?: {
    datapointId: string;
    name?: string;
  };
}

interface DatapointSelectorProps {
  datapoints: Datapoint[];
  selectedDatapoint: string;
  onSelect: (datapointId: string) => void;
  className?: string;
  placeholder?: string;
}

export const DatapointSelector: React.FC<DatapointSelectorProps> = ({
  datapoints,
  selectedDatapoint,
  onSelect,
  className,
  placeholder = 'Select a datapoint to test',
}) => {
  const [open, setOpen] = useState(false);

  const selectedDP = datapoints.find((dp) => dp.id === selectedDatapoint);

  const getDisplayText = (datapoint: Datapoint) => {
    const datapointId = datapoint.esrsDatapoint?.datapointId || 'Unknown ID';
    const name = datapoint.esrsDatapoint?.name;
    return name ? `${datapointId}: ${name}` : datapointId;
  };

  const getSearchValue = (datapoint: Datapoint) => {
    const datapointId = datapoint.esrsDatapoint?.datapointId || '';
    const name = datapoint.esrsDatapoint?.name || '';
    const status = datapoint.status || '';
    return `${datapointId} ${name} ${status}`.toLowerCase();
  };

  return (
    <div className={cn('w-full', className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
          >
            {selectedDP ? (
              <div className="flex items-center justify-between w-full">
                <span className="truncate pr-2">
                  {getDisplayText(selectedDP).length > 60
                    ? `${getDisplayText(selectedDP).substring(0, 60)}...`
                    : getDisplayText(selectedDP)}
                </span>
                <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 flex-shrink-0 ml-2">
                  {selectedDP.status}
                </span>
              </div>
            ) : (
              placeholder
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" side="bottom">
          <Command>
            <CommandInput placeholder="Search datapoints..." />
            <CommandList>
              <CommandEmpty>No datapoint found.</CommandEmpty>
              <CommandGroup>
                {datapoints.map((datapoint) => (
                  <CommandItem
                    key={datapoint.id}
                    value={getSearchValue(datapoint)}
                    onSelect={() => {
                      onSelect(datapoint.id);
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        selectedDatapoint === datapoint.id
                          ? 'opacity-100'
                          : 'opacity-0'
                      )}
                    />
                    <div className="flex items-center justify-between w-full">
                      <div className="flex flex-col gap-1 min-w-0 flex-1">
                        <span className="text-sm font-medium truncate">
                          {datapoint.esrsDatapoint?.datapointId}{' '}
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 flex-shrink-0 ml-2">
                            {datapoint.status}
                          </span>
                        </span>
                        {datapoint.esrsDatapoint?.name && (
                          <span className="text-xs text-slate-500 truncate">
                            {datapoint.esrsDatapoint.name.length > 100
                              ? `${datapoint.esrsDatapoint.name.substring(0, 100)}...`
                              : datapoint.esrsDatapoint.name}
                          </span>
                        )}
                      </div>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
};
