import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { TipTapEditor } from '@/components/ui/tiptap/tiptap-editor';

interface ProcessTiming {
  name: string;
  duration: number;
  startTime: number;
  endTime: number;
}

interface TestResultsProps {
  result: {
    content: string;
    metadata?: any;
    error?: string;
    prompts?: Array<{
      role: string;
      content: string;
    }>;
    timings?: {
      processes: ProcessTiming[];
      totalDuration: number;
    };
  };
}

export const TestResults: React.FC<TestResultsProps> = ({ result }) => {
  if (result.error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{result.error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-green-500" />
          Test Results
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">Generated Content:</h4>
            <TipTapEditor
              content={result.content}
              setContent={() => {}}
              isEditable={false}
            />
          </div>

          {result.timings && (
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Performance Metrics:
              </h4>
              <div className="bg-muted/30 rounded-lg p-4 space-y-2">
                <div className="text-sm font-semibold mb-3">
                  Total Duration: {result.timings.totalDuration}ms
                </div>
                <div className="space-y-1">
                  {result.timings.processes.map((process, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-center text-sm py-1 border-b border-border/50 last:border-0"
                    >
                      <span className="text-muted-foreground">
                        {process.name}
                      </span>
                      <span className="font-mono font-medium">
                        {process.duration}ms
                      </span>
                    </div>
                  ))}
                </div>
                <div className="mt-3 pt-3 border-t border-border/50">
                  <div className="text-xs text-muted-foreground">
                    Process Timeline:
                  </div>
                  <div className="mt-2 relative h-8 bg-background rounded">
                    {result.timings.processes.map((process, index) => {
                      const totalDuration = result.timings!.totalDuration;
                      const relativeStart =
                        ((process.startTime -
                          Math.min(
                            ...result.timings!.processes.map((p) => p.startTime)
                          )) /
                          totalDuration) *
                        100;
                      const relativeWidth =
                        (process.duration / totalDuration) * 100;

                      return (
                        <div
                          key={index}
                          className="absolute h-full"
                          style={{
                            left: `${relativeStart}%`,
                            width: `${relativeWidth}%`,
                          }}
                          title={`${process.name}: ${process.duration}ms`}
                        >
                          <div
                            className={cn(
                              'h-full rounded-sm opacity-80',
                              index % 2 === 0 ? 'bg-primary' : 'bg-primary/60'
                            )}
                          />
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          )}

          {result.prompts && result.prompts.length > 0 && (
            <div>
              <h4 className="font-medium mb-2">Prompts Sent to LLM:</h4>
              <Accordion type="multiple" className="w-full">
                {result.prompts.map((prompt, index) => (
                  <AccordionItem key={index} value={`item-${index}`}>
                    <AccordionTrigger className="hover:no-underline">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-semibold capitalize bg-primary/10 px-2 py-1 rounded">
                          {prompt.role}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          Prompt {index + 1} of {result.prompts?.length}
                        </span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="text-sm whitespace-pre-wrap bg-background p-3 rounded">
                        {prompt.content}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
