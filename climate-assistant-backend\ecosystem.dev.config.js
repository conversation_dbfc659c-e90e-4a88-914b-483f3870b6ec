module.exports = {
  apps: [
    {
      name: 'climate-assistant-backend-dev',
      script: './dist/main.js',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'development',
        NODE_OPTIONS: '--max-old-space-size=2048',
      },
      watch: true,
      ignore_watch: ['node_modules', 'logs', '.git', 'dist'],
      error_file: './logs/dev-err.log',
      out_file: './logs/dev-out.log',
      log_file: './logs/dev-combined.log',
      merge_logs: true,
      time: false,
      autorestart: true,
      max_restarts: 5,
      restart_delay: 1000,
    },
  ],
};
