# Datapoint Generation Process Documentation

## Overview

This document explains how the Climate Assistant system generates ESRS (European Sustainability Reporting Standards) datapoints based on uploaded documents. The process involves sophisticated AI-powered analysis that links document content to regulatory requirements and generates compliant sustainability reporting content.

## System Architecture

The datapoint generation process involves several key components:

1. **Document Processing**: Uploaded documents are chunked and processed
2. **Document-Datapoint Linking**: AI identifies relationships between document chunks and ESRS datapoints
3. **Context Generation**: Relevant document chunks are assembled into generation context
4. **AI Content Generation**: LLM generates compliant datapoint content
5. **Review and Gap Analysis**: AI reviews generated content for compliance gaps

## Detailed Process Flow

### Phase 1: Document Upload and Processing

When a document is uploaded to the system:

1. **Document Chunking**: Documents are split into manageable chunks (typically by page or content sections)
2. **Status Tracking**: Document status progresses through:
   - `Uploaded` → `Processing` → `LinkingData` → `LinkingDataFinished`

### Phase 2: Document Chunk to Datapoint Linking

**File**: `datapoint-document-chunk.service.ts`

This is the core process that establishes relationships between document content and ESRS requirements:

#### 2.1 Disclosure Requirement Classification

For each document chunk:

```typescript
// AI classifies which ESRS Disclosure Requirements (DR) the chunk relates to
const disclosureRequirementClassificationResponse = 
  await this.llmRateLimitService.handleRequest({
    model: LLM_MODELS['o4-mini'],
    messages: disclosureRequirementClassificationPrompt,
    json: true,
    temperature: 0,
  });
```

**Process**:
- Each document chunk is analyzed by AI
- System identifies which ESRS Disclosure Requirements the content addresses
- Results are cleaned and normalized (removing prefixes like "ESRS 2.")

#### 2.2 Datapoint Classification

For each matched Disclosure Requirement:

```typescript
// AI identifies specific datapoints within the DR that the chunk addresses
const datapointClassificationResponse = 
  await this.llmRateLimitService.handleRequest({
    model: LLM_MODELS['o4-mini'],
    messages: datapointClassificationPrompt,
    json: true,
    temperature: 0,
  });
```

**Process**:
- Analyzes chunk content against specific ESRS datapoints
- Creates `DatapointDocumentChunk` relationships in database
- Updates datapoint custom remarks with relevant policies/actions found

#### 2.3 Batch Processing and Optimization

- **Parallel Processing**: Uses `throat(10)` for chunk processing and `throat(20)` for DR processing
- **Batch Size**: Processes 20 chunks at a time to avoid overwhelming the system
- **Error Handling**: Continues processing even if individual chunks fail
- **Caching**: ESRS Disclosure Requirements are cached for 5 minutes

### Phase 3: Context Generation for Datapoint Generation

**File**: `datapoint-document.service.ts`

When generating content for a specific datapoint:

#### 3.1 Document Chunk Retrieval and Prioritization

```typescript
const documentChunks = datapointRequest.datapointDocumentChunkMap
  .filter((map) => map.active)
  .map((datapointDocumentChunk) => ({
    ...datapointDocumentChunk.documentChunk,
    key_information: datapointDocumentChunk.key_information,
    documentTitle: datapointDocumentChunk.documentChunk.document.name,
    // ... other metadata
  }))
  .sort((a, b) => {
    // Priority: Date (recent first) → Document Type → User Remarks
  });
```

**Document Type Priority**:
1. Business Report
2. Sustainability Report  
3. Materiality Analysis
4. Policy
5. Strategy
6. Other

#### 3.2 Content Reduction for Large Documents

When total document content exceeds 300,000 characters:

```typescript
// AI reduces large chunks to key information only
const reductionResponse = await this.llmRateLimitService.handleRequest({
  model: LLM_MODELS['o4-mini'],
  messages: extractedChunkInformation,
  json: true,
  temperature: 0.2,
});
```

**Process**:
- Chunks over 3,000 characters are analyzed for relevance
- AI extracts only key information related to the specific datapoint
- Results are cached in `key_information` field for future use

#### 3.3 Metadata Integration

Each chunk includes comprehensive metadata:

```typescript
const metadata = `
<Metadata>
Part 1: ${documentChunk.metadataJson}
Part 2:
Document Type: ${document.documentType}
ESRS Category: ${document.esrsCategory.join(', ')}
Year: ${document.year}
Month: ${document.month}
Day: ${document.day}
Remarks: ${document.remarks}
</Metadata>
`;
```

### Phase 4: AI Content Generation

**File**: `datapoint-generation.service.ts`

#### 4.1 Datapoint Type Classification

The system intelligently categorizes datapoints for specialized processing:

**MDR (Minimum Disclosure Requirement) Types**:
- **MDR-A**: Actions and policies (`DP_MDRA_GENERATION`)
- **MDR-P**: Policies and commitments (`DP_MDRP_GENERATION`)
- **MDR-T**: Targets and metrics (`DP_MDRT_GENERATION`)

**Data Type Classifications**:
- **Numeric Types**: `monetary`, `ghgEmissions`, `energy`, `intensity`, etc.
- **Tabular Types**: Any dataType containing 'table'
- **Narrative Types**: `narrative`, `semi-narrative`, etc.
- **Boolean Types**: Simple yes/no responses

#### 4.2 Prompt Chain System

The system uses a sophisticated multi-prompt architecture:

**A-Series Prompts (Primary Generation)**:
- **A1**: Main generation prompt with core variables
- **A2**: Material topics context (conditional)
- **A3**: Non-material topics context (conditional)
- **A4**: Existing report text reference (conditional)
- **A5**: Conditional datapoint instructions (conditional)
- **A6**: Related datapoints context (conditional)

**B-Series Prompts (Enhancement)**:
- **B1**: MDR formatting improvement (MDR types only)

#### 4.3 Materiality Integration

```typescript
const materialTopicsInHierarchy = 
  await this.materialTopicsService.generateHierarchicalListOfTopics({
    topicRelations,
    projectId: datapointRequest.dataRequest.project.id,
    material: true,
  });
```

**Materiality Impact**:
- Material topics receive comprehensive reporting treatment
- Non-material topics get minimal coverage or exclusion notices
- Conditional datapoints reference materiality in gap analysis

#### 4.4 Context Assembly

The final context includes:

```typescript
const unifiedVariables = {
  esrsDatapoint,
  language: project.primaryContentLanguage,
  reportTextGenerationRules: project.reportTextGenerationRules,
  generalCompanyProfile,
  reportingYear: project.reportingYear,
  customUserRemark: datapointRequest.customUserRemark,
  currentContent: datapointRequest.content,
  documentContext: linkedChunksContext,
  otherDatapoints,
  relatedDatapoints,
  materialTopics: materialTopicsInHierarchy,
  nonMaterialTopics: nonMaterialTopicsInHierarchy
};
```

#### 4.5 Two-Stage Generation Process

1. **Primary Generation**: Uses the appropriate prompt chain for initial content
2. **MDR Enhancement**: For MDR datapoints, applies B1 formatting improvements

```typescript
// Primary generation
const predatapointGenerationResponse = await this.llmRateLimiterService.handleRequest({
  model: determinedModel,
  messages: prompts,
  json: true,
  temperature: 0,
});

// MDR enhancement (if applicable)
if (esrsDatapoint.datapointId.includes('MDR')) {
  const improvingFormattingResponse = await this.llmRateLimiterService.handleRequest({
    model: LLM_MODELS['o4-mini'],
    messages: b1Prompt,
    json: true,
    temperature: 0,
  });
}
```

### Phase 5: Citation and Source Management

#### 5.1 Citation Extraction

```typescript
let { reportText: generatedContent, citation: generatedCitation } =
  extractCitationsFromReportTextGeneration(
    datapointGenerationResponse.response['datapoint'],
    documentChunksIndex
  );
```

**Citation Formats**:
- Simple: `<source>["chunk-1", "chunk-2"]</source>`
- Weighted: `<sources-options>{"active":[{"chunk-3":"60%"}], "inactive":[{"chunk-6":"50%"}]}</sources-options>`

#### 5.2 Content Post-Processing

- **HTML Cleanup**: Removes unnecessary `<br>` tags and pre/postfix formatting
- **Citation Mapping**: Converts AI-generated chunk references to actual document IDs
- **Metadata Storage**: Saves generation metadata including model used and token consumption

### Phase 6: Gap Analysis and Review

**File**: `datapoint-generation.service.ts` - `reviewDatapointContentWithAI`

#### 6.1 Gap Analysis Type Detection

**MDR Analysis**:
- Detects MDR datapoints with existing policies (h2 count > 0)
- Uses specialized MDR gap analysis prompts
- Focuses on policy completeness and compliance

**Standard Analysis**:
- General datapoint gap analysis
- Comprehensive requirement checking
- Cross-references with related datapoints

#### 6.2 Materiality-Aware Review

```typescript
if (materialTopicsInHierarchy.length > 0) {
  datapointRequestGapAnalysisChatCompletion.push({
    role: 'user',
    content: `Material Topics: ${materialTopicsInHierarchy.map(topic => topic.topic).join(', ')}`
  });
}
```

#### 6.3 Gap Analysis Response Processing

The AI returns structured gap information:

```typescript
type GapInformation = {
  gap?: string;
  actions?: string[];
  exampleText?: string;
  text?: string;
  disclaimer?: string;
  title?: string;
};

const gapAnalysis: GapInformation & {
  gapIdentified: boolean;
  gaps?: GapInformation[];
} = gapAnalysisCompletionResponse.response;
```

## Performance Optimizations

### 1. Concurrency Control
- **Document Chunk Processing**: `throat(10)` - limits parallel chunk processing
- **DR Processing**: `throat(20)` - limits parallel disclosure requirement processing
- **Batch Processing**: 20 chunks per batch to balance performance and resource usage

### 2. Content Size Limits
- **Document Context**: 300,000 characters maximum
- **Chat Content**: 300,000 characters maximum per message
- **Chunk Reduction Threshold**: 3,000 characters before AI reduction kicks in

### 3. Caching Strategies
- **ESRS Disclosure Requirements**: 5-minute cache for frequently accessed data
- **Key Information**: Cached chunk reductions stored in database
- **Prompt Templates**: Database-stored and reused across generations

### 4. Error Handling
- **Graceful Degradation**: Individual chunk failures don't stop overall processing
- **Retry Logic**: Up to 1 retry for failed datapoint matches
- **Status Tracking**: Comprehensive document status updates throughout process

## Database Schema Integration

### Key Entities

1. **DatapointRequest**: Core entity with generated content and metadata
2. **ESRSDatapoint**: Regulatory requirement definitions
3. **DatapointDocumentChunk**: Links between document chunks and datapoints
4. **DocumentChunk**: Source document fragments with content and metadata
5. **Document**: Original uploaded documents with metadata
6. **ESRSTopic**: Hierarchical topic structure for materiality assessment

### Relationship Flow

```
Document → DocumentChunk → DatapointDocumentChunk → DatapointRequest → ESRSDatapoint
```

## Quality Assurance

### 1. Multi-Model Approach
- **o4-mini**: Used for classification and reduction tasks
- **o3**: Used for primary content generation
- **Temperature 0**: Ensures consistent, deterministic outputs

### 2. Validation Layers
- **Content Validation**: Checks for policy compliance violations
- **Citation Validation**: Ensures proper source attribution
- **Format Validation**: Maintains consistent output formatting

### 3. Human Oversight
- **Super Admin Features**: Enhanced generation capabilities for privileged users
- **Comment System**: AI can add explanatory comments to generated content
- **Action History**: Comprehensive audit trail of all generation activities

## Best Practices

### 1. Document Preparation
- **Structured Documents**: Better results with well-formatted input documents
- **Metadata Completeness**: Include document type, date, and remarks for better prioritization
- **ESRS Category Tagging**: Helps with relevant content identification

### 2. Generation Optimization
- **Materiality Assessment**: Complete materiality assessments improve relevance
- **Custom Remarks**: Specific user guidance enhances output quality
- **Iterative Refinement**: Use existing content as reference for consistency

### 3. Performance Management
- **Batch Processing**: Process multiple datapoints in batches when possible
- **Monitor Token Usage**: Track and optimize LLM API consumption
- **Regular Cache Cleanup**: Maintain cache freshness for optimal performance

## Conclusion

The Climate Assistant's datapoint generation process represents a sophisticated AI-driven approach to sustainability reporting. By combining intelligent document analysis, regulatory knowledge, and advanced language models, the system can automatically generate high-quality, compliant ESRS datapoint content that significantly reduces the manual effort required for sustainability reporting while maintaining accuracy and regulatory compliance.

The multi-stage process ensures that generated content is not only accurate and compliant but also properly sourced and contextually appropriate for each organization's specific circumstances and materiality assessments.
