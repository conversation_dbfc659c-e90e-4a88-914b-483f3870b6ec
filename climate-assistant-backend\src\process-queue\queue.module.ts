import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import {
  ChunkExtractionProcessor,
  ChunkLinkingProcessor,
  DatapointGenerationProcessor,
  DatapointReviewProcessor,
} from './queue.service';
import { JobProcessor } from 'src/types/jobs';
import { DocumentModule } from 'src/document/document.module';
import { DatapointDocumentChunkModule } from 'src/datapoint-document-chunk/datapoint-document-chunk.module';
import { UsersModule } from 'src/users/users.module';
import { DatapointRequestModule } from 'src/datapoint/datapoint-request.module';
import { DataRequestModule } from 'src/data-request/data-request.module';
import { DEFAULT_QUEUE_JOB_OPTIONS } from 'src/util/queue.config';

@Module({
  imports: [
    BullModule.registerQueue(
      { 
        name: JobProcessor.ChunkExtraction,
        defaultJobOptions: DEFAULT_QUEUE_JOB_OPTIONS,
      },
      { 
        name: JobProcessor.ChunkDpLinking,
        defaultJobOptions: DEFAULT_QUEUE_JOB_OPTIONS,
      },
      { 
        name: JobProcessor.DatapointGeneration,
        defaultJobOptions: DEFAULT_QUEUE_JOB_OPTIONS,
      },
      { 
        name: JobProcessor.DatapointReview,
        defaultJobOptions: DEFAULT_QUEUE_JOB_OPTIONS,
      }
    ),
    DocumentModule,
    UsersModule,
    DatapointDocumentChunkModule,
    DatapointRequestModule,
    DataRequestModule,
  ],
  providers: [
    ChunkExtractionProcessor,
    ChunkLinkingProcessor,
    DatapointGenerationProcessor,
    DatapointReviewProcessor,
  ],
  exports: [],
})
export class ProcessQueueModule {}
