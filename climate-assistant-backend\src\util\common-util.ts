export function isTextPresentInHTML(htmlString: string): boolean {
  if (!htmlString) return false;
  // Remove HTML tags using a regular expression
  const textContent: string = htmlString.replace(/<[^>]*>/g, '').trim();
  return textContent.length > 0;
}

export function chunkArray<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

//At times, we need to check if the request is for data generation or not.
// This function helps in identifying that. In such case, we append '-generation' to the request id.
export function isRequestForDataGenerationType(requestId: string) {
  return requestId.endsWith('-generation');
}

export function getGenerationIdFromRequestId(requestId: string) {
  return requestId.split('-generation')[0];
}
