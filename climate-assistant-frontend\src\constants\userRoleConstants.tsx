export enum SystemPermissions {
  // Datapoints
  CREATE_DATAPOINTS = 'CREATE_DATAPOINTS',
  EDIT_DATAPOINTS = 'EDIT_DATAPOINTS',
  APPROVE_DATAPOINTS = 'APPROVE_DATAPOINTS',
  MARK_DATAPOINTS = 'MARK_DATAPOINTS',
  EXPORT_DATAPOINTS_EXCEL = 'EXPORT_DATAPOINTS_EXCEL',

  // Disclosure Requirements
  CREATE_DRS = 'CREATE_DRS',
  EDIT_DRS = 'EDIT_DRS',
  APPROVE_DRS = 'APPROVE_DRS',
  MARK_DRS = 'MARK_DRS',
  ASSIGN_USERS_DRS = 'ASSIGN_USERS_DRS',
  EXPORT_DR_EXCEL = 'EXPORT_DR_EXCEL',

  // GAP Analysis
  GAP_ANALYSIS_DATAPOINTS = 'GAP_ANALYSIS_DATAPOINTS',
  GAP_ANALYSIS_DATAPOINTS_REVIEW = 'GAP_ANALYSIS_DATAPOINTS_REVIEW',
  GAP_ANALYSIS_DRS = 'GAP_ANALYSIS_DRS',
  GAP_ANALYSIS_DRS_REVIEW = 'GAP_ANALYSIS_DRS_REVIEW',

  // Comments
  CREATE_COMMENTS = 'CREATE_COMMENTS',
  EDIT_COMMENTS = 'EDIT_COMMENTS',

  // Administrative & Workspace
  CREATE_WORKSPACE = 'CREATE_WORKSPACE',
  SWITCH_WORKSPACE = 'SWITCH_WORKSPACE',
  EDIT_WORKSPACE_SETTINGS = 'EDIT_WORKSPACE_SETTINGS',
  EDIT_PROJECT_SETTINGS = 'EDIT_PROJECT_SETTINGS',
  CHANGE_USER_ROLES = 'CHANGE_USER_ROLES',
  INVITE_USERS = 'INVITE_USERS',

  // General System Access
  ACCESS_LEGAL_TEXT = 'ACCESS_LEGAL_TEXT',
  EXPORT_FINAL_REPORT = 'EXPORT_FINAL_REPORT',

  // Materiality Setting
  CHANGE_MATERIALITY_SETTING = 'CHANGE_MATERIALITY_SETTING',
  ACCESS_MATERIALITY_SETTING = 'ACCESS_MATERIALITY_SETTING',

  // Version History
  ACCESS_VERSION_HISTORY = 'ACCESS_VERSION_HISTORY',
  RESTORE_VERSION_HISTORY = 'RESTORE_VERSION_HISTORY',
}
