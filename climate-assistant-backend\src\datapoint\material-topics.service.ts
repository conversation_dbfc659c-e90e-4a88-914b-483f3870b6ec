import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ESRSTopicDatapoint } from 'src/knowledge-base/entities/esrs-topic-datapoint.entity';
import { ProjectService } from 'src/project/project.service';
import { ESRSTopic } from 'src/knowledge-base/entities/esrs-topic.entity';
import { DatapointRequestData } from './entities/datapoint-request.dto';

@Injectable()
export class MaterialTopicsService {
  constructor(
    @InjectRepository(ESRSTopicDatapoint)
    private readonly esrsTopicDatapointRepository: Repository<ESRSTopicDatapoint>,
    private readonly projectService: ProjectService
  ) {}

  async loadMaterialTopics(
    datapointRequest: DatapointRequestData
  ): Promise<any> {
    const { id } = datapointRequest.esrsDatapoint;
    const projectId = datapointRequest.dataRequest.projectId;
    const esrsTopicDatapoint: ESRSTopicDatapoint[] =
      await this.esrsTopicDatapointRepository.find({
        where: { esrsDatapointId: id },
        relations: ['topic'],
      });
    const validMaterialTopics = await this.generateHierarchicalListOfTopics({
      topicRelations: esrsTopicDatapoint,
      projectId,
      material: true,
    });
    return validMaterialTopics;
  }

  async generateHierarchicalListOfTopics({
    topicRelations,
    projectId,
    material,
  }: {
    topicRelations: ESRSTopicDatapoint[];
    projectId: string;
    material: boolean;
  }) {
    const projectEsrsTopics = await this.projectService.getProjectEsrsTopics({
      projectId,
      esrsTopicIds: topicRelations.map((tr) => tr.topic.id),
    });

    // 1. Filter to get only the "matching" topics
    const filteredTopics = projectEsrsTopics.filter(
      (mt) => Boolean(mt.active) === material
    );
    const filteredTopicIds = new Set(
      filteredTopics.map((mt) => mt.esrsTopicId)
    );

    // 2. Build a map of ESRSTopic => only if it is in filteredTopicIds
    const topicsById = new Map<number, ESRSTopic>();
    for (const tr of topicRelations) {
      if (filteredTopicIds.has(tr.topic.id)) {
        topicsById.set(tr.topic.id, { ...tr.topic, children: [] });
      }
    }

    // 3. If the parent is missing forcibly nullify the parent so it can become top-level
    // In this case, anscestors are not included in the list
    for (const [, topic] of topicsById.entries()) {
      if (topic.parentId && !topicsById.has(topic.parentId)) {
        topic.parentId = null;
      }
    }

    // 4. Build the children arrays
    for (const [, topic] of topicsById.entries()) {
      if (topic.parentId && topicsById.has(topic.parentId)) {
        topicsById.get(topic.parentId).children.push(topic);
      }
    }

    // 5. Pick only roots for the return array
    const hierarchicalTopics = [...topicsById.values()].filter(
      (t) => !t.parentId
    );

    return hierarchicalTopics;
  }
}
