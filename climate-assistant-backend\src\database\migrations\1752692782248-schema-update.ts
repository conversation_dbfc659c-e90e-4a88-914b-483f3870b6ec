import { MigrationInterface, QueryRunner } from 'typeorm';

// This migration adds an evaluatorComment column to both datapoint_generation and data_request_generation tables.
// The column is of type VARCHAR and allows NULL values.
// This is useful for storing comments from evaluators regarding the generated content.
export class AddEvaluatorCommentToGenerations1752692782248
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add evaluatorComment to datapoint_generation table
    await queryRunner.query(`
      ALTER TABLE "datapoint_generation" 
      ADD COLUMN "evaluatorComment" VARCHAR NULL
    `);

    // Add evaluatorComment to data_request_generation table
    await queryRunner.query(`
      ALTER TABLE "data_request_generation" 
      ADD COLUMN "evaluatorComment" VARCHAR NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove evaluatorComment from datapoint_generation table
    await queryRunner.query(`
      ALTER TABLE "datapoint_generation" 
      DROP COLUMN "evaluatorComment"
    `);

    // Remove evaluatorComment from data_request_generation table
    await queryRunner.query(`
      ALTER TABLE "data_request_generation" 
      DROP COLUMN "evaluatorComment"
    `);
  }
}
