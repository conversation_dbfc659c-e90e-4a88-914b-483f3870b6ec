import {
  <PERSON>um<PERSON>,
  CreateDate<PERSON><PERSON><PERSON>n,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { DataRequest } from './data-request.entity';
import { User } from '../../users/entities/user.entity';

export enum dataRequestGenerationStatus {
  Pending = 'pending',
  Approved = 'approved',
  Rejected = 'rejected',
}

//This entity is used to store the temporary generated datapoint
// text until the admin user approves the generated text, after that,
// it's moved to the datapoint request entity in content field
@Entity()
export class DataRequestGeneration {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'json' })
  data: any;

  @Column({ type: 'uuid', nullable: true })
  evaluatorId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'evaluatorId' })
  evaluator: User;

  @Column({ nullable: true, type: Date, default: null })
  evaluatedAt: Date;

  @Column({ type: 'varchar', nullable: true })
  evaluatorComment: string;

  @CreateDateColumn({ nullable: true })
  createdAt: Date;

  @ManyToOne(
    () => DataRequest,
    (dataRequest) => dataRequest.dataRequestGenerations
  )
  @JoinColumn({ name: 'dataRequestId' })
  dataRequest: DataRequest;

  @Column({
    type: 'enum',
    enum: dataRequestGenerationStatus,
    default: dataRequestGenerationStatus.Pending,
  })
  status: dataRequestGenerationStatus;
}
