import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * This migration updates the data_request_generation_status_enum to add a new 'minorChanges' status.
 */
export class SchemaUpdate1753664598489 implements MigrationInterface {
  name = 'SchemaUpdate1753664598489';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."data_request_generation_status_enum" RENAME TO "data_request_generation_status_enum_old"`
    );
    await queryRunner.query(
      `CREATE TYPE "public"."data_request_generation_status_enum" AS ENUM('pending', 'approved', 'rejected', 'minorChanges')`
    );
    await queryRunner.query(
      `ALTER TABLE "data_request_generation" ALTER COLUMN "status" DROP DEFAULT`
    );
    await queryRunner.query(
      `ALTER TABLE "data_request_generation" ALTER COLUMN "status" TYPE "public"."data_request_generation_status_enum" USING "status"::"text"::"public"."data_request_generation_status_enum"`
    );
    await queryRunner.query(
      `ALTER TABLE "data_request_generation" ALTER COLUMN "status" SET DEFAULT 'pending'`
    );
    await queryRunner.query(
      `DROP TYPE "public"."data_request_generation_status_enum_old"`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."data_request_generation_status_enum_old" AS ENUM('approved', 'pending', 'rejected')`
    );
    await queryRunner.query(
      `ALTER TABLE "data_request_generation" ALTER COLUMN "status" DROP DEFAULT`
    );
    await queryRunner.query(
      `ALTER TABLE "data_request_generation" ALTER COLUMN "status" TYPE "public"."data_request_generation_status_enum_old" USING "status"::"text"::"public"."data_request_generation_status_enum_old"`
    );
    await queryRunner.query(
      `ALTER TABLE "data_request_generation" ALTER COLUMN "status" SET DEFAULT 'pending'`
    );
    await queryRunner.query(
      `DROP TYPE "public"."data_request_generation_status_enum"`
    );
    await queryRunner.query(
      `ALTER TYPE "public"."data_request_generation_status_enum_old" RENAME TO "data_request_generation_status_enum"`
    );
  }
}
