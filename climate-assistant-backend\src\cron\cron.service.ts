import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';
import { InjectRepository } from '@nestjs/typeorm';
import { In, LessThan, Repository } from 'typeorm';
import { Document, DocumentStatus } from '../document/entities/document.entity';
import { JobProcessor, JobQueue } from 'src/types/jobs';
import { DEFAULT_JOB_CONFIG } from 'src/util/queue.config';
import { WorkerLogger } from 'src/shared/logger.service';

@Injectable()
export class CronService {
  private readonly isMainWorker = process.env.INSTANCE_ID === '0' || !process.env.INSTANCE_ID;
  private readonly workerId = process.env.INSTANCE_ID || process.pid;

  constructor(
    @InjectQueue(JobProcessor.ChunkExtraction)
    private readonly chunkExtractionQueue: Queue,
    @InjectQueue(JobProcessor.ChunkDpLinking)
    private readonly chunkLinkingQueue: Queue,
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>
  ) {}

  private readonly logger = new WorkerLogger(CronService.name);

  @Cron('*/10 * * * *') // every 10 minutes
  async checkUnprocessedDocuments() {
    // Only run on main worker to prevent duplication
    if (!this.isMainWorker) {
      this.logger.debug(`Worker ${this.workerId} skipping cron - not main worker`);
      return;
    }
    
    this.logger.log(`Main worker ${this.workerId} checking for unprocessed documents...`);

    const includedStatuses = [
      DocumentStatus.NotProcessed,
      DocumentStatus.ExtractingData,
      DocumentStatus.DataExtractionFinished,
      DocumentStatus.LinkingData,
    ];

    const unprocessedDocuments = await this.documentRepository.find({
      where: {
        status: In(includedStatuses),
        createdAt: LessThan(new Date(Date.now() - 10 * 60 * 1000)), // 10 minutes ago
      },
    });

    for (const document of unprocessedDocuments) {
      this.logger.log(`Processing document: ${document.id}`);

      try {
        switch (document.status) {
          case DocumentStatus.NotProcessed:
          case DocumentStatus.ExtractingData:
            this.logger.log(
              `Adding document to extraction queue: ${document.id}`
            );
            await this.chunkExtractionQueue.add(
              JobQueue.ChunkExtract,
              {
                documentId: document.id,
              },
              {
                ...DEFAULT_JOB_CONFIG,
                jobId: `chunkExtraction-${document.id}`,
              }
            );
            this.documentRepository.update(document.id, {
              status: DocumentStatus.QueuedForExtraction,
            });
            break;

          case DocumentStatus.DataExtractionFinished:
          case DocumentStatus.LinkingData:
            this.logger.log(`Adding document to linking queue: ${document.id}`);
            await this.chunkLinkingQueue.add(
              JobQueue.ChunkDpLink,
              {
                documentId: document.id,
              },
              {
                ...DEFAULT_JOB_CONFIG,
                jobId: `chunkLinking-${document.id}`,
              }
            );
            this.documentRepository.update(document.id, {
              status: DocumentStatus.QueuedForLinking,
            });
            break;

          default:
            this.logger.log(
              `Invalid status for document: ${document.id}: ${document.status}`
            );
            break;
        }
      } catch (error) {
        this.logger.error(error);
      }
    }
  }
}
