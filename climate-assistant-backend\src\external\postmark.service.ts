import { Injectable } from '@nestjs/common';
import { ServerClient } from 'postmark';
import {
  type TemplatedMessage,
  type Message,
  type MessageSendingResponse,
  LinkTrackingOptions,
} from 'postmark/dist/client/models';

@Injectable()
export class PostmarkService {
  private readonly client: ServerClient;

  constructor() {
    this.client = new ServerClient(process.env.POSTMARK_SERVER_TOKEN);
  }

  /**
   * Send templated email using Postmark template alias
   */
  async sendTemplatedEmail({
    templateAlias,
    to,
    from = '<EMAIL>',
    cc,
    bcc,
    replyTo,
    tag,
    metadata,
    headers,
    trackOpens = true,
    trackLinks = LinkTrackingOptions.None,
    templateModel,
    messageStream = 'outbound',
  }: {
    templateAlias: string;
    to: string;
    from?: string;
    cc?: string;
    bcc?: string;
    replyTo?: string;
    tag?: string;
    metadata?: Record<string, string>;
    headers?: Array<{ Name: string; Value: string }>;
    trackOpens?: boolean;
    trackLinks?: LinkTrackingOptions;
    templateModel: Record<string, any>;
    messageStream?: string;
  }): Promise<MessageSendingResponse> {
    const message: TemplatedMessage = {
      TemplateAlias: templateAlias,
      To: to,
      From: from,
      TemplateModel: templateModel,
      MessageStream: messageStream,
      TrackOpens: trackOpens,
      TrackLinks: trackLinks,
    };

    // Add optional fields only if provided
    if (cc) message.Cc = cc;
    if (bcc) message.Bcc = bcc;
    if (replyTo) message.ReplyTo = replyTo;
    if (tag) message.Tag = tag;
    if (metadata) message.Metadata = metadata;
    if (headers) message.Headers = headers;

    try {
      return await this.client.sendEmailWithTemplate(message);
    } catch (error) {
      console.error(
        'Error sending templated email:',
        error.statusCode,
        error.message
      );
      throw new Error(`Failed to send templated email: ${error.message}`);
    }
  }

  /**
   * Send raw HTML email without templates
   */
  async sendRawHtmlEmail({
    to,
    subject,
    htmlBody,
    textBody,
    from = '<EMAIL>',
    cc,
    bcc,
    replyTo,
    tag,
    metadata,
    headers,
    trackOpens = true,
    trackLinks = LinkTrackingOptions.None,
    attachments,
    messageStream = 'outbound',
  }: {
    to: string;
    subject: string;
    htmlBody: string;
    textBody?: string;
    from?: string;
    cc?: string;
    bcc?: string;
    replyTo?: string;
    tag?: string;
    metadata?: Record<string, string>;
    headers?: Array<{ Name: string; Value: string }>;
    trackOpens?: boolean;
    trackLinks?: LinkTrackingOptions;
    attachments?: Array<{
      Name: string;
      Content: string;
      ContentType: string;
      ContentID: string;
    }>;
    messageStream?: string;
  }): Promise<MessageSendingResponse> {
    const message: Message = {
      To: to,
      From: from,
      Subject: subject,
      HtmlBody: htmlBody,
      MessageStream: messageStream,
      TrackOpens: trackOpens,
      TrackLinks: trackLinks,
    };

    // Add optional fields only if provided
    if (textBody) message.TextBody = textBody;
    if (cc) message.Cc = cc;
    if (bcc) message.Bcc = bcc;
    if (replyTo) message.ReplyTo = replyTo;
    if (tag) message.Tag = tag;
    if (metadata) message.Metadata = metadata;
    if (headers) message.Headers = headers;
    if (attachments) message.Attachments = attachments;

    try {
      return await this.client.sendEmail(message);
    } catch (error) {
      console.error(
        'Error sending raw HTML email:',
        error.statusCode,
        error.message
      );
      throw new Error(`Failed to send raw HTML email: ${error.message}`);
    }
  }

  /**
   * Helper method for common email types
   */
  async sendPasswordReset({
    to,
    name,
    resetLink,
  }: {
    to: string;
    name: string;
    resetLink: string;
  }): Promise<MessageSendingResponse> {
    return this.sendTemplatedEmail({
      templateAlias: 'password-reset',
      to,
      templateModel: {
        name,
        reset_link: resetLink,
      },
      tag: 'password-reset',
      metadata: {
        type: 'transactional',
        category: 'authentication',
      },
    });
  }

  /**
   * Helper method for workspace invitations
   */
  async sendWorkspaceInvite({
    to,
    inviterName,
    workspaceName,
    inviteLink,
  }: {
    to: string;
    inviterName: string;
    workspaceName: string;
    inviteLink: string;
  }): Promise<MessageSendingResponse> {
    return this.sendTemplatedEmail({
      templateAlias: 'workspace-invite',
      to,
      templateModel: {
        inviter_name: inviterName,
        workspace_name: workspaceName,
        invite_link: inviteLink,
      },
      tag: 'workspace-invite',
      metadata: {
        type: 'transactional',
        category: 'collaboration',
      },
    });
  }
}
