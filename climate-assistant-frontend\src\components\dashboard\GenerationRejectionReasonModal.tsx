import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '../ui/button';
import { Textarea } from '../ui/textarea';
import { Label } from '../ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

const rejectionReasonSchema = z.object({
  // Coverage
  coverage_status: z.enum(['complete', 'partial', 'missing']).optional(),
  coverage_comment: z.string().optional(),

  // Accuracy
  accuracy_status: z
    .enum(['accurate', 'minor_inferred', 'major_hallucination'])
    .optional(),
  accuracy_comment: z.string().optional(),

  // Traceability
  traceability_status: z
    .enum(['fully_traced', 'partially_unclear', 'untraceable'])
    .optional(),
  traceability_comment: z.string().optional(),

  // Clarity & Compliance
  clarity_status: z
    .enum(['clear_compliant', 'minor_issues', 'major_rewrite'])
    .optional(),
  clarity_comment: z.string().optional(),

  // Gap Analysis Quality
  gap_quality_status: z
    .enum(['correct', 'partially_useful', 'misleading'])
    .optional(),
  gap_quality_comment: z.string().optional(),

  // Effort Level
  effort_score: z.enum(['1', '2', '3', '4', '5']).optional(),
  effort_comment: z.string().optional(),

  // Overall comment (stored as JSON string)
  comment: z.string().optional(),
});

export type RejectGenerationFormData = z.infer<typeof rejectionReasonSchema>;

export function GenerationRejectionReasonModal({
  open,
  setOpen,
  callback,
  generationId,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  callback: (data: RejectGenerationFormData, generationId: string) => void;
  generationId: string;
}) {
  const { register, handleSubmit } = useForm<RejectGenerationFormData>({
    defaultValues: {
      coverage_status: undefined,
      coverage_comment: '',
      accuracy_status: undefined,
      accuracy_comment: '',
      traceability_status: undefined,
      traceability_comment: '',
      clarity_status: undefined,
      clarity_comment: '',
      gap_quality_status: undefined,
      gap_quality_comment: '',
      effort_score: undefined,
      effort_comment: '',
      comment: '',
    },
    resolver: zodResolver(rejectionReasonSchema),
  });

  const onSubmit = async (data: RejectGenerationFormData) => {
    // Create evaluation object without the comment field
    const evaluationData = {
      coverage: {
        status: data.coverage_status,
        comment: data.coverage_comment,
      },
      accuracy: {
        status: data.accuracy_status,
        comment: data.accuracy_comment,
      },
      traceability: {
        status: data.traceability_status,
        comment: data.traceability_comment,
      },
      clarity: {
        status: data.clarity_status,
        comment: data.clarity_comment,
      },
      gap_quality: {
        status: data.gap_quality_status,
        comment: data.gap_quality_comment,
      },
      effort: {
        score: data.effort_score,
        comment: data.effort_comment,
      },
    };

    // Store the evaluation as JSON in the comment field
    const formDataWithJson = {
      comment: JSON.stringify(evaluationData),
    };

    callback(formDataWithJson, generationId);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-6">
          <DialogHeader>
            <DialogTitle>Generation Evaluation & Rejection</DialogTitle>
            <DialogDescription>
              Please evaluate the generation across all criteria before
              rejecting.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-6">
            {/* Coverage */}
            <div className="grid gap-3">
              <h3 className="font-semibold text-sm">1️⃣ Coverage</h3>
              <p className="text-sm text-gray-600">
                Does the output fully address all mandatory requirements or
                scoring criteria & question requirements?
              </p>
              <div className="grid gap-2">
                <Label htmlFor="coverage_status">Status:</Label>
                <select
                  {...register('coverage_status')}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                >
                  <option value="">Select status...</option>
                  <option value="complete">✔️ Complete</option>
                  <option value="partial">⚠️ Partial</option>
                  <option value="missing">❌ Missing</option>
                </select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="coverage_comment">Comment:</Label>
                <Textarea
                  id="coverage_comment"
                  placeholder='If "Partial" or "Missing," specify which sections or criteria are absent.'
                  rows={2}
                  {...register('coverage_comment')}
                />
              </div>
            </div>

            {/* Accuracy */}
            <div className="grid gap-3">
              <h3 className="font-semibold text-sm">2️⃣ Accuracy</h3>
              <p className="text-sm text-gray-600">
                Does every statement come directly from the provided datapoints
                or linked evidence?
              </p>
              <div className="grid gap-2">
                <Label htmlFor="accuracy_status">Status:</Label>
                <select
                  {...register('accuracy_status')}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                >
                  <option value="">Select status...</option>
                  <option value="accurate">✔️ Accurate</option>
                  <option value="minor_inferred">
                    ⚠️ Minor inferred content
                  </option>
                  <option value="major_hallucination">
                    ❌ Major hallucination
                  </option>
                </select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="accuracy_comment">Comment:</Label>
                <Textarea
                  id="accuracy_comment"
                  placeholder="Quote or describe the suspect statement."
                  rows={2}
                  {...register('accuracy_comment')}
                />
              </div>
            </div>

            {/* Traceability */}
            <div className="grid gap-3">
              <h3 className="font-semibold text-sm">3️⃣ Traceability</h3>
              <p className="text-sm text-gray-600">
                Are all factual statements correctly cited to the right
                datapoint ID or document chunk?
              </p>
              <div className="grid gap-2">
                <Label htmlFor="traceability_status">Status:</Label>
                <select
                  {...register('traceability_status')}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                >
                  <option value="">Select status...</option>
                  <option value="fully_traced">✔️ Fully traced</option>
                  <option value="partially_unclear">
                    ⚠️ Partially unclear
                  </option>
                  <option value="untraceable">
                    ❌ Untraceable / wrong citation
                  </option>
                </select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="traceability_comment">Comment:</Label>
                <Textarea
                  id="traceability_comment"
                  placeholder="Note any missing or incorrect references."
                  rows={2}
                  {...register('traceability_comment')}
                />
              </div>
            </div>

            {/* Clarity & Compliance */}
            <div className="grid gap-3">
              <h3 className="font-semibold text-sm">
                4️⃣ Clarity, Style & Compliance
              </h3>
              <p className="text-sm text-gray-600">
                Is the language clear, legally sound, and suitable for
                CSRD/EcoVadis submissions and in line with the respective
                workspace language?
              </p>
              <div className="grid gap-2">
                <Label htmlFor="clarity_status">Status:</Label>
                <select
                  {...register('clarity_status')}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                >
                  <option value="">Select status...</option>
                  <option value="clear_compliant">✔️ Clear & compliant</option>
                  <option value="minor_issues">⚠️ Minor phrasing issues</option>
                  <option value="major_rewrite">❌ Major rewrite needed</option>
                </select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="clarity_comment">Comment:</Label>
                <Textarea
                  id="clarity_comment"
                  placeholder="Suggest clearer phrasing or structural edits."
                  rows={2}
                  {...register('clarity_comment')}
                />
              </div>
            </div>

            {/* Gap Analysis Quality */}
            <div className="grid gap-3">
              <h3 className="font-semibold text-sm">5️⃣ Gap Analysis Quality</h3>
              <p className="text-sm text-gray-600">
                (Only if gap analysis is included, e.g. EcoVadis) Are identified
                gaps and suggested actions appropriate and precise?
              </p>
              <div className="grid gap-2">
                <Label htmlFor="gap_quality_status">Status:</Label>
                <select
                  {...register('gap_quality_status')}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                >
                  <option value="">Select status...</option>
                  <option value="correct">✔️ Correct</option>
                  <option value="partially_useful">⚠️ Partially useful</option>
                  <option value="misleading">❌ Misleading or vague</option>
                </select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="gap_quality_comment">Comment:</Label>
                <Textarea
                  id="gap_quality_comment"
                  placeholder="Note any irrelevant or missing recommendations."
                  rows={2}
                  {...register('gap_quality_comment')}
                />
              </div>
            </div>

            {/* Effort Level */}
            <div className="grid gap-3">
              <h3 className="font-semibold text-sm">6️⃣ Effort Level</h3>
              <p className="text-sm text-gray-600">
                How much editing did you need to do?
              </p>
              <div className="grid gap-2">
                <Label htmlFor="effort_score">Score:</Label>
                <select
                  {...register('effort_score')}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                >
                  <option value="">Select score...</option>
                  <option value="1">1 – No edits</option>
                  <option value="2">2 – Minor edits</option>
                  <option value="3">3 – Moderate rewrites</option>
                  <option value="4">4 – Substantial rewrites</option>
                  <option value="5">5 – Major rewrite</option>
                </select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="effort_comment">Comment:</Label>
                <Textarea
                  id="effort_comment"
                  placeholder="(Optional) Where time was lost or why it was onerous."
                  rows={2}
                  {...register('effort_comment')}
                />
              </div>
            </div>

            <DialogDescription className="pt-3">
              <span className="text-yellow-600">
                WARNING: This rejection cannot be undone.
              </span>
            </DialogDescription>
          </div>

          <DialogFooter>
            <Button type="submit">Submit Rejection</Button>
            <Button
              type="button"
              onClick={() => setOpen(false)}
              variant="secondary"
            >
              Cancel
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
