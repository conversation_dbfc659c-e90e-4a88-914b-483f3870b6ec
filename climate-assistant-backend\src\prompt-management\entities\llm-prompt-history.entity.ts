import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { LlmPrompt } from './llm-prompt.entity';
import { User } from '../../users/entities/user.entity';

@Entity('llm_prompt_history')
export class LlmPromptHistory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  promptId: string;

  @ManyToOne(() => LlmPrompt, (prompt) => prompt.history)
  @JoinColumn({ name: 'promptId' })
  prompt: LlmPrompt;

  @Column({
    type: 'text',
  })
  oldPrompt: string;

  @Column({
    type: 'text',
  })
  newPrompt: string;

  @Column({
    type: 'jsonb',
    nullable: true,
  })
  changes: Record<string, any>;

  @Column({ type: 'uuid', nullable: true })
  changedById: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'changedById' })
  changedBy: User;

  @CreateDateColumn()
  createdAt: Date;
}
