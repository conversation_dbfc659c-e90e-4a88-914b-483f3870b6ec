import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  UseGuards,
  Req,
  BadRequestException,
} from '@nestjs/common';
import { Roles } from 'src/auth/roles.decorator';
import { USER_ROLES, LLM_MODELS, SystemPermissions } from 'src/constants';
import { PromptManagementService } from './prompt-management.service';
import {
  CreatePromptDto,
  UpdatePromptDto,
  TestPromptDto,
  TestGapAnalysisDto,
} from './dto/prompt.dto';
import { DatapointRequestService } from 'src/datapoint/datapoint-request.service';
import { PermissionGuard } from 'src/auth/guard/permission.guard';
import { Permissions } from 'src/auth/decorators/permissions.decorator';
import { DatapointGenerationService } from 'src/datapoint/datapoint-generation.service';

@Controller('admin/prompts')
@UseGuards(PermissionGuard)
@Roles(USER_ROLES.SuperAdmin)
export class PromptManagementController {
  constructor(
    private readonly promptService: PromptManagementService,
    private readonly datapointGenerationService: DatapointGenerationService,
    private readonly datapointService: DatapointRequestService
  ) {}

  @Permissions(SystemPermissions.VIEW_ALL_PROMPTS)
  @Get()
  async findAll() {
    return this.promptService.findAll();
  }

  @Permissions(SystemPermissions.VIEW_PROMPT_MODELS)
  @Get('models')
  async getAvailableModels() {
    return Object.values(LLM_MODELS);
  }

  @Permissions(SystemPermissions.VIEW_PROMPTS_BY_FEATURE)
  @Get('feature/:feature')
  async findByFeature(@Param('feature') feature: string) {
    return this.promptService.findByFeature(feature);
  }

  @Permissions(SystemPermissions.VIEW_PROMPT_DETAILS)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.promptService.findOne(id);
  }

  @Permissions(SystemPermissions.VIEW_PROMPT_METADATA)
  @Get(':id/metadata')
  async getPromptWithMetadata(@Param('id') id: string) {
    return this.promptService.getPromptWithMetadata(id);
  }

  @Permissions(SystemPermissions.VIEW_PROMPT_HISTORY)
  @Get(':id/history')
  async getHistory(@Param('id') id: string) {
    return this.promptService.getHistory(id);
  }

  @Permissions(SystemPermissions.CREATE_PROMPTS)
  @Post()
  async create(@Body() createPromptDto: CreatePromptDto) {
    // Validate prompt variables
    const isValid = await this.promptService.validatePromptVariables(
      createPromptDto.prompt,
      createPromptDto.requiredVariables
    );

    if (!isValid) {
      throw new BadRequestException(
        'Prompt is missing required variables. Please ensure all variables defined in requiredVariables are present in the prompt text as {{variableName}}'
      );
    }

    return this.promptService.create(createPromptDto);
  }

  @Permissions(SystemPermissions.EDIT_PROMPTS)
  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updatePromptDto: UpdatePromptDto,
    @Req() req: any
  ) {
    // If prompt is being updated, validate variables
    if (updatePromptDto.prompt) {
      const existingPrompt = await this.promptService.findOne(id);
      const requiredVariables =
        updatePromptDto.requiredVariables || existingPrompt.requiredVariables;

      const isValid = await this.promptService.validatePromptVariables(
        updatePromptDto.prompt,
        requiredVariables
      );

      if (!isValid) {
        throw new BadRequestException(
          'Prompt is missing required variables. Please ensure all variables defined in requiredVariables are present in the prompt text as {{variableName}}'
        );
      }
    }

    return this.promptService.update(id, updatePromptDto, req.user.id);
  }

  @Permissions(SystemPermissions.TEST_PROMPTS)
  @Post('test')
  async testPrompt(@Body() testPromptDto: TestPromptDto, @Req() req: any) {
    // Call the datapoint service with dry run mode
    const result =
      await this.datapointGenerationService.generateDatapointContentWithControlledAI(
        {
          datapointRequestId: testPromptDto.datapointRequestId,
          userId: req.user.id,
          workspaceId: req.user.workspaceId,
          useExistingReportTextForReference:
            testPromptDto.useExistingReportTextForReference || false,
          dryRun: true,
          testPrompts: testPromptDto.prompts,
        }
      );

    return result;
  }

  @Permissions(SystemPermissions.TEST_PROMPTS)
  @Post('test-dp-gaps')
  async testGapAnalysisPrompt(
    @Body() testPromptDto: TestGapAnalysisDto,
    @Req() req: any
  ) {
    // Call the datapoint service with dry run mode for gap analysis
    const result =
      await this.datapointGenerationService.reviewDatapointContentWithControlledAI(
        {
          datapointRequestId: testPromptDto.datapointRequestId,
          userId: req.user.id,
          workspaceId: req.user.workspaceId,
          dryRun: true,
          testPrompts: testPromptDto.prompts,
        }
      );

    return result;
  }

  @Permissions(SystemPermissions.VIEW_ALL_DATAPOINTS)
  @Get('/all/workspace')
  async getAllDatapointsForWorkspace(@Req() req) {
    const workspaceId = req.user.workspaceId;
    return await this.datapointService.findAllDatapointsForWorkspace(
      workspaceId
    );
  }
}
