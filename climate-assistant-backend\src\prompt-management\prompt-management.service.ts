import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LlmPrompt } from './entities/llm-prompt.entity';
import { LlmPromptHistory } from './entities/llm-prompt-history.entity';
import { CreatePromptDto, UpdatePromptDto } from './dto/prompt.dto';
import * as Handlebars from 'handlebars';

@Injectable()
export class PromptManagementService {
  // Shared constants for Handlebars helper names - used consistently across methods
  private readonly HELPER_NAMES = {
    JSON: 'json',
    LOOKUP: 'lookup',
    CONTAINS: 'contains',
    EQ: 'eq',
    GENERATION_LANGUAGE: 'generationLanguage',
    IS_TRUTHY: 'isTruthy',
    LANGUAGE_NAME: 'languageName',
  } as const;

  constructor(
    @InjectRepository(LlmPrompt)
    private readonly promptRepository: Repository<LlmPrompt>,
    @InjectRepository(LlmPromptHistory)
    private readonly historyRepository: Repository<LlmPromptHistory>
  ) {}

  async findAll(): Promise<LlmPrompt[]> {
    return this.promptRepository.find({
      where: { isActive: true },
      order: { feature: 'ASC', chainIdentifier: 'ASC' },
    });
  }

  async findByFeature(feature: string): Promise<LlmPrompt[]> {
    return this.promptRepository.find({
      where: { feature, isActive: true },
      order: { chainIdentifier: 'ASC' },
    });
  }

  async findOne(id: string): Promise<LlmPrompt> {
    const prompt = await this.promptRepository.findOne({ where: { id } });
    if (!prompt) {
      throw new NotFoundException(`Prompt with ID ${id} not found`);
    }
    return prompt;
  }

  async findByFeatureAndChain(
    feature: string,
    chainIdentifier: string
  ): Promise<LlmPrompt> {
    const prompt = await this.promptRepository.findOne({
      where: { feature, chainIdentifier, isActive: true },
    });
    if (!prompt) {
      throw new NotFoundException(
        `Prompt for feature ${feature} with chain ${chainIdentifier} not found`
      );
    }
    return prompt;
  }

  async create(createPromptDto: CreatePromptDto): Promise<LlmPrompt> {
    const prompt = this.promptRepository.create(createPromptDto);
    return this.promptRepository.save(prompt);
  }

  async update(
    id: string,
    updatePromptDto: UpdatePromptDto,
    userId: string
  ): Promise<LlmPrompt> {
    const existingPrompt = await this.findOne(id);
    try {
      // Delete the existing prompt
      await this.promptRepository.delete(id);

      // Create a new prompt with the updated data and a new ID
      const newPromptData = {
        feature: existingPrompt.feature,
        chainIdentifier: existingPrompt.chainIdentifier,
        prompt: updatePromptDto.prompt || existingPrompt.prompt,
        model: updatePromptDto.model || existingPrompt.model,
        requiredVariables:
          updatePromptDto.requiredVariables || existingPrompt.requiredVariables,
        endpoint: updatePromptDto.endpoint || existingPrompt.endpoint,
        description: updatePromptDto.description || existingPrompt.description,
        isActive:
          updatePromptDto.isActive !== undefined
            ? updatePromptDto.isActive
            : true,
      };

      const newPrompt = this.promptRepository.create(newPromptData);
      const savedPrompt = await this.promptRepository.save(newPrompt);

      // Create history entry with the new prompt ID
      const history = this.historyRepository.create({
        promptId: savedPrompt.id,
        oldPrompt: existingPrompt.prompt,
        newPrompt: updatePromptDto.prompt || existingPrompt.prompt,
        changedById: userId,
        changes: {
          model:
            existingPrompt.model !== updatePromptDto.model
              ? {
                  old: existingPrompt.model,
                  new: updatePromptDto.model,
                }
              : undefined,
          requiredVariables:
            JSON.stringify(existingPrompt.requiredVariables) !==
            JSON.stringify(updatePromptDto.requiredVariables)
              ? {
                  old: existingPrompt.requiredVariables,
                  new: updatePromptDto.requiredVariables,
                }
              : undefined,
          comment: updatePromptDto.comment || undefined,
        },
      });
      await this.historyRepository.save(history);

      return savedPrompt;
    } catch (error) {
      const validate = await this.findOne(id);
      if (!validate) {
        await this.promptRepository.save(existingPrompt);
      }
    }
  }

  async getHistory(promptId: string): Promise<LlmPromptHistory[]> {
    return this.historyRepository.find({
      where: { promptId },
      relations: ['changedBy'],
      order: { createdAt: 'DESC' },
    });
  }

  async validatePromptVariables(
    prompt: string,
    requiredVariables: Record<string, string>
  ): Promise<boolean> {
    const { required, optional } = this.extractPromptVariables(prompt);
    const requiredVarKeys = Object.keys(requiredVariables || {});

    return requiredVarKeys.every(
      (varKey) => required.has(varKey) || optional.has(varKey)
    );
  }

  extractPromptVariables(prompt: string): {
    required: Set<string>;
    optional: Map<string, { content: string; hasVariable: boolean }>;
  } {
    const required = new Set<string>();
    const optional = new Map<
      string,
      { content: string; hasVariable: boolean }
    >();

    // Use shared helper names for consistency
    const knownHelpers: string[] = Object.values(this.HELPER_NAMES);

    // Define regex patterns with descriptive names and detailed comments
    const REGEX_PATTERNS = {
      // Matches: {{#if variableName}} - Captures conditional block openers
      // Input: "{{#if user}} Welcome {{/if}}"
      // Output: Captures "user" as conditional variable
      conditionalIfOpener: /\{\{#if\s+(\w+)\}\}/g,

      // Matches: {{#if variable}}content{{/if}} - Captures entire conditional blocks
      // Input: "{{#if isActive}}User is active: {{userName}}{{/if}}"
      // Output: Captures "isActive" and "User is active: {{userName}}" as content
      conditionalIfBlock: /\{\{#if\s+(\w+)\}\}([\s\S]*?)\{\{\/if\}\}/g,

      // Matches: {{variable}} - Simple variable references
      // Input: "Hello {{userName}}"
      // Output: Captures "userName"
      simpleVariable: /\{\{(\w+)\}\}/g,

      // Matches: {{helper arg1 arg2}} - Helper function calls with arguments
      // Input: "{{lookup user 'name'}}" or "{{eq status 'active'}}"
      // Output: Captures helper name and arguments
      helperWithArgs: /\{\{(?!#|\/)\s*(\w+)\s+([^}]+)\}\}/g,

      // Matches: {{#unless}} {{#each}} {{#with}} - Other block helpers
      // Input: "{{#each items}}" or "{{#unless isEmpty}}"
      // Output: Captures the variable being operated on
      blockHelpers: /\{\{#(?:unless|each|with)\s+(\w+)/g,

      // Matches: numbers, booleans, null - Literal values to filter out
      // Input: "123", "true", "false", "null"
      // Output: Used to identify non-variable tokens
      literalValues: /^\d+$|^(true|false|null)$/,

      // Matches: word tokens in helper arguments
      // Input: "user 'name' active"
      // Output: ["user", "name", "active"]
      wordTokens: /\w+/g,
    };

    // Step 1: Extract conditional variables as REQUIRED
    // These are variables that control if/unless conditions and must be provided
    const conditionalMatches = prompt.matchAll(
      REGEX_PATTERNS.conditionalIfOpener
    );
    for (const match of conditionalMatches) {
      const condVar = match[1];
      required.add(condVar);
    }

    // Step 2: Extract variables from INSIDE conditional blocks as OPTIONAL
    // These variables are only used when certain conditions are met
    const blockMatches = prompt.matchAll(REGEX_PATTERNS.conditionalIfBlock);
    for (const match of blockMatches) {
      const [, condVar, content] = match;
      const varsInBlock = new Set<string>();

      // Extract simple variables from block content
      // Example: {{userName}} -> "userName"
      const simpleVarMatches = content.matchAll(REGEX_PATTERNS.simpleVariable);
      for (const varMatch of simpleVarMatches) {
        varsInBlock.add(varMatch[1]);
      }

      // Extract variables from helper calls within the block
      // Example: {{lookup user 'name'}} -> "user" (and potentially "name" if it's a variable)
      const helperMatches = content.matchAll(REGEX_PATTERNS.helperWithArgs);
      for (const helperMatch of helperMatches) {
        const [, helper, args] = helperMatch;
        const tokens = args.match(REGEX_PATTERNS.wordTokens) || [];

        tokens
          .filter((token) => !REGEX_PATTERNS.literalValues.test(token))
          .forEach((token) => varsInBlock.add(token));
      }

      // Mark all variables found in conditional blocks as optional
      varsInBlock.forEach((v) =>
        optional.set(v, { content: content.trim(), hasVariable: true })
      );
    }

    // Step 3: Extract required variables from outside conditionals
    // Remove all conditional blocks to get the "always executed" part of the template
    const promptWithoutConditionals = prompt.replace(
      REGEX_PATTERNS.conditionalIfBlock,
      ''
    );

    // Extract simple variables from non-conditional parts
    const requiredSimpleMatches = promptWithoutConditionals.matchAll(
      REGEX_PATTERNS.simpleVariable
    );
    for (const match of requiredSimpleMatches) {
      const variable = match[1];
      if (!optional.has(variable)) {
        required.add(variable);
      }
    }

    // Extract variables from helper calls in non-conditional parts
    const requiredHelperMatches = promptWithoutConditionals.matchAll(
      REGEX_PATTERNS.helperWithArgs
    );
    for (const match of requiredHelperMatches) {
      const [, helper, args] = match;
      const tokens = args.match(REGEX_PATTERNS.wordTokens) || [];

      tokens.forEach((token, idx) => {
        if (!REGEX_PATTERNS.literalValues.test(token) && !optional.has(token)) {
          // Special handling for 'lookup' helper: second argument (idx 1) is usually a property name
          // For other helpers, all non-literal tokens are considered variables
          if (
            (helper === this.HELPER_NAMES.LOOKUP && idx === 1) ||
            !knownHelpers.includes(helper) ||
            (knownHelpers.includes(helper) &&
              helper !== this.HELPER_NAMES.LOOKUP)
          ) {
            required.add(token);
          }
        }
      });
    }

    // Extract variables from other block helpers (unless, each, with)
    const blockHelperMatches = promptWithoutConditionals.matchAll(
      REGEX_PATTERNS.blockHelpers
    );
    for (const match of blockHelperMatches) {
      const variable = match[1];
      if (!optional.has(variable)) {
        required.add(variable);
      }
    }

    return { required, optional };
  }

  async compilePromptContent({
    promptContent,
    variables,
  }: {
    promptContent: string;
    variables: Record<string, any>;
  }): Promise<string> {
    // Register Handlebars helpers using shared helper names for consistency
    Handlebars.registerHelper(this.HELPER_NAMES.JSON, function (context) {
      return JSON.stringify(context);
    });

    Handlebars.registerHelper(this.HELPER_NAMES.LOOKUP, function (obj, field) {
      if (!obj) {
        if (field === 'EN') return 'English';
        if (field === 'de') return 'German';
        return '';
      }
      return obj[field];
    });

    Handlebars.registerHelper(
      this.HELPER_NAMES.CONTAINS,
      function (str, substr) {
        return str && str.includes(substr);
      }
    );

    Handlebars.registerHelper(this.HELPER_NAMES.EQ, function (a, b) {
      return a === b;
    });

    Handlebars.registerHelper(
      this.HELPER_NAMES.GENERATION_LANGUAGE,
      function (langCode) {
        const languageMap = {
          de: 'German',
          en: 'English',
          EN: 'English',
          fr: 'French',
          es: 'Spanish',
          it: 'Italian',
        };
        return languageMap[langCode] || langCode;
      }
    );

    Handlebars.registerHelper(this.HELPER_NAMES.IS_TRUTHY, function (value) {
      return value && value !== '';
    });

    const enhancedVariables = {
      ...variables,
      LANGUAGE_MAP: {
        de: 'German',
        en: 'English',
        fr: 'French',
        es: 'Spanish',
        it: 'Italian',
      },
    };

    try {
      const template = Handlebars.compile(promptContent);
      const compiledPrompt = template(enhancedVariables);

      return compiledPrompt;
    } catch (error) {
      console.error(
        `[PromptManagement] Error compiling prompt content:`,
        error
      );
      throw error;
    }
  }

  // New method to get prompt with metadata about variables
  async getPromptWithMetadata(id: string): Promise<{
    prompt: LlmPrompt;
    variables: {
      required: string[];
      optional: Array<{
        name: string;
        content: string;
        hasVariable: boolean;
      }>;
    };
  }> {
    const prompt = await this.findOne(id);
    const { required, optional } = this.extractPromptVariables(prompt.prompt);

    return {
      prompt,
      variables: {
        required: Array.from(required),
        optional: Array.from(optional.entries()).map(([name, data]) => ({
          name,
          content: data.content,
          hasVariable: data.hasVariable,
        })),
      },
    };
  }
}
