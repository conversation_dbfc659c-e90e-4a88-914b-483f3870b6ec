# PM2 Setup for Climate Assistant Backend

## Overview

PM2 is configured to manage the backend Node.js application with cluster mode support in production. This setup allocates 3 CPU cores in production for better performance and reliability.

## Configuration Files

### Production Configuration (`ecosystem.config.js`)
- **Instances**: 3 (utilizes 3 CPU cores)
- **Execution Mode**: Cluster (for load balancing across CPUs)
- **Memory Limit**: 1500MB per instance
- **Auto-restart**: Enabled with max 10 restarts
- **Daily Restart**: Scheduled at 3 AM to prevent memory leaks

### Development Configuration (`ecosystem.dev.config.js`)
- **Instances**: 1 (single process)
- **Execution Mode**: Fork
- **Watch Mode**: Enabled for auto-reload on file changes

## Key Features

### 1. **Cluster Mode in Production**
- Distributes load across 3 CPU cores
- Provides zero-downtime reloads
- Automatic load balancing between instances

### 2. **Memory Management**
- Max memory per instance: 1500MB
- Node.js heap size: 5120MB
- Automatic restart on memory threshold

### 3. **Logging**
- Separate error and output logs
- Timestamped entries
- Merged logs for easier debugging
- Log files location: `./logs/`

### 4. **Process Management**
- Graceful shutdown with 5-second timeout
- Automatic restart on crash
- Minimum uptime before restart: 10 seconds
- Restart delay: 4 seconds

## Usage

### Production Deployment
The Dockerfile is configured to automatically use PM2 in production:
```bash
npm run migration:run:prod && npm run start:prod:pm2
```

### Local Development with PM2
```bash
# Build the application first
npm run build

# Run with PM2 in development mode
npm run start:dev:pm2
```

### PM2 Commands (Inside Container)
```bash
# Check process status
pm2 status

# View logs
pm2 logs

# Monitor CPU/Memory
pm2 monit

# Reload with zero downtime
pm2 reload all

# View detailed process info
pm2 show climate-assistant-backend
```

## Environment Variables

### Production
- `NODE_ENV`: production
- `NODE_OPTIONS`: --max-old-space-size=5120

### Development
- `NODE_ENV`: development
- `NODE_OPTIONS`: --max-old-space-size=2048

## Monitoring

### Health Checks
- PM2 monitors process health automatically
- Restarts on crash or memory limit
- Daily restart at 3 AM to maintain stability

### Resource Usage
- Each instance can use up to 1500MB RAM
- Total allocation: ~4.5GB RAM (3 instances)
- CPU usage distributed across 3 cores

## Troubleshooting

### High Memory Usage
1. Check logs: `pm2 logs`
2. Monitor memory: `pm2 monit`
3. Force restart: `pm2 restart all`

### Process Not Starting
1. Check error logs: `tail -f logs/err.log`
2. Verify build: `npm run build`
3. Check PM2 status: `pm2 status`

### Cluster Communication Issues
1. Ensure ports are not blocked
2. Check instance variables with `pm2 show`
3. Verify cluster mode is enabled

## Docker Integration

The backend Dockerfile includes:
1. PM2 installation via package.json
2. Log directory creation
3. Automatic PM2 startup in production

This ensures seamless deployment with proper CPU allocation in containerized environments.

## Important Note

When adding PM2 or any new dependency:
1. Run `npm install` to update `package-lock.json`
2. Commit both `package.json` and `package-lock.json`
3. This ensures `npm ci` works correctly in Docker builds 