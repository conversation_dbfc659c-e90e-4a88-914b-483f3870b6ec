import { Injectable } from '@nestjs/common';
import { LlmRateLimiterService } from 'src/llm-rate-limiter/llm-rate-limiter.service';
import { LLM_MODELS } from 'src/constants';
import { DocumentParsingUtils } from './utils';
import { TableAnalysisService } from './table-analysis.service';
import { WorkerLogger } from 'src/shared/logger.service';

export interface TableRegion {
  startRow: number;
  startCol: number;
  height: number;
  width: number;
}

export interface TableData {
  headers: any[][];
  rows: any[][];
}

@Injectable()
export class TableDetectionService {
  private readonly logger = new WorkerLogger(TableDetectionService.name);

  constructor(
    private readonly llmRateLimitService: LlmRateLimiterService,
    private readonly tableAnalysisService: TableAnalysisService
  ) {}

  /**
   * Detect header rows using heuristic method
   */
  detectHeaderRows(data: any[][]): number {
    this.logger.log(
      `Detecting header rows with heuristic method, ${data.length} rows total`
    );

    if (data.length <= 1) return 1;

    // Look at the first few rows (up to 5) to identify potential headers
    const maxRowsToCheck = Math.min(5, data.length - 1);
    let headerRowCount = 1; // Default to first row as header

    for (let i = 1; i < maxRowsToCheck; i++) {
      const row = data[i];

      // If row is empty or has only a few values, it's likely not a header
      const nonEmptyCells = row.filter(
        (cell) =>
          cell !== undefined && cell !== null && String(cell).trim() !== ''
      ).length;

      if (nonEmptyCells < 2) continue;

      // Check if the row has characteristics of headers:
      // 1. Mostly text values rather than numbers
      // 2. Short text values (typical for headers)
      const textCells = row.filter(
        (cell) =>
          cell !== undefined &&
          cell !== null &&
          typeof cell === 'string' &&
          String(cell).trim() !== '' &&
          String(cell).length < 50 &&
          isNaN(Number(cell))
      ).length;

      const isLikelyHeader = textCells >= nonEmptyCells * 0.7;

      if (isLikelyHeader) {
        headerRowCount = i + 1;
      } else {
        // Once we find a row that doesn't look like a header, stop checking
        break;
      }
    }

    this.logger.log(
      `Heuristic header detection complete, found ${headerRowCount} header rows`
    );
    return headerRowCount;
  }

  /**
   * Detect header rows using LLM
   */
  async detectHeaderRowsWithLLM(tableData: any[][]): Promise<number> {
    this.logger.log(
      `Detecting header rows with LLM, ${tableData.length} rows total`
    );

    if (tableData.length <= 1) return 1;

    // Filter out completely empty rows that might appear during page transitions
    const filteredTableData = tableData.filter((row) =>
      row.some(
        (cell) =>
          cell !== undefined && cell !== null && String(cell).trim() !== ''
      )
    );

    if (filteredTableData.length <= 1) return 1;

    // Convert table data to a sample for LLM analysis (first 8 rows max)
    const rowSample = filteredTableData
      .slice(0, 8)
      .map((row) =>
        row
          .map((cell) =>
            cell !== null && cell !== undefined ? String(cell) : ''
          )
          .join('\t')
      )
      .join('\n');

    // Create a prompt that clearly defines the header detection task
    const messages: any[] = [
      {
        role: 'system',
        content:
          'You are an expert in spreadsheet structure analysis. Your task is to identify header rows in tabular data.',
      },
      {
        role: 'user',
        content: `Analyze this tabular data and determine how many rows should be considered as headers.
        
        Header rows typically:
        - Contain column titles or category labels
        - Use shorter text rather than lengthy content
        - Appear at the top of the table
        - May form a hierarchical structure (in multi-row headers)
        
        Return ONLY a single number representing the count of header rows as JSON.
        Example:
        {
          "headerRowCount": 1
        }
        
        Table data (tab-separated):
        ${rowSample}`,
      },
    ];

    try {
      const result = await this.llmRateLimitService.handleRequest({
        model: LLM_MODELS['o4-mini'],
        messages,
        json: true,
        temperature: 0,
      });

      const headerCount = result.response.headerRowCount;
      if (headerCount) {
        this.logger.log(`LLM detected ${headerCount} header rows`);
        return headerCount;
      }

      this.logger.warn(
        'LLM response could not be parsed as a valid header count, falling back to heuristic method'
      );
      const fallbackCount = this.detectHeaderRows(filteredTableData);
      this.logger.log(
        `Fallback header detection found ${fallbackCount} header rows`
      );
      return fallbackCount;
    } catch (error: any) {
      this.logger.error(
        `Error using LLM for header detection: ${error.message}, falling back to heuristic method`
      );
      const fallbackCount = this.detectHeaderRows(filteredTableData);
      this.logger.log(
        `Fallback header detection found ${fallbackCount} header rows`
      );
      return fallbackCount;
    }
  }

  /**
   * Find empty rows and columns with adaptive thresholds based on data density
   */
  findEmptyRowsAndColsAdaptive(
    data: any[][],
    headerRowCount = 2
  ): {
    emptyRows: boolean[];
    emptyCols: boolean[];
  } {
    this.logger.log(
      `Finding empty rows and columns adaptively, ${data.length} rows, header count: ${headerRowCount}`
    );

    if (data.length === 0) return { emptyRows: [], emptyCols: [] };

    const rowCount = data.length;
    const colCount = data[0].length;

    // Calculate data density to adjust emptiness threshold
    const totalCells = rowCount * colCount;
    let nonEmptyCells = 0;

    // Count non-empty cells
    for (let r = 0; r < rowCount; r++) {
      for (let c = 0; c < colCount; c++) {
        const value = data[r][c];
        if (
          value !== undefined &&
          value !== null &&
          String(value).trim() !== ''
        ) {
          nonEmptyCells++;
        }
      }
    }

    // Calculate data density as percentage
    const dataDensity = nonEmptyCells / totalCells;

    // Adaptive threshold: For sparse sheets (low density), be more lenient
    // For dense sheets, require more empty cells to consider a row/col empty
    const emptyThreshold = Math.min(0.9, Math.max(0.95, 1 - dataDensity));

    // Arrays to track empty/non-empty status
    const rowEmptyCounts = Array(rowCount).fill(0);
    const colEmptyCounts = Array(colCount).fill(0);

    // Count empty cells in each row and column
    for (let r = 0; r < rowCount; r++) {
      for (let c = 0; c < colCount; c++) {
        const value = data[r][c];
        const isEmpty =
          value === undefined || value === null || String(value).trim() === '';

        if (isEmpty) {
          rowEmptyCounts[r]++;
          colEmptyCounts[c]++;
        }
      }
    }

    // Determine empty rows and columns based on adaptive threshold
    const emptyRows = rowEmptyCounts.map(
      (count) => count / colCount >= emptyThreshold
    );
    const emptyCols = colEmptyCounts.map((count, colIndex) => {
      // Check if this column has any header content
      let hasHeaderContent = false;
      for (let r = 0; r < Math.min(headerRowCount, rowCount); r++) {
        const value = data[r][colIndex];
        if (
          value !== undefined &&
          value !== null &&
          String(value).trim() !== ''
        ) {
          hasHeaderContent = true;
          break;
        }
      }

      // If column has header content, use a stricter threshold or never mark as empty
      if (hasHeaderContent) {
        // Never mark a column with header content as empty
        return false;
      }

      // Standard threshold for columns without header content
      return count / rowCount >= emptyThreshold;
    });

    this.logger.log(
      `Adaptive analysis complete: detected ${emptyRows.filter(Boolean).length} empty rows and ${emptyCols.filter(Boolean).length} empty columns`
    );
    return { emptyRows, emptyCols };
  }

  /**
   * Enhanced region identification using 8-way connectivity
   */
  identifyTableRegionsEnhanced(
    data: any[][],
    emptyRows: boolean[],
    emptyCols: boolean[]
  ): TableRegion[] {
    this.logger.log(`Identifying table regions with 8-way connectivity`);

    const rowCount = data.length;
    const colCount = data[0].length;

    // Create a grid representing non-empty cells
    const grid: boolean[][] = Array(rowCount)
      .fill(false)
      .map((_, r) =>
        Array(colCount)
          .fill(false)
          .map((_, c) => {
            if (emptyRows[r] || emptyCols[c]) return false;
            const value = data[r][c];
            return !(
              value === undefined ||
              value === null ||
              String(value).trim() === ''
            );
          })
      );

    // Use connected component analysis with 8-way connectivity
    const visited: boolean[][] = Array(rowCount)
      .fill(false)
      .map(() => Array(colCount).fill(false));

    const regions: TableRegion[] = [];

    // Direction vectors for 8-way connectivity
    const dr = [-1, -1, 0, 1, 1, 1, 0, -1];
    const dc = [0, 1, 1, 1, 0, -1, -1, -1];

    // Find all connected components (table regions)
    for (let r = 0; r < rowCount; r++) {
      for (let c = 0; c < colCount; c++) {
        if (grid[r][c] && !visited[r][c]) {
          // Start a new region
          let minRow = r,
            maxRow = r,
            minCol = c,
            maxCol = c;

          // Use BFS to explore the connected component
          const queue: [number, number][] = [[r, c]];
          visited[r][c] = true;

          while (queue.length > 0) {
            const [curR, curC] = queue.shift()!;

            // Update region boundaries
            minRow = Math.min(minRow, curR);
            maxRow = Math.max(maxRow, curR);
            minCol = Math.min(minCol, curC);
            maxCol = Math.max(maxCol, curC);

            // Explore neighbors with 8-way connectivity
            for (let i = 0; i < 8; i++) {
              const newR = curR + dr[i];
              const newC = curC + dc[i];

              // Check if the neighbor is valid and unvisited
              if (
                newR >= 0 &&
                newR < rowCount &&
                newC >= 0 &&
                newC < colCount &&
                grid[newR][newC] &&
                !visited[newR][newC]
              ) {
                queue.push([newR, newC]);
                visited[newR][newC] = true;
              }
            }
          }

          // Check if region is big enough to be a potential table
          // Minimum 2x2 size and at least 4 cells
          if (maxRow - minRow >= 1 && maxCol - minCol >= 1) {
            regions.push({
              startRow: minRow,
              startCol: minCol,
              height: maxRow - minRow + 1,
              width: maxCol - minCol + 1,
            });
          }
        }
      }
    }

    this.logger.log(
      `Table region identification complete, found ${regions.length} potential regions`
    );
    return regions;
  }

  /**
   * Extract data for a specific region from the normalized sheet data
   */
  extractRegionData(
    data: any[][],
    region: {
      startRow: number;
      startCol: number;
      height: number;
      width: number;
    }
  ): any[][] {
    this.logger.log(
      `Extracting region data: starting at row ${region.startRow}, col ${region.startCol}, ${region.height}x${region.width}`
    );

    const { startRow, startCol, height, width } = region;

    // Create a new array for the region data
    const regionData: any[][] = [];

    for (let r = 0; r < height; r++) {
      const row: any[] = [];
      for (let c = 0; c < width; c++) {
        // Get data from the original sheet, handling out-of-bounds gracefully
        const dataRow = startRow + r;
        const dataCol = startCol + c;

        if (dataRow < data.length && dataCol < data[dataRow].length) {
          row.push(data[dataRow][dataCol]);
        } else {
          row.push('');
        }
      }
      regionData.push(row);
    }

    return regionData;
  }

  /**
   * Analyze sheet structure to determine if it contains a single continuous table
   */
  analyzeSheetStructure(data: any[][]): {
    continuousTableProbability: number;
    tableTypes: string[];
    estimatedHeaderRows: number;
  } {
    // Initialize result
    const result = {
      continuousTableProbability: 0,
      tableTypes: [] as string[],
      estimatedHeaderRows: 1,
    };

    if (data.length < 10) return result;

    // Check for patterns indicating a continuous table
    // 1. Analyze first few rows - are they likely headers?
    let headerRows = 0;
    let headerEvidence = 0;

    for (let r = 0; r < Math.min(10, data.length); r++) {
      // Check if row has header-like properties
      const headerScore = DocumentParsingUtils.calculateHeaderLikelihood(
        data[r]
      );

      if (headerScore > 0.6) {
        headerRows++;
        headerEvidence += headerScore;
      } else {
        break; // First non-header row found
      }
    }

    result.estimatedHeaderRows = Math.max(1, headerRows);

    // 2. Analyze row consistency - do subsequent rows follow a consistent pattern?
    const rowPatternScores: number[] = [];

    // Skip the identified headers
    for (let r = headerRows; r < Math.min(100, data.length); r++) {
      // Calculate pattern consistency with preceding rows
      if (r > headerRows) {
        rowPatternScores.push(
          DocumentParsingUtils.calculateRowPatternSimilarity(
            data[r],
            data[r - 1]
          )
        );
      }
    }

    // Calculate the average pattern consistency
    const avgPatternConsistency =
      rowPatternScores.length > 0
        ? rowPatternScores.reduce((sum, score) => sum + score, 0) /
          rowPatternScores.length
        : 0;

    // 3. Analyze column data types - are they consistent?
    const columnTypeConsistency = this.analyzeColumnTypeConsistency(
      data,
      headerRows
    );

    // Determine the continuity probability
    result.continuousTableProbability =
      headerEvidence * 0.3 +
      avgPatternConsistency * 0.4 +
      columnTypeConsistency * 0.3;

    // Identify table types based on analysis
    if (result.continuousTableProbability > 0.7) {
      result.tableTypes.push('continuous');
    } else if (avgPatternConsistency > 0.6) {
      result.tableTypes.push('semi-continuous');
    } else {
      result.tableTypes.push('segmented');
    }

    return result;
  }

  /**
   * Analyze column type consistency after headers
   */
  private analyzeColumnTypeConsistency(
    data: any[][],
    headerRows: number
  ): number {
    if (data.length <= headerRows) return 0;

    const colCount = data[0].length;
    let totalConsistency = 0;
    let columnsAnalyzed = 0;

    for (let c = 0; c < colCount; c++) {
      // Skip columns that are completely empty
      let hasData = false;

      for (let r = headerRows; r < data.length; r++) {
        if (DocumentParsingUtils.getCellDataType(data[r][c]) !== 'empty') {
          hasData = true;
          break;
        }
      }

      if (!hasData) continue;

      // Analyze data type consistency for this column
      const typeCounts: Record<string, number> = {};
      let totalCells = 0;

      for (let r = headerRows; r < data.length; r++) {
        const cellType = DocumentParsingUtils.getCellDataType(data[r][c]);

        if (cellType !== 'empty') {
          typeCounts[cellType] = (typeCounts[cellType] || 0) + 1;
          totalCells++;
        }
      }

      // Find most common type
      let maxTypeCount = 0;
      for (const count of Object.values(typeCounts)) {
        maxTypeCount = Math.max(maxTypeCount, count);
      }

      // Calculate consistency score for this column
      const columnConsistency = totalCells > 0 ? maxTypeCount / totalCells : 0;

      totalConsistency += columnConsistency;
      columnsAnalyzed++;
    }

    return columnsAnalyzed > 0 ? totalConsistency / columnsAnalyzed : 0;
  }

  /**
   * Determine optimal header count based on data analysis
   */
  calculateOptimalHeaderCount(
    llmHeaderCount: number,
    regionData: any[][]
  ): number {
    if (regionData.length <= 1) return 1;

    // Enforce reasonable limits
    const maxPossible = Math.min(5, Math.floor(regionData.length / 2));

    // Start with LLM's suggestion
    const headerCount = Math.max(1, Math.min(llmHeaderCount, maxPossible));

    // Analyze the first few rows to see if they look like headers
    const potentialHeaderRows = Math.min(5, regionData.length - 1);
    const headerLikeScores: number[] = [];

    for (let r = 0; r < potentialHeaderRows; r++) {
      let textValues = 0;
      let numericValues = 0;
      let nonEmptyValues = 0;

      for (let c = 0; c < regionData[r].length; c++) {
        const value = regionData[r][c];
        if (
          value !== undefined &&
          value !== null &&
          String(value).trim() !== ''
        ) {
          nonEmptyValues++;

          if (typeof value === 'number' || !isNaN(Number(value))) {
            numericValues++;
          } else {
            textValues++;
          }
        }
      }

      // Headers typically have:
      // 1. More text than numbers
      // 2. Good cell coverage
      const cellCoverage = nonEmptyValues / regionData[r].length;
      const textRatio = nonEmptyValues > 0 ? textValues / nonEmptyValues : 0;

      // Compute header-like score
      headerLikeScores.push(cellCoverage * 0.5 + textRatio * 0.5);
    }

    // Check for significant drop in header-like score
    for (let r = 1; r < headerLikeScores.length; r++) {
      if (headerLikeScores[r] < headerLikeScores[r - 1] * 0.7) {
        // Found a significant drop - likely transition from header to data
        return r;
      }
    }

    // If no clear drop, stick with LLM suggestion with constraints
    return headerCount;
  }

  /**
   * Normalize sheet data to ensure consistent dimensions and handle empty values
   */
  normalizeSheetData(data: any[][]): any[][] {
    this.logger.log(`Normalizing sheet data: ${data.length} rows`);

    if (data.length === 0) return [];

    // Find the maximum number of columns
    const maxCols = Math.max(...data.map((row) => row.length));

    // Create a new normalized data array with consistent dimensions
    const normalizedData = data.map((row) => {
      // Create a new row with the maximum column count
      const normalizedRow = Array(maxCols).fill('');

      // Copy existing values
      for (let i = 0; i < row.length; i++) {
        const value = row[i];
        normalizedRow[i] = value !== undefined && value !== null ? value : '';
      }

      return normalizedRow;
    });

    this.logger.log(
      `Sheet normalized to consistent dimensions: ${data.length} rows x ${maxCols} columns`
    );
    return normalizedData;
  }

  /**
   * Calculate header likelihood for a row based on text content and cell coverage
   */
  calculateHeaderLikelihood(row: any[]): number {
    if (!row || row.length === 0) return 0;

    let textCells = 0;
    let nonEmptyCells = 0;
    let shortTextCells = 0;

    for (const cell of row) {
      if (cell !== undefined && cell !== null && String(cell).trim() !== '') {
        nonEmptyCells++;

        if (typeof cell === 'string') {
          textCells++;

          // Headers often have shorter text
          if (String(cell).length < 50) {
            shortTextCells++;
          }
        }
      }
    }

    if (nonEmptyCells === 0) return 0;

    // Headers typically have:
    // 1. High proportion of text cells
    // 2. High proportion of short text cells
    // 3. Good overall cell coverage

    const textRatio = textCells / nonEmptyCells;
    const shortTextRatio = shortTextCells / nonEmptyCells;
    const coverageRatio = nonEmptyCells / row.length;

    return textRatio * 0.4 + shortTextRatio * 0.4 + coverageRatio * 0.2;
  }

  /**
   * Convert regions to tables
   */
  async regionsToTables(
    regions: Array<{
      startRow: number;
      startCol: number;
      height: number;
      width: number;
    }>,
    data: any[][]
  ): Promise<{ headers: any[][]; rows: any[][] }[]> {
    const tables: { headers: any[][]; rows: any[][] }[] = [];

    for (const region of regions) {
      // Extract region data
      const regionData = this.extractRegionData(data, region);

      // Skip regions that are too small
      if (regionData.length < 2 || regionData[0].length < 2) continue;

      // Analyze region data to detect headers
      const headerRowCount = await this.detectHeaderRowsWithLLM(regionData);

      // Apply more robust header count determination
      const safeHeaderCount = this.calculateOptimalHeaderCount(
        headerRowCount,
        regionData
      );

      // Create the table
      tables.push({
        headers: regionData.slice(0, safeHeaderCount),
        rows: regionData.slice(safeHeaderCount),
      });
    }

    return tables;
  }

  /**
   * Apply hierarchical clustering to better group table regions
   * This helps handle complex layouts with many tables
   */
  applyHierarchicalClustering(
    regions: Array<{
      startRow: number;
      startCol: number;
      height: number;
      width: number;
    }>,
    data: any[][]
  ): Array<{
    startRow: number;
    startCol: number;
    height: number;
    width: number;
  }> {
    this.logger.log(
      `Applying hierarchical clustering to ${regions.length} regions`
    );

    if (regions.length <= 1) return regions;

    // Calculate distances between all region pairs
    const distances: { i: number; j: number; distance: number }[] = [];

    for (let i = 0; i < regions.length; i++) {
      for (let j = i + 1; j < regions.length; j++) {
        const r1 = regions[i];
        const r2 = regions[j];

        // Calculate center points
        const r1CenterRow = r1.startRow + r1.height / 2;
        const r1CenterCol = r1.startCol + r1.width / 2;
        const r2CenterRow = r2.startRow + r2.height / 2;
        const r2CenterCol = r2.startCol + r2.width / 2;

        // Calculate Euclidean distance between centers
        const distance = Math.sqrt(
          Math.pow(r1CenterRow - r2CenterRow, 2) +
            Math.pow(r1CenterCol - r2CenterCol, 2)
        );

        distances.push({ i, j, distance });
      }
    }

    // Sort distances
    distances.sort((a, b) => a.distance - b.distance);

    // Create a union-find data structure for clustering
    const parent = Array.from({ length: regions.length }, (_, i) => i);

    const find = (x: number): number => {
      if (parent[x] !== x) {
        parent[x] = find(parent[x]);
      }
      return parent[x];
    };

    const union = (x: number, y: number): void => {
      parent[find(x)] = find(y);
    };

    // Dynamic clustering threshold based on table density and count
    const dataDensity = DocumentParsingUtils.calculateDataDensity(data);
    const baseClusterThreshold = 5; // Base threshold for merging
    const clusterThreshold =
      baseClusterThreshold * (1 + Math.min(1, Math.max(0.2, dataDensity * 2)));

    // Merge regions based on distance threshold
    for (const { i, j, distance } of distances) {
      if (distance > clusterThreshold) continue;

      const r1 = regions[i];
      const r2 = regions[j];

      // Additional checks for alignment
      const horizontalOverlap =
        r1.startCol <= r2.startCol + r2.width &&
        r2.startCol <= r1.startCol + r1.width;

      const verticalOverlap =
        r1.startRow <= r2.startRow + r2.height &&
        r2.startRow <= r1.startRow + r1.height;

      // Only merge if there's alignment
      if (horizontalOverlap || verticalOverlap) {
        union(i, j);
      }
    }

    // Group regions by cluster
    const clusters = new Map<number, number[]>();

    for (let i = 0; i < regions.length; i++) {
      const clusterID = find(i);
      if (!clusters.has(clusterID)) {
        clusters.set(clusterID, []);
      }
      clusters.get(clusterID)!.push(i);
    }

    // Create merged regions
    const mergedRegions: typeof regions = [];

    for (const clusterIndices of clusters.values()) {
      if (clusterIndices.length === 1) {
        // Single region in cluster, keep as is
        mergedRegions.push(regions[clusterIndices[0]]);
        continue;
      }

      // Merge multiple regions in cluster
      let minRow = Infinity;
      let minCol = Infinity;
      let maxRow = -Infinity;
      let maxCol = -Infinity;

      for (const idx of clusterIndices) {
        const region = regions[idx];
        minRow = Math.min(minRow, region.startRow);
        minCol = Math.min(minCol, region.startCol);
        maxRow = Math.max(maxRow, region.startRow + region.height - 1);
        maxCol = Math.max(maxCol, region.startCol + region.width - 1);
      }

      mergedRegions.push({
        startRow: minRow,
        startCol: minCol,
        height: maxRow - minRow + 1,
        width: maxCol - minCol + 1,
      });
    }

    this.logger.log(
      `Clustering complete: merged into ${mergedRegions.length} regions`
    );
    return mergedRegions;
  }

  /**
   * Enhanced table detection algorithm with progressive consistency enforcement
   * Maintains table cohesion throughout large worksheets
   */
  async detectTablesInSheet(
    data: any[][],
    merges: any[] = []
  ): Promise<{ headers: any[][]; rows: any[][] }[]> {
    this.logger.log(
      `Detecting tables in sheet: ${data.length} rows, ${merges.length} merged regions`
    );

    if (data.length === 0) return [];

    // Normalize the data
    const normalizedData = this.normalizeSheetData(data);

    // For very large sheets, use a progressive approach instead of trying to process all at once
    if (normalizedData.length > 300) {
      this.logger.log(
        `Large sheet detected (${normalizedData.length} rows), using progressive approach`
      );
      return this.detectTablesProgressively(normalizedData, merges);
    }

    // Standard processing for smaller sheets
    this.logger.log(
      `Using standard table detection for sheet with ${normalizedData.length} rows`
    );
    const { emptyRows, emptyCols } =
      this.findEmptyRowsAndColsAdaptive(normalizedData);
    const regions = this.identifyTableRegionsEnhanced(
      normalizedData,
      emptyRows,
      emptyCols
    );
    const mergedRegions = this.applyHierarchicalClustering(
      regions,
      normalizedData
    );

    // Convert regions to tables
    const tables = await this.regionsToTables(mergedRegions, normalizedData);
    this.logger.log(
      `Standard detection complete, found ${tables.length} tables`
    );
    return tables;
  }

  /**
   * Progressive table detection for large worksheets
   * Processes the sheet in overlapping chunks to maintain consistency
   */
  async detectTablesProgressively(
    data: any[][],
    merges: any[] = []
  ): Promise<{ headers: any[][]; rows: any[][] }[]> {
    this.logger.log(
      `Starting progressive table detection on ${data.length} rows`
    );

    const rowCount = data.length;
    const chunkSize = 150; // Process in reasonable chunks
    const overlap = 50; // Ensure chunks overlap to maintain consistency

    // Analyze the first chunk to detect the primary table pattern
    const firstChunk = data.slice(0, Math.min(chunkSize, rowCount));
    const { emptyRows: firstEmptyRows, emptyCols: firstEmptyCols } =
      this.findEmptyRowsAndColsAdaptive(firstChunk);

    // Detect column structure from the first chunk - this is critical for consistency
    const columnStructure = this.tableAnalysisService.analyzeColumnStructure(
      firstChunk,
      firstEmptyCols
    );

    // Strategy determination - based on first chunk analysis
    const analysisResult = this.analyzeSheetStructure(firstChunk);
    const isContinuousTable = analysisResult.continuousTableProbability > 0.7;
    this.logger.log(
      `Sheet analysis: continuous table probability: ${analysisResult.continuousTableProbability.toFixed(2)}, detected ${analysisResult.estimatedHeaderRows} header rows`
    );

    // If we detect a single continuous table structure, handle it specially
    if (isContinuousTable) {
      this.logger.log(
        `Detected continuous table structure, handling as single table`
      );
      return this.tableAnalysisService.handleContinuousTable(
        data,
        columnStructure,
        (data) => this.detectHeaderRowsWithLLM(data)
      );
    }

    // Process chunks with awareness of global structure
    const allRegions: Array<{
      startRow: number;
      startCol: number;
      height: number;
      width: number;
      confidence: number;
    }> = [];

    // Process each chunk
    for (
      let startRow = 0;
      startRow < rowCount;
      startRow += chunkSize - overlap
    ) {
      const endRow = Math.min(startRow + chunkSize, rowCount);

      // Extract the current chunk
      const chunk = data.slice(startRow, endRow);

      // Process this chunk
      const { emptyRows, emptyCols } = this.findEmptyRowsAndColsAdaptive(chunk);

      // Force column structure consistency with first chunk when appropriate
      const consistentEmptyCols =
        this.tableAnalysisService.enforceColumnConsistency(
          emptyCols,
          columnStructure
        );

      // Detect regions in this chunk
      const regions = this.identifyTableRegionsEnhanced(
        chunk,
        emptyRows,
        consistentEmptyCols
      );

      // Adjust region positions to account for chunk offset
      const offsetRegions = regions.map((region) => ({
        ...region,
        startRow: region.startRow + startRow,
        confidence: this.tableAnalysisService.assessTableConfidenceAdaptive(
          this.extractRegionData(chunk, region),
          chunk.length,
          chunk[0].length
        ),
      }));

      // Add to our collection
      allRegions.push(...offsetRegions);
    }

    // Apply global consistency enforcement
    const cohesiveRegions = this.tableAnalysisService.enforceGlobalConsistency(
      allRegions,
      data
    );

    this.logger.log(
      `Progressive detection complete, found ${cohesiveRegions.length} table regions`
    );
    const result = await this.regionsToTables(cohesiveRegions, data);
    this.logger.log(`Converted regions to ${result.length} tables`);
    return result;
  }
}
