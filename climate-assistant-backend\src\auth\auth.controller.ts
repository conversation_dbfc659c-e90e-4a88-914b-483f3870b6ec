import {
  Body,
  Controller,
  Get,
  Post,
  Request,
  Res,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from './auth.guard';
import { AuthService } from './auth.service';
import { JWT_COOKIE_KEY, Public } from './helpers';
import { Response } from 'express';
import { UsersService } from '../users/users.service';
import {
  LoginDto,
  LoginResponseSuccess,
  LogoutResponseSuccess,
  RegisterWithCompanyDto,
} from './auth.dto';
import { Roles } from '../auth/roles.decorator';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SystemPermissions, USER_ROLES } from 'src/constants';
import { isProduction } from 'src/env-helper';
import { PermissionService } from './services/permission.service';
import { PermissionGuard } from './guard/permission.guard';
import { Permissions } from './decorators/permissions.decorator';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(
    private authService: AuthService,
    private readonly userService: UsersService,
    private readonly permissionService: PermissionService
  ) {}

  @Public()
  @Post('login')
  @ApiOperation({ summary: 'User login' })
  @ApiResponse({ status: 200, description: 'Login successful' })
  async login(
    @Body() loginDto: LoginDto,
    @Res() res: Response
  ): Promise<Response<LoginResponseSuccess>> {
    const token = await this.authService.login(
      loginDto.email,
      loginDto.password
    );

    res.cookie(JWT_COOKIE_KEY, token, {
      httpOnly: true,
      secure: true,
      sameSite: 'none',
      expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 1),
    });

    return res.send({ message: 'Login successful' });
  }

  @Public()
  @Post('register')
  @ApiOperation({ summary: 'User registration' })
  @ApiResponse({ status: 200, description: 'Registration successful' })
  async register(
    @Body() registerDto: RegisterWithCompanyDto,
    @Res() res: Response
  ): Promise<Response<LoginResponseSuccess>> {
    const token = await this.authService.registerWithCompany(registerDto);

    res.cookie(JWT_COOKIE_KEY, token, {
      httpOnly: true,
      secure: true,
      expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 1),
    });

    return res.send({ message: 'Login successful' });
  }

  @Public()
  @Post('request-password-reset')
  @ApiOperation({ summary: 'Password reset request' })
  @ApiResponse({
    status: 200,
    description: 'Password reset email sent successfully',
  })
  async passwordResetEmail(
    @Res() res: Response,
    @Request() req,
    @Body() body: { email: string }
  ): Promise<Response> {
    await this.authService.sendPasswordResetEmail(
      body.email,
      req.headers.origin
    );

    return res.send({ message: 'Password reset email sent successfully' });
  }

  @Public()
  @Post('validate-password-reset-token')
  @ApiOperation({ summary: 'Password reset token validatiaon' })
  @ApiResponse({
    status: 200,
    description: 'Valid token',
  })
  async validatePasswordResetToken(
    @Request() req,
    @Body() body: { token: string }
  ) {
    const token = await this.authService.validateToken(body.token);
    return token.user;
  }

  @Public()
  @Post('password-reset-submit')
  @ApiOperation({ summary: 'Password reset submit' })
  @ApiResponse({
    status: 200,
    description: 'Password submitted successfully',
  })
  async passwordSubmit(
    @Request() req,
    @Res() res: Response,
    @Body() body: { password: string; fullName?: string; token: string }
  ) {
    const userToken = await this.authService.resetPassword(
      body.password,
      body.token,
      body.fullName
    );
    res.cookie(JWT_COOKIE_KEY, userToken, {
      httpOnly: true,
      secure: true,
      expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 1),
    });

    return res.send({ message: 'Password reset successful' });
  }

  @UseGuards(AuthGuard)
  @Post('logout')
  @ApiOperation({ summary: 'User logout' })
  @ApiResponse({ status: 200, description: 'Logout successful' })
  async logout(@Res() res: Response): Promise<Response<LogoutResponseSuccess>> {
    res.clearCookie(JWT_COOKIE_KEY);
    return res.send({ message: 'Logout successful' });
  }

  @UseGuards(AuthGuard)
  @Get('profile')
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({
    status: 200,
    description: 'User profile retrieved successfully',
  })
  getProfile(@Request() req) {
    return this.userService.findById(req.user.id);
  }

  @UseGuards(PermissionGuard)
  @Post('switch-workspace')
  @Permissions(SystemPermissions.SWITCH_WORKSPACE)
  async switchWorkspace(
    @Request() req,
    @Body() body: { workspaceId: string },
    @Res() res: Response
  ) {
    const userId = req.user.id;
    const { workspaceId } = body;

    const token = await this.authService.switchUserWorkspace(
      userId,
      workspaceId
    );

    res.cookie(JWT_COOKIE_KEY, token, {
      httpOnly: true,
      secure: isProduction,
      expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 1),
    });

    return res.send({ message: 'Switch successful' });
  }

  @UseGuards(AuthGuard)
  @Get('permissions')
  @ApiOperation({ summary: 'Get user permissions' })
  @ApiResponse({
    status: 200,
    description: 'User permissions retrieved successfully',
  })
  getUserPermissions(@Request() req) {
    return this.permissionService.getUserPermissions(
      req.user.id,
      req.user.workspaceId
    );
  }
}
