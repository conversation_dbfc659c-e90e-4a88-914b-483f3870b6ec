import { MigrationInterface, QueryRunner } from 'typeorm';
import { SystemPermissions } from '../../constants';

export class SchemaUpdate1750772723874 implements MigrationInterface {
  name = 'SchemaUpdate1750772723874';

  // New prompt management permissions to be added
  private readonly newPermissions = [
    SystemPermissions.VIEW_ALL_PROMPTS,
    SystemPermissions.CREATE_PROMPTS,
    SystemPermissions.EDIT_PROMPTS,
    SystemPermissions.VIEW_PROMPT_DETAILS,
    SystemPermissions.VIEW_PROMPT_METADATA,
    SystemPermissions.VIEW_PROMPT_HISTORY,
    SystemPermissions.TEST_PROMPTS,
    SystemPermissions.VIEW_PROMPT_MODELS,
    SystemPermissions.VIEW_PROMPTS_BY_FEATURE,
    SystemPermissions.VIEW_ALL_DATAPOINTS,
  ];

  getPermissionDescription(permission: SystemPermissions): string {
    const descriptions: Record<string, string> = {
      // Existing permissions descriptions would be here...

      // Prompt Management
      [SystemPermissions.VIEW_ALL_PROMPTS]: 'View all prompts in the system',
      [SystemPermissions.CREATE_PROMPTS]: 'Create new prompts',
      [SystemPermissions.EDIT_PROMPTS]: 'Edit existing prompts',
      [SystemPermissions.VIEW_PROMPT_DETAILS]:
        'View detailed information about prompts',
      [SystemPermissions.VIEW_PROMPT_METADATA]:
        'View prompt metadata including usage statistics',
      [SystemPermissions.VIEW_PROMPT_HISTORY]:
        'View prompt version history and changes',
      [SystemPermissions.TEST_PROMPTS]: 'Test prompts with sample data',
      [SystemPermissions.VIEW_PROMPT_MODELS]:
        'View available LLM models for prompts',
      [SystemPermissions.VIEW_PROMPTS_BY_FEATURE]:
        'View prompts filtered by feature category',
      [SystemPermissions.VIEW_ALL_DATAPOINTS]:
        'View all datapoints in the workspace',
    };

    return descriptions[permission] || `Permission ${permission}`;
  }

  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, we need to update the permission enum to include new permissions
    const allPermissions = Object.values(SystemPermissions);
    const enumValues = allPermissions.map((value) => `'${value}'`).join(', ');

    // Drop and recreate the enum with new values
    await queryRunner.query(
      `ALTER TYPE "public"."permission_name_enum" RENAME TO "permission_name_enum_old"`
    );

    await queryRunner.query(
      `CREATE TYPE "public"."permission_name_enum" AS ENUM (${enumValues})`
    );

    await queryRunner.query(
      `ALTER TABLE "permission" ALTER COLUMN "name" TYPE "public"."permission_name_enum" USING "name"::text::"public"."permission_name_enum"`
    );

    await queryRunner.query(`DROP TYPE "public"."permission_name_enum_old"`);

    // Insert new prompt management permissions
    for (const permission of this.newPermissions) {
      await queryRunner.query(
        `
                INSERT INTO "permission" (name, description)
                VALUES ($1, $2)
                ON CONFLICT (name) DO NOTHING
                `,
        [permission, this.getPermissionDescription(permission)]
      );
    }

    // Assign all new permissions to SUPER_ADMIN role
    await queryRunner.query(
      `
            WITH role_data AS (
                SELECT id FROM "role" WHERE name = 'SUPER_ADMIN'
            ),
            permission_data AS (
                SELECT id FROM "permission" WHERE name = ANY($1)
            )
            INSERT INTO "role_permission" ("roleId", "permissionId")
            SELECT rd.id, pd.id
            FROM role_data rd
            CROSS JOIN permission_data pd
            ON CONFLICT ("roleId", "permissionId") DO NOTHING
            `,
      [this.newPermissions]
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove role permissions for new prompt management permissions
    await queryRunner.query(
      `
            DELETE FROM "role_permission" 
            WHERE "permissionId" IN (
                SELECT id FROM "permission" WHERE name = ANY($1)
            )
            `,
      [this.newPermissions]
    );

    // Remove the new permissions
    await queryRunner.query(`DELETE FROM "permission" WHERE name = ANY($1)`, [
      this.newPermissions,
    ]);

    // Note: We don't recreate the old enum in the down migration
    // as it would be complex and potentially risky to remove enum values
    // that might be in use. The permissions are removed from the table instead.
  }
}
