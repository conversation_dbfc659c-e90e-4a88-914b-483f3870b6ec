global:
  scrape_interval: 15s
  evaluation_interval: 15s

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets: ['alertmanager:9093']

# Load rules files
rule_files:
  - '/etc/prometheus/alerts/*.yml'

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # PM2 Worker Metrics - Each worker exposes metrics on a different port
  - job_name: 'backend-workers'
    static_configs:
      - targets: 
          - 'backend:3001'  # Worker 0 (Main Worker)
          - 'backend:3002'  # Worker 1
          - 'backend:3003'  # Worker 2
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        regex: 'backend:300(\d)'
        target_label: worker_id
        replacement: '${1}'

  # Container metrics via cAdvisor
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    metrics_path: /metrics

  # Loki metrics
  - job_name: 'loki'
    static_configs:
      - targets: ['loki:3100']
    metrics_path: /metrics

  # Promtail metrics
  - job_name: 'promtail'
    static_configs:
      - targets: ['promtail:9080']
    metrics_path: /metrics

  # PostgreSQL metrics
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Frontend metrics (if exposed)
  # Note: The React frontend doesn't expose metrics, so this is commented out
  # to avoid "unsupported Content-Type" errors in Prometheus logs
  # - job_name: 'frontend'
  #   static_configs:
  #     - targets: ['frontend:5000']
  #   metrics_path: /metrics

  # PM2 metrics exporter (optional)
  - job_name: 'pm2'
    static_configs:
      - targets: ['backend:9209']
    metrics_path: /metrics
