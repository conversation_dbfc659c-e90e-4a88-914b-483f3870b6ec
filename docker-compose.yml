services:
  frontend:
    build: ./climate-assistant-frontend
    container_name: climate-assistant-frontend
    hostname: climate-assistant-frontend
    healthcheck:
      test: ['CMD-SHELL', 'curl -f http://localhost/ || exit 1']
      interval: 30s
      timeout: 10s
      retries: 3
    ports:
      - 5000:5000
    networks:
      - climate-assistant_default
    logging:
      driver: 'json-file'
      options:
        max-size: '100m'
        max-file: '3'
    restart: on-failure

  backend-db:
    build: ./db
    hostname: backend-db
    environment:
      POSTGRES_DB: ${BACKEND_DB_NAME}
      POSTGRES_USER: ${BACKEND_DB_USER}
      POSTGRES_PASSWORD: ${BACKEND_DB_PASSWORD}
    volumes:
      - ./backend-db-data:/var/lib/postgresql/data
      - ./db/init_pgvector.sql:/docker-entrypoint-initdb.d/fsck.sql
    restart: unless-stopped
    ports:
      - ${BACKEND_DB_PORT}:5432
    healthcheck:
      test:
        ['CMD-SHELL', 'pg_isready -U ${BACKEND_DB_USER} -d ${BACKEND_DB_NAME}']
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - climate-assistant_default
    logging:
      driver: 'json-file'
      options:
        max-size: '100m'
        max-file: '3'
    deploy:
      resources:
        limits:
          memory: 6g
          cpus: '3.0'

  backend:
    build: ./climate-assistant-backend
    container_name: climate-assistant-backend
    hostname: climate-assistant-backend
    env_file:
      - .env
    volumes:
      - ./climate-assistant-backend/user-uploads:/usr/src/app/user-uploads
    healthcheck:
      test: ['CMD-SHELL', 'curl -f http://localhost:3000/ || exit 1']
      interval: 30s
      timeout: 10s
      retries: 3
    depends_on:
      - backend-db
    networks:
      - climate-assistant_default
    logging:
      driver: 'json-file'
      options:
        max-size: '200m'
        max-file: '5'
    deploy:
      resources:
        limits:
          memory: 6g
          cpus: '3.0'
        reservations:
          memory: 1g
    restart: on-failure

  pgadmin:
    image: dpage/pgadmin4:latest
    user: root
    container_name: pgadmin
    restart: always
    ports:
      - 8888:80
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_DEFAULT_PASSWORD}
      SCRIPT_NAME: /pgadmin
    volumes:
      - ./pgadmin-data:/var/lib/pgadmin
    networks:
      - climate-assistant_default
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'
    deploy:
      resources:
        limits:
          memory: 512m
          cpus: '1.0'

  nginx:
    image: nginx:1.26.1
    ports:
      - 80:80
      - 443:443
    volumes:
      - ./nginx:/etc/nginx/conf.d
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    command: '/bin/sh -c ''while :; do sleep 6h & wait $${!}; nginx -s reload; done & nginx -g "daemon off;"'''
    networks:
      - climate-assistant_default
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  certbot:
    image: certbot/certbot:v1.7.0
    volumes:
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do certbot renew; sleep 12h & wait $${!}; done;'"
    networks:
      - climate-assistant_default
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  redis:
    image: redis:7-alpine
    hostname: backend-redis
    container_name: redis
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 10s
      timeout: 5s
      retries: 3
    ports:
      - 6379:6379
    networks:
      - climate-assistant_default
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: cadvisor
    restart: unless-stopped
    privileged: true
    devices:
      - /dev/kmsg
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/kmsg:/dev/kmsg:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - climate-assistant_default
    deploy:
      resources:
        limits:
          memory: 200M
          cpus: '0.3'
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    restart: unless-stopped
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.enable-lifecycle'
    networks:
      - climate-assistant_default
    # Optional: resource constraints
    deploy:
      resources:
        limits:
          memory: 300M
          cpus: '0.5'
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  loki:
    image: grafana/loki:latest
    container_name: loki
    restart: unless-stopped
    volumes:
      - loki_data:/loki
      - ./loki/config.yaml:/etc/loki/config.yaml:ro
    command: -config.file=/etc/loki/config.yaml
    networks:
      - climate-assistant_default
    # Minimal resource constraints
    deploy:
      resources:
        limits:
          memory: 200M
          cpus: '0.3'
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  promtail:
    image: grafana/promtail:latest
    container_name: promtail
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - promtail_data:/tmp
      - ./promtail/promtail-config.yaml:/etc/promtail/config.yaml:ro
    command: -config.file=/etc/promtail/config.yaml -client.url=http://loki:3100/loki/api/v1/push
    user: root
    networks:
      - climate-assistant_default
    # Resource constraints
    deploy:
      resources:
        limits:
          memory: 100M
          cpus: '0.2'
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    container_name: postgres-exporter
    environment:
      DATA_SOURCE_NAME: 'postgresql://${BACKEND_DB_USER}:${BACKEND_DB_PASSWORD}@backend-db:5432/${BACKEND_DB_NAME}?sslmode=disable'
    ports:
      - '9187:9187'
    restart: unless-stopped
    networks:
      - climate-assistant_default
    depends_on:
      - backend-db
    deploy:
      resources:
        limits:
          memory: 100M
          cpus: '0.1'
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    restart: unless-stopped
    volumes:
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SERVER_ROOT_URL=${GRAFANA_ROOT_URL}
      - GF_SERVER_SERVE_FROM_SUB_PATH=true
      - GF_SECURITY_ADMIN_USER=<EMAIL>
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_DEFAULT_PASSWORD}
    ports:
      - 3001:3000
    networks:
      - climate-assistant_default
    deploy:
      resources:
        limits:
          memory: 400M
          cpus: '0.5'
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

networks:
  climate-assistant_default:
    driver: bridge

volumes:
  prometheus_data:
  loki_data:
  promtail_data:
  grafana_data:
  cadvisor_data:
