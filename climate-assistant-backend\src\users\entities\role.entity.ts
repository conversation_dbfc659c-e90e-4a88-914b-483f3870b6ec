import { <PERSON><PERSON><PERSON>, PrimaryGenerated<PERSON><PERSON>umn, <PERSON>umn, OneToMany } from 'typeorm';
import { UserWorkspace } from './user-workspace.entity';
import { USER_ROLES } from '../../constants';

@Entity()
export class Role {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: USER_ROLES,
    unique: true,
  })
  name: USER_ROLES;

  @Column({ nullable: true })
  description: string;

  @OneToMany(() => UserWorkspace, (userWorkspace) => userWorkspace.role)
  userWorkspaces: UserWorkspace[];
}
