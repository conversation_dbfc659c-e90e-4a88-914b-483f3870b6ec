import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { WorkerLogger } from 'src/shared/logger.service';

@Injectable()
export class LoggerMiddleware implements NestMiddleware {
  private logger = new WorkerLogger(`HTTP`);
  use(req: Request, res: Response, next: NextFunction) {
    this.logger.log(
      JSON.stringify({
        level: 'info',
        method: req.method,
        url: req.originalUrl,
        statusCode: res.statusCode,
        message: 'HTTP request handled',
      }),
    );
    next();
  }
}
