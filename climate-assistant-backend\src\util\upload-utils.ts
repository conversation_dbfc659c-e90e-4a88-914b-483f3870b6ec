import { diskStorage } from 'multer';
import { parse } from 'path';
import { existsSync, mkdirSync } from 'fs';
import { NestInterceptor } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';

export const USER_FILE_UPLOAD_DIRECTORY = 'user-uploads/';

export const fileInterceptor = FileInterceptor('file', {
  storage: diskStorage({
    destination: (req, file, callback) => {
      const workspaceId = (req as any).user.workspaceId;
      const workspacePath = `${USER_FILE_UPLOAD_DIRECTORY}${workspaceId}`;

      if (!existsSync(workspacePath)) {
        mkdirSync(workspacePath, { recursive: true });
      }

      callback(null, workspacePath);
    },
    filename: (req, file, callback) => {
      const originalName = Buffer.from(file.originalname, 'latin1').toString(
        'utf8',
      );
      const { name, ext } = parse(originalName);
      // const name = file.originalname.split('.')[0];
      // const extension = extname(file.originalname);
      file.originalname = originalName;
      const randomName = Array(32)
        .fill(null)
        .map(() => Math.round(Math.random() * 16).toString(16))
        .join('');

      return callback(null, `${name}-${randomName}${ext}`);
    },
  }),
}) as unknown as NestInterceptor;
