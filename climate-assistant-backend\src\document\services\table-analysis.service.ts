import { Injectable } from '@nestjs/common';
import { DocumentParsingUtils } from './utils';
import { TableRegion } from './table-detection.service';
import { WorkerLogger } from 'src/shared/logger.service';

@Injectable()
export class TableAnalysisService {
  private readonly logger = new WorkerLogger(TableAnalysisService.name);

  /**
   * Analyze column structure to help maintain consistency
   */
  analyzeColumnStructure(
    data: any[][],
    emptyCols: boolean[]
  ): { isActive: boolean[] } {
    const colCount = data[0].length;
    const isActive = Array(colCount).fill(false);

    // First, mark columns that aren't empty
    for (let c = 0; c < colCount; c++) {
      isActive[c] = !emptyCols[c];
    }

    // Now, analyze data density in each column
    const columnDensity: number[] = Array(colCount).fill(0);

    for (let c = 0; c < colCount; c++) {
      let nonEmptyCells = 0;

      for (let r = 0; r < data.length; r++) {
        const value = data[r][c];
        if (
          value !== undefined &&
          value !== null &&
          String(value).trim() !== ''
        ) {
          nonEmptyCells++;
        }
      }

      columnDensity[c] = nonEmptyCells / data.length;
    }

    // Update activity based on density
    for (let c = 0; c < colCount; c++) {
      // Mark columns with reasonable density as active
      if (columnDensity[c] > 0.1) {
        isActive[c] = true;
      }
    }

    return { isActive };
  }

  /**
   * Enforce column consistency based on global structure
   */
  enforceColumnConsistency(
    localEmptyCols: boolean[],
    columnStructure: { isActive: boolean[] }
  ): boolean[] {
    // Create a copy to avoid modifying the original
    const adjustedEmptyCols = [...localEmptyCols];

    // Enforce global column structure for consistency
    for (
      let c = 0;
      c < Math.min(localEmptyCols.length, columnStructure.isActive.length);
      c++
    ) {
      // If this is a globally active column, don't mark it as empty
      if (columnStructure.isActive[c]) {
        adjustedEmptyCols[c] = false;
      }
    }

    return adjustedEmptyCols;
  }

  /**
   * Enforce global consistency across regions detected in different chunks
   */
  enforceGlobalConsistency(
    regions: Array<TableRegion & { confidence: number }>,
    data: any[][]
  ): TableRegion[] {
    if (regions.length <= 1) return regions;

    // Sort regions by position (top to bottom)
    regions.sort((a, b) => a.startRow - b.startRow);

    // Group regions by proximity and alignment
    const groups: Array<typeof regions> = [];
    let currentGroup: typeof regions = [regions[0]];

    for (let i = 1; i < regions.length; i++) {
      const current = regions[i];
      const previous = regions[i - 1];

      // Check if these regions are well-aligned
      const horizontalOverlap = DocumentParsingUtils.calculateHorizontalOverlap(
        current,
        previous
      );
      const verticalGap =
        current.startRow - (previous.startRow + previous.height);

      // If aligned and close, add to current group
      if (horizontalOverlap > 0.5 && verticalGap < 20) {
        currentGroup.push(current);
      } else {
        // Start a new group
        groups.push(currentGroup);
        currentGroup = [current];
      }
    }

    // Add the last group
    if (currentGroup.length > 0) {
      groups.push(currentGroup);
    }

    // Merge regions within each group
    const mergedRegions: TableRegion[] = [];

    for (const group of groups) {
      if (group.length === 1) {
        // Single region, keep as is (remove confidence as it's no longer needed)
        const { confidence, ...region } = group[0];
        mergedRegions.push(region);
      } else {
        // Multiple regions, merge them
        let startRow = Infinity;
        let startCol = Infinity;
        let endRow = -Infinity;
        let endCol = -Infinity;

        // Find the bounding box containing all regions in this group
        for (const region of group) {
          startRow = Math.min(startRow, region.startRow);
          startCol = Math.min(startCol, region.startCol);
          endRow = Math.max(endRow, region.startRow + region.height);
          endCol = Math.max(endCol, region.startCol + region.width);
        }

        mergedRegions.push({
          startRow,
          startCol,
          height: endRow - startRow,
          width: endCol - startCol,
        });
      }
    }

    return mergedRegions;
  }

  /**
   * Adaptive table confidence assessment with context awareness
   */
  assessTableConfidenceAdaptive(
    regionData: any[][],
    totalRows: number,
    totalCols: number
  ): number {
    if (regionData.length < 2) return 0;

    const rowCount = regionData.length;
    const colCount = regionData[0].length;

    if (colCount < 2) return 0;

    // Count non-empty cells
    let nonEmptyCells = 0;
    const totalCells = rowCount * colCount;

    // Count consistent data types per column
    const columnDataTypes: Record<number, Map<string, number>> = {};

    for (let c = 0; c < colCount; c++) {
      columnDataTypes[c] = new Map<string, number>();
    }

    // Analyze the data
    for (let r = 0; r < rowCount; r++) {
      for (let c = 0; c < colCount; c++) {
        const value = regionData[r][c];

        if (
          value !== undefined &&
          value !== null &&
          String(value).trim() !== ''
        ) {
          nonEmptyCells++;

          // Determine data type
          let dataType = 'text';
          if (typeof value === 'number' || !isNaN(Number(value))) {
            dataType = 'number';
          } else if (
            value instanceof Date ||
            DocumentParsingUtils.isLikelyDate(value)
          ) {
            dataType = 'date';
          }

          // Update column data type counts
          const currentCount = columnDataTypes[c].get(dataType) || 0;
          columnDataTypes[c].set(dataType, currentCount + 1);
        }
      }
    }

    // Calculate relative size of region compared to whole sheet
    const relativeSizeRatio = (rowCount * colCount) / (totalRows * totalCols);

    // Small tables in large sheets should be assessed differently
    const isSmallTable = relativeSizeRatio < 0.1;

    // For small tables, we need stricter consistency requirements
    const minRequiredDensity = isSmallTable ? 0.4 : 0.2;

    // Reject if there's not enough data
    const densityScore = nonEmptyCells / totalCells;
    if (densityScore < minRequiredDensity) {
      return 0;
    }

    // Calculate column consistency score with adaptive weighting
    let totalConsistency = 0;
    let columnsWithData = 0;

    for (let c = 0; c < colCount; c++) {
      const types = columnDataTypes[c];
      let maxCount = 0;
      let totalCount = 0;

      for (const count of types.values()) {
        maxCount = Math.max(maxCount, count);
        totalCount += count;
      }

      if (totalCount > 0) {
        const columnConsistency = maxCount / totalCount;
        totalConsistency += columnConsistency;
        columnsWithData++;
      }
    }

    const typeConsistencyScore =
      columnsWithData > 0 ? totalConsistency / columnsWithData : 0;

    // Calculate grid pattern score with emphasis on header-like structures
    let headerLikeScore = 0;

    // Check first row for header-like characteristics
    if (rowCount > 1) {
      let headerCells = 0;
      for (let c = 0; c < colCount; c++) {
        const value = regionData[0][c];
        if (
          value !== undefined &&
          value !== null &&
          String(value).trim() !== '' &&
          typeof value === 'string' &&
          value.length < 50
        ) {
          headerCells++;
        }
      }

      headerLikeScore = headerCells / colCount;
    }

    // Combine scores with weights adjusted for table size
    if (isSmallTable) {
      // Small tables need stronger evidence of tabular structure
      return (
        densityScore * 0.3 + typeConsistencyScore * 0.4 + headerLikeScore * 0.3
      );
    } else {
      // Larger tables can be more forgiving
      return (
        densityScore * 0.25 +
        typeConsistencyScore * 0.35 +
        headerLikeScore * 0.4
      );
    }
  }

  /**
   * Handle a worksheet that contains a single continuous table
   * This is a special case optimization for common worksheet layouts
   */
  async handleContinuousTable(
    data: any[][],
    columnStructure: { isActive: boolean[] },
    detectHeaderRowsWithLLM: (data: any[][]) => Promise<number>
  ): Promise<{ headers: any[][]; rows: any[][] }[]> {
    // Find the header section - we need to determine where the headers end
    let headerRowCount = 1; // Default to 1 header row

    // Use a stronger header detection approach for continuous tables
    for (let r = 1; r < Math.min(10, data.length); r++) {
      const headerLikelihood = DocumentParsingUtils.calculateHeaderLikelihood(data[r]);
      const dataRowLikelihood = DocumentParsingUtils.calculateDataRowLikelihood(data[r]);

      if (headerLikelihood > dataRowLikelihood && headerLikelihood > 0.6) {
        headerRowCount++;
      } else {
        break;
      }
    }

    // Determine active columns based on column structure and content
    const activeColumns: number[] = [];

    for (let c = 0; c < data[0].length; c++) {
      if (columnStructure.isActive[c]) {
        activeColumns.push(c);
      }
    }

    // Determine where the table data ends (ignore trailing empty rows)
    let lastDataRow = data.length - 1;

    while (lastDataRow > headerRowCount) {
      const isEmptyRow = activeColumns.every((c) => {
        const value = data[lastDataRow][c];
        return (
          value === undefined || value === null || String(value).trim() === ''
        );
      });

      if (isEmptyRow) {
        lastDataRow--;
      } else {
        break;
      }
    }

    // Extract the continuous table
    const tableRegion = {
      startRow: 0,
      startCol: 0,
      height: lastDataRow + 1,
      width: data[0].length,
    };

    // Create a single table from this region
    const headers = data.slice(0, headerRowCount);
    const rows = data.slice(headerRowCount, lastDataRow + 1);

    return [{ headers, rows }];
  }
} 