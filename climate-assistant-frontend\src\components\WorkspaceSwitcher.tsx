import { useState } from 'react';
import {
  LoaderCircle,
  RecycleIcon,
  Check,
  ChevronsUpDown,
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';
import { switchWorkspace } from '@/api/workspace-settings/workspace-settings.api';
import { Button, buttonVariants } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useAuthentication } from '@/api/authentication/authentication.query';
import { useWorkspaces } from '@/hooks/useWorkspaces';

interface WorkspaceSwitcherProps {
  currentWorkspaceId: string;
  className?: string;
}

export const WorkspaceSwitcher: React.FC<WorkspaceSwitcherProps> = ({
  currentWorkspaceId,
  className,
}) => {
  const [isSaving, setIsSaving] = useState(false);
  const { workspaces } = useWorkspaces();
  const [selectedWorkspace, setSelectedWorkspace] = useState(currentWorkspaceId);
  const [open, setOpen] = useState(false);
  const { toast } = useToast();
  const { sessionReloadMutation } = useAuthentication();

  const handleSwitchWorkspace = async () => {
    try {
      setIsSaving(true);
      await switchWorkspace(selectedWorkspace);
      await sessionReloadMutation.mutateAsync();
      toast({
        variant: 'success',
        description: 'Workspace switched successfully.',
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'An unknown error occurred.',
      });
      console.error('Failed to switch workspace:', error);
    }
    setIsSaving(false);
  };

  return (
    <div className={cn('flex gap-4 items-center', className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-[300px] justify-between"
          >
            {selectedWorkspace
              ? workspaces.find(
                  (workspace) => workspace.id === selectedWorkspace
                )?.name
              : 'Select a workspace'}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[300px] p-0" side="bottom">
          <Command>
            <CommandInput placeholder="Search workspace..." />
            <CommandList>
              <CommandEmpty>No workspace found.</CommandEmpty>
              <CommandGroup>
                {workspaces.map((workspace) => (
                  <CommandItem
                    key={workspace.id}
                    value={workspace.name + workspace.id}
                    onSelect={() => {
                      setSelectedWorkspace(workspace.id);
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        selectedWorkspace === workspace.id
                          ? 'opacity-100'
                          : 'opacity-0'
                      )}
                    />
                    <div className="flex flex-col gap-1">
                      <span>{workspace.name}</span>
                      <span className="text-xs text-slate-400">
                        {workspace.id}
                      </span>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      <button
        className={cn(
          buttonVariants({ variant: 'outline' }),
          'flex gap-2',
          isSaving ? 'cursor-not-allowed opacity-50' : ''
        )}
        onClick={handleSwitchWorkspace}
        disabled={isSaving || !selectedWorkspace}
      >
        {isSaving && <LoaderCircle className="h-4 w-4 animate-spin" />}
        <RecycleIcon className="h-4 w-4" />
        Switch
      </button>
    </div>
  );
}; 