// src/auth/services/permission.service.ts
import { Injectable, Inject } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Permission } from '../entities/permission.entity';
import { RolePermission } from '../entities/role-permission.entity';

@Injectable()
export class PermissionService {
  constructor(
    @InjectRepository(Permission)
    private permissionRepository: Repository<Permission>,
    @InjectRepository(RolePermission)
    private rolePermissionRepository: Repository<RolePermission>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  async assignPermissionsToRole(roleId: string, permissionIds: string[]) {
    const rolePermissions = permissionIds.map((permissionId) => ({
      roleId,
      permissionId,
    }));

    await this.rolePermissionRepository.save(rolePermissions);
  }

  async getRolePermissions(roleId: string) {
    return this.rolePermissionRepository.find({
      where: { roleId },
      relations: ['permission'],
    });
  }

  async getAllRolePermissions() {
    const rolePermissions = await this.rolePermissionRepository.find({
      relations: ['role', 'permission'],
    });

    return rolePermissions.reduce((acc, rp) => {
      if (!acc[rp.role.id]) {
        acc[rp.role.id] = {
          role: rp.role,
          permissions: [],
        };
      }
      acc[rp.role.id].permissions.push(rp.permission);
      return acc;
    }, {});
  }

  async getUserPermissions(userId: string, workspaceId: string) {
    const userPermissions = await this.rolePermissionRepository
      .createQueryBuilder('rolePermission')
      .innerJoin(
        'user_workspace',
        'userWorkspace',
        'userWorkspace.roleId = rolePermission.roleId'
      )
      .leftJoinAndSelect('rolePermission.permission', 'permission')
      .where('userWorkspace.userId = :userId', { userId })
      .andWhere('userWorkspace.workspaceId = :workspaceId', { workspaceId })
      .getMany();

    return userPermissions.map((rp) => rp.permission.name);
  }

  async invalidateUserPermissions(
    userId: string,
    workspaceId: string
  ): Promise<void> {
    const cacheKey = `permissions:${userId}:${workspaceId}`;
    await this.cacheManager.del(cacheKey);
  }
}
