import { MigrationInterface, QueryRunner } from 'typeorm';

//Migration to add content_version column to datapoint_request and data_request tables for restore version
export class SchemaUpdate1750934407518 implements MigrationInterface {
  name = 'SchemaUpdate1750934407518';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" ADD "content_version" uuid`
    );
    await queryRunner.query(
      `ALTER TABLE "data_request" ADD "content_version" uuid`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "data_request" DROP COLUMN "content_version"`
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" DROP COLUMN "content_version"`
    );
  }
}
