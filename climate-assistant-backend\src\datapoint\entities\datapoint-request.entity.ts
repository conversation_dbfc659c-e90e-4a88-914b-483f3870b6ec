import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

import { DatapointDocumentChunk } from '../../datapoint-document-chunk/entities/datapoint-document-chunk.entity';
import { ESRSDatapoint } from './esrs-datapoint.entity';
import { DataRequest } from '../../data-request/entities/data-request.entity';
import { Comment } from '../../project/entities/comment.entity';
import { DatapointGeneration } from './datapoint-generation.entity';
import { CommentGeneration } from '../../project/entities/comment-generation.entity';

export enum DatapointRequestStatus {
  NotResponded = 'not_reported',
  IncompleteData = 'incomplete_data',
  CompleteData = 'complete_data',
}

export enum DatapointQueueStatus {
  QueuedForGeneration = 'queued_for_generation',
  QueuedForReview = 'queued_for_review',
}

@Entity()
export class DatapointRequest {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  dataRequestId: string;

  @Column('int')
  esrsDatapointId: number;

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'text', nullable: true })
  metadata: string;

  @Column({ type: 'text', default: '' })
  customUserRemark: string;

  @Column({ type: 'enum', enum: DatapointRequestStatus })
  status: DatapointRequestStatus;

  @Column({
    type: 'enum',
    enum: DatapointQueueStatus,
    default: null,
  })
  queueStatus: DatapointQueueStatus;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'uuid', nullable: true })
  content_version: string;

  @ManyToOne(() => DataRequest, (dataRequest) => dataRequest.datapointRequests)
  @JoinColumn({ name: 'dataRequestId' })
  dataRequest: DataRequest;

  @ManyToOne(() => ESRSDatapoint)
  @JoinColumn({ name: 'esrsDatapointId' })
  esrsDatapoint: ESRSDatapoint;

  @OneToMany(() => DatapointDocumentChunk, (ddc) => ddc.datapointRequest)
  datapointDocumentChunkMap: DatapointDocumentChunk[];

  @OneToMany(
    () => DatapointGeneration,
    (datapointGeneration) => datapointGeneration.datapointRequest
  )
  datapointGenerations: DatapointGeneration[];

  @OneToMany(() => Comment, (comment) => comment.datapointRequest, {
    nullable: true,
    createForeignKeyConstraints: false,
  })
  comments: Comment[];

  @OneToMany(
    () => CommentGeneration,
    (commentGeneration) => commentGeneration.dataRequest,
    {
      nullable: true,
      createForeignKeyConstraints: false,
    }
  )
  commentGenerations: CommentGeneration[];
}
