#!/bin/sh

echo "========================================"
echo "PM2 Instance Verification Report"
echo "========================================"
echo ""

# Check if PM2 is running
if ! pm2 list >/dev/null 2>&1; then
    echo "ERROR: PM2 is not running or not found"
    exit 1
fi

# Count instances
INSTANCE_COUNT=$(pm2 list | grep -c "climate-assistant-backend")
echo "✓ Number of PM2 instances: $INSTANCE_COUNT"

if [ "$INSTANCE_COUNT" -eq 3 ]; then
    echo "✓ SUCCESS: Running with 3 instances as expected"
else
    echo "✗ WARNING: Expected 3 instances, but found $INSTANCE_COUNT"
fi

echo ""
echo "Instance Details:"
echo "-----------------"

# Show process information
pm2 list | grep -E "(id|──|climate-assistant)" | while read line; do
    echo "$line"
done

echo ""
echo "Process Verification:"
echo "--------------------"

# Count actual Node.js processes
NODE_PROCESSES=$(ps aux | grep "dist/main.js" | grep -v grep | wc -l | tr -d ' ')
echo "✓ Node.js main processes: $NODE_PROCESSES"

echo ""
echo "Memory Usage Summary:"
echo "--------------------"

# Get memory usage for each instance
pm2 jlist 2>/dev/null | grep -o '"memory":[0-9]*' | grep -o '[0-9]*' | while read mem; do
    MEM_MB=$((mem / 1024 / 1024))
    echo "  Instance memory: ${MEM_MB}MB"
done

echo ""
echo "CPU Information:"
echo "----------------"

# Show CPU count in container
CPU_COUNT=$(nproc)
echo "✓ Available CPUs in container: $CPU_COUNT"

# Check if cluster mode is enabled
CLUSTER_MODE=$(pm2 jlist 2>/dev/null | grep -c '"exec_mode":"cluster_mode"')
if [ "$CLUSTER_MODE" -gt 0 ]; then
    echo "✓ Cluster mode: ENABLED"
else
    echo "✗ Cluster mode: DISABLED"
fi

echo ""
echo "========================================"
echo "To view real-time monitoring: pm2 monit"
echo "To view logs: pm2 logs"
echo "========================================" 