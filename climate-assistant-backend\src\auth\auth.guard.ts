import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
import { IS_PUBLIC_KEY } from './helpers';
import { Reflector } from '@nestjs/core';
import { UserWorkspace } from 'src/users/entities/user-workspace.entity';
import { USER_ROLES } from 'src/constants';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private jwtService: JwtService,
    private reflector: Reflector,
    private configService: ConfigService,
    @InjectRepository(UserWorkspace)
    private userWorkspaceRepository: Repository<UserWorkspace>
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const token = this.extractFromCookies(request);
    if (!token) {
      throw new UnauthorizedException();
    }
    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: this.configService.get<string>('JWT_SECRET'),
      });
      request['user'] = payload;
    } catch {
      throw new UnauthorizedException();
    }

    const requiredRoles = this.reflector.get<USER_ROLES[]>(
      'roles',
      context.getHandler()
    );
    if (!requiredRoles) {
      return true;
    }
    const userWorkspace = await this.userWorkspaceRepository.findOne({
      where: { userId: request.user.id, workspaceId: request.user.workspaceId },
      relations: ['role'],
    });
    if (!userWorkspace || !userWorkspace.role) {
      return false;
    }
    const userRole = userWorkspace.role.name;

    return requiredRoles.includes(userRole);
  }

  private extractFromCookies(request: Request): string | undefined {
    return request?.cookies['token'];
  }
}
