import { ESRSTopicDatapoint } from '../../knowledge-base/entities/esrs-topic-datapoint.entity';
import { ESRSDisclosureRequirement } from '../../knowledge-base/entities/esrs-disclosure-requirement.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  JoinColumn,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';

@Entity()
export class ESRSDatapoint {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar' })
  datapointId: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'boolean', default: false })
  optional: boolean;

  @Column({ type: 'boolean', default: false })
  conditional: boolean;

  @Column({ type: 'text', nullable: true })
  paragraph?: string;

  @Column({ type: 'text', nullable: true })
  relatedAR?: string;

  @Column({ type: 'text', nullable: true })
  lawText?: string;

  @Column({ type: 'text', nullable: true })
  lawTextAR?: string;

  @Column({ type: 'text', nullable: true })
  footnotes?: string;

  @Column({ type: 'text', nullable: true })
  footnotesAR?: string;

  @Column({ type: 'varchar', nullable: true })
  dataType?: string;

  @Column({ type: 'boolean', default: false })
  publicAccess: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @Column({ type: 'int', nullable: true })
  esrsDisclosureRequirementId: number;

  @Column({ type: 'text', nullable: true })
  exampleOutput: string;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(
    () => ESRSDisclosureRequirement,
    (disclosureRequirement) => disclosureRequirement.esrsDatapoints
  )
  @JoinColumn({ name: 'esrsDisclosureRequirementId' })
  esrsDisclosureRequirement: ESRSDisclosureRequirement;

  @OneToMany(() => ESRSTopicDatapoint, (relation) => relation.datapoint)
  topicRelations: ESRSTopicDatapoint[];
}
