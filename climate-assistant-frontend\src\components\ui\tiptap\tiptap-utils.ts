export function preprocessTiptapContent(
  content: string,
  refId?: string,
  shouldShowCitation?: boolean
): string {
  const processedContent = content.replace(
    /\[dpcite-(\d+)\|color-([#a-fA-F0-9]+)\|text-"([^"]+)"\]/g,
    (_match, index, color, text) => {
      return shouldShowCitation
        ? `<span data-modal-link="true" data-index="${index}" ${refId ? `data-refid="${refId}"` : ''} data-color="${color}" data-text="${text}"></span>`
        : '';
    }
  );

  //   console.log('processedContent', processedContent);
  return processedContent;
}
