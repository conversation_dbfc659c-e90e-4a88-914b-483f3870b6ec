#!/bin/bash

echo "Testing PM2 setup for Climate Assistant Backend"
echo "=============================================="

# Check if PM2 is installed
if ! command -v pm2 &> /dev/null; then
    echo "PM2 is not installed globally. Installing..."
    npm install -g pm2
fi

# Build the application
echo "Building the application..."
npm run build

# Start with PM2 in development mode
echo "Starting application with PM2..."
pm2 start ecosystem.dev.config.js

# Wait for processes to start
sleep 3

# Show status
echo ""
echo "PM2 Process Status:"
pm2 status

echo ""
echo "To view logs: pm2 logs"
echo "To stop: pm2 stop all"
echo "To delete: pm2 delete all"
echo ""
echo "Test production mode (3 instances):"
echo "pm2 delete all && NODE_ENV=production pm2 start ecosystem.config.js --env production" 