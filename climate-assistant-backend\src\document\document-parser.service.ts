import { Injectable } from '@nestjs/common';
import 'dotenv/config';
import type { DocumentChunkGenerated } from 'src/types';
import { LlmRateLimiterService } from 'src/llm-rate-limiter/llm-rate-limiter.service';
import { MarkdownTableService } from './services/markdown-table.service';
import { TableDetectionService } from './services/table-detection.service';
import { HtmlProcessingService } from './services/html-processing.service';
import { ChunkProcessingService } from './services/chunk-processing.service';
import { PdfProcessingService } from './services/pdf-processing.service';
import { SpreadsheetProcessingService } from './services/spreadsheet-processing.service';
import { TableAnalysisService } from './services/table-analysis.service';
import { WorkerLogger } from 'src/shared/logger.service';

@Injectable()
export class DocumentParserService {
  private readonly markdownTableService = new MarkdownTableService();
  private readonly tableAnalysisService = new TableAnalysisService();
  private readonly tableDetectionService = new TableDetectionService(
    this.llmRateLimitService,
    this.tableAnalysisService
  );
  private readonly htmlProcessingService = new HtmlProcessingService();
  private readonly chunkProcessingService = new ChunkProcessingService(
    this.htmlProcessingService
  );
  private readonly pdfProcessingService = new PdfProcessingService(
    this.htmlProcessingService,
    this.markdownTableService,
    this.chunkProcessingService
  );
  private readonly spreadsheetProcessingService =
    new SpreadsheetProcessingService(
      this.tableDetectionService,
      this.markdownTableService,
      this.chunkProcessingService,
      this.tableAnalysisService
    );

  constructor(private readonly llmRateLimitService: LlmRateLimiterService) {}
  private readonly logger = new WorkerLogger(DocumentParserService.name);

  async parseDocumentToMarkdown(
    path: string,
    premiumMode?: boolean
  ): Promise<DocumentChunkGenerated[]> {
    this.logger.log(
      `Starting to parse document: ${path}, premium mode: ${premiumMode}`
    );

    if (path.endsWith('.pdf')) {
      this.logger.log(`Detected PDF document: ${path}`);
      return this.parsePageBasedPDFToMarkdown(path, premiumMode);
    } else if (
      path.endsWith('.xlsx') ||
      path.endsWith('.xls') ||
      path.endsWith('.csv')
    ) {
      this.logger.log(`Detected spreadsheet document: ${path}`);
      return this.parseSpreadsheetToMarkdown(path);
    } else {
      this.logger.log(`Unsupported file type: ${path}`);
      throw new Error('Unsupported file type');
    }
  }

  /**
   * Process PDF document by page with overlapping content for context
   */
  async parsePageBasedPDFToMarkdown(
    filePath: string,
    premiumMode?: boolean
  ): Promise<DocumentChunkGenerated[]> {
    return this.pdfProcessingService.parsePageBasedPDFToMarkdown(
      filePath,
      premiumMode
    );
  }

  /**
   * Enhanced spreadsheet parsing with improved table detection for hidden rows/columns
   */
  private async parseSpreadsheetToMarkdown(
    path: string,
    maxTokens: number = 3000
  ): Promise<DocumentChunkGenerated[]> {
    return this.spreadsheetProcessingService.parseSpreadsheetToMarkdown(
      path,
      maxTokens
    );
  }
}
