import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';

import { USER_QUERY_KEY } from '../apiConstants';

import {
  fetchUserProfile,
  login,
  logout,
  resetPasswordMutation,
  sendPasswordResetEmailMutation,
  fetchUserPermissions,
} from '@/api/authentication/authentication.api.ts';
import { IUser } from '@/types/user';
import { usePermissions } from '@/context/permissionsContext';

export const useAuthentication = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { setPermissions } = usePermissions();

  const { data: user, isLoading } = useQuery<IUser | null>({
    queryKey: [USER_QUERY_KEY],
    retry: 0,
    queryFn: fetchUserProfile,
    staleTime: 1000 * 60 * 60,
  });

  useQuery({
    queryKey: ['permissions', user?.id],
    queryFn: async () => {
      try {
        const response = await fetchUserPermissions();
        setPermissions(response);
        return response;
      } catch (error) {
        console.error('Permission fetch error in queryFn:', error);
        throw error;
      }
    },
    enabled: !!user,
    retry: 3,
    staleTime: 1000 * 60 * 60, // Consider data fresh for 1 hour
    refetchOnWindowFocus: false,
  });

  const loginMutation = useMutation({
    mutationFn: login,
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: [USER_QUERY_KEY] });
      await queryClient.invalidateQueries({ queryKey: ['permissions'] });
      navigate('/');
    },
  });

  const logoutMutation = useMutation({
    mutationFn: logout,
    onSuccess: async () => {
      await queryClient.setQueryData([USER_QUERY_KEY], () => null);
      setPermissions([]);
      navigate('/login');
    },
  });

  const sessionReloadMutation = useMutation({
    mutationFn: fetchUserProfile,
    onSuccess: async () => {
      queryClient.clear();
    },
  });

  const sendPasswordResetEmail = useMutation({
    mutationFn: sendPasswordResetEmailMutation,
  });

  const resetPassword = useMutation({
    mutationFn: resetPasswordMutation,
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: [USER_QUERY_KEY] });
      navigate('/dashboard');
    },
  });

  // useEffect(() => {
  //   if (user != null) {
  //     void HotjarService.identifyUser();
  //   }
  //   if (user == null) {
  //     void MixpanelService.reset();
  //   }
  // }, [user]);

  return {
    user,
    isLoading,
    isLoginPending: loginMutation.isPending,
    login: loginMutation.mutate,
    logout: logoutMutation.mutate,
    sendPasswordResetEmail: sendPasswordResetEmail.mutate,
    resetPassword: resetPassword.mutate,
    restPasswordLoading: resetPassword.isPending,
    sendPasswordResetEmailLoading: sendPasswordResetEmail.isPending,
    loginErrors: loginMutation.error as string | string[] | null,
    sessionReloadMutation,
  };
};
