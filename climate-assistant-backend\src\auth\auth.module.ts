import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { AuthGuard } from './auth.guard';
import { PermissionGuard } from './guard/permission.guard';
import { PermissionService } from './services/permission.service';
import { Permission } from './entities/permission.entity';
import { RolePermission } from './entities/role-permission.entity';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    UsersModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      global: true,
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: '7 days' },
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([Permission, RolePermission]),
  ],
  providers: [AuthService, AuthGuard, PermissionService, PermissionGuard],
  controllers: [AuthController],
  exports: [JwtModule, AuthGuard, PermissionService, PermissionGuard],
})
export class AuthModule {}
