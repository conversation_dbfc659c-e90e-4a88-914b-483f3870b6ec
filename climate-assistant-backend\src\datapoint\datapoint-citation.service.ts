import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { DatapointRequest } from './entities/datapoint-request.entity';
import { DatapointDocumentChunk } from 'src/datapoint-document-chunk/entities/datapoint-document-chunk.entity';
import { DatapointGeneration } from './entities/datapoint-generation.entity';
import { WorkspaceService } from 'src/workspace/workspace.service';

@Injectable()
export class DatapointCitationService {
  constructor(
    @InjectRepository(DatapointRequest)
    private readonly datapointRequestRepository: Repository<DatapointRequest>,
    @InjectRepository(DatapointDocumentChunk)
    private readonly datapointDocumentChunkMapRepository: Repository<DatapointDocumentChunk>,
    @InjectRepository(DatapointGeneration)
    private readonly datapointGenerationRepository: Repository<DatapointGeneration>,
    private readonly workspaceService: WorkspaceService
  ) {}

  async findDatapointGenerationWithId(datapointGenerationId: string) {
    return await this.datapointGenerationRepository.findOne({
      where: { id: datapointGenerationId },
      relations: ['datapointRequest', 'evaluator'],
    });
  }

  async loadDatapointCitations({
    citationId,
    datapointRequest,
    datapointGeneration,
  }: {
    citationId: string;
    datapointRequest: DatapointRequest;
    datapointGeneration?: DatapointGeneration;
  }) {
    let citations: any = [];
    if (datapointGeneration) {
      const metadata = JSON.parse(datapointGeneration.data.metadata);
      citations = metadata ? (metadata.citation?.[citationId] ?? []) : [];
    } else {
      citations = datapointRequest.metadata
        ? (JSON.parse(datapointRequest.metadata).citation?.[citationId] ?? [])
        : [];
    }

    if (!citations || citations.length === 0) {
      return [];
    }

    const documentChunks = await this.datapointDocumentChunkMapRepository.find({
      where: {
        documentChunkId: In(citations.map((citation) => citation.id)),
      },
      relations: ['documentChunk.document'],
    });

    return citations.map((citation) => {
      const documentChunkMaps = documentChunks.find(
        (dc) => dc.documentChunk.id === citation.id
      );
      return {
        ...citation,
        ...documentChunkMaps.documentChunk,
      };
    });
  }

  async updateContentAndReplaceCitation({
    datapointRequestId,
    citationId,
    index,
    workspaceId,
    userId,
    datapointRequest,
  }: {
    datapointRequestId: string;
    citationId: string;
    index: number;
    workspaceId: string;
    userId: string;
    datapointRequest: DatapointRequest;
  }) {
    if (!datapointRequest || !datapointRequest.metadata) {
      throw new Error(
        `Datapoint request with ID ${datapointRequestId} not found`
      );
    }

    const metadata = JSON.parse(datapointRequest.metadata);
    metadata.citation = metadata.citation || {};
    const citations = metadata.citation[citationId] || [];

    const currentCitationIndex = citations.findIndex(
      (citation) => citation.active
    );
    if (!citations[currentCitationIndex]) {
      throw new Error(`No active citation found for citationId ${citationId}`);
    }

    const newCitationIndex = index;
    if (!citations[newCitationIndex]) {
      throw new Error(
        `Citation index ${newCitationIndex} is invalid for citationId ${citationId}`
      );
    }

    const replacementValue = citations[newCitationIndex].value;
    const activeCitationValue = citations[currentCitationIndex].value;

    const regexMarkdown = new RegExp(`\"${activeCitationValue}\"`, 'g');
    datapointRequest.content = datapointRequest.content.replace(
      regexMarkdown,
      `\"${replacementValue}\"`
    );

    citations[currentCitationIndex].active = false;
    citations[newCitationIndex].active = true;
    metadata.citation[citationId] = citations;
    datapointRequest.metadata = JSON.stringify(metadata);

    await this.datapointRequestRepository.save(datapointRequest);

    await this.workspaceService.storeActionHistory({
      event: 'datapoint_request_citation_replaced',
      ref: datapointRequestId,
      workspaceId: workspaceId,
      versionData: {
        event: 'datapoint_request_citation_replaced',
        doneBy: userId,
        data: datapointRequest,
      },
    });

    return datapointRequest;
  }
}
