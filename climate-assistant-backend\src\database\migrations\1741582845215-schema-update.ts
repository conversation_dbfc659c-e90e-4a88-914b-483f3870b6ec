import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1741582845215 implements MigrationInterface {
  name = 'SchemaUpdate1741582845215';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "workspace" DROP COLUMN "reportTextGenerationRules"`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "workspace" ADD "reportTextGenerationRules" text`
    );
  }
}
