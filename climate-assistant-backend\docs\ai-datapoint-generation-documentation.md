# AI Datapoint Generation and Review System Documentation

## Overview

This documentation covers the intricate workings of two core AI-powered functions in the Climate Assistant platform:

1. **`generateDatapointContentWithAI`** - Generates sustainability report content for ESRS datapoints using AI
2. **`reviewDatapointContentWithAI`** - Analyzes generated content to identify gaps and provide improvement suggestions

Both functions are part of the `DatapointRequestService` and form the backbone of the platform's AI-assisted sustainability reporting capabilities.

## Core Architecture

### Function Locations
- **Service**: `climate-assistant-backend/src/datapoint/datapoint-request.service.ts`
- **Controller**: `climate-assistant-backend/src/datapoint/datapoint-request.controller.ts`
- **Frontend API**: `climate-assistant-frontend/src/api/datapoint/datapoint-request.api.ts`

### Dependencies
- **Prompt Management**: Dynamic prompt system with database-stored templates
- **LLM Rate Limiting**: Controls API usage and prevents overload
- **Document Chunking**: Vector-based document retrieval for context
- **Citation System**: Automated source referencing and validation
- **Materiality Assessment**: Topic-based relevance filtering

## 1. generateDatapointContentWithAI Function

### Purpose
Generates high-quality, legally compliant sustainability report content for specific ESRS (European Sustainability Reporting Standards) datapoints using AI, incorporating company documents, materiality assessments, and regulatory requirements.

### Function Signature
```typescript
async generateDatapointContentWithAI({
  datapointRequestId: string,
  userId: string,
  workspaceId: string,
  useExistingReportTextForReference: boolean
}): Promise<void>
```

### Core Workflow

#### 1. Data Retrieval and Validation
```typescript
// Fetches datapoint with comprehensive relations
const datapointRequest = await this.datapointRequestRepository.findOne({
  where: { id: datapointRequestId },
  relations: {
    dataRequest: { project: true },
    esrsDatapoint: {
      esrsDisclosureRequirement: true,
      topicRelations: { topic: true }
    },
    datapointDocumentChunkMap: {
      documentChunk: { document: true }
    }
  }
});
```

#### 2. Context Generation
The system builds comprehensive context from multiple sources:

**Document Context**: 
- Retrieves linked document chunks via vector database
- Applies relevance filtering to reduce noise
- Maintains citation mappings for source attribution

**Company Profile**:
- Fetches general company information from workspace
- Includes industry, size, and operational context

**Related Datapoints**:
- Identifies other completed datapoints in the same disclosure requirement
- Provides consistency and continuity in reporting

#### 3. Datapoint Type Classification

The system intelligently categorizes datapoints into specialized types for optimized processing:

##### MDR (Minimum Disclosure Requirement) Types
- **MDR-A**: Actions and policies (DP_MDRA_GENERATION)
- **MDR-P**: Policies and commitments (DP_MDRP_GENERATION) 
- **MDR-T**: Targets and metrics (DP_MDRT_GENERATION)

**Detection Logic**:
```typescript
const h2Count = (datapointRequest.content.match(/<\/h2>/g) || []).length;
if (esrsDatapoint.datapointId.includes('MDR-A') && h2Count > 0) {
  feature = 'DP_MDRA_GENERATION';
}
```

##### Data Type Classifications

**Numeric Types**:
- `monetary`, `gYear`, `date`, `ghgEmissions`, `energy`, `intensity`
- `integer`, `decimal`, `volume`, `area`, `percent`, `mass`
- Uses specialized numeric prompt chains (DP_NUMERIC_GENERATION)

**Tabular Types**:
- Any dataType containing 'table' substring
- Complex table formats: `table 1/decimal`, `table 2/ghgEmissions`, etc.
- Uses table-specific formatting prompts (DP_TABLE_GENERATION)

**Narrative Types**:
- `narrative`, `semi-narrative`, `narrative/monetary`, `narrative/numerical`
- Uses default generation prompts (DP_DEFAULT_GENERATION)

**Boolean and Simple Types**:
- `boolean`, `date`
- Uses simplified generation logic

#### 4. Materiality Integration

The system incorporates sophisticated materiality assessment:

```typescript
const materialTopicsInHierarchy = await this.generateHierarchicalListOfTopics({
  topicRelations,
  projectId: datapointRequest.dataRequest.project.id,
  material: true
});
```

**Hierarchical Topic Structure**:
- **Topic Level**: E1, E2, E3, E4, E5, S1, S2, S3, S4, G1, General Disclosures
- **Sub-Topic Level**: Specific areas within main topics
- **Sub-Sub-Topic Level**: Granular reporting requirements

**Materiality Impact**:
- Material topics receive comprehensive reporting treatment
- Non-material topics get minimal coverage or exclusion notices
- Conditional datapoints reference materiality in gap analysis

#### 5. Prompt Chain System

The system uses a sophisticated prompt management architecture with chained prompts:

**A-Series Prompts (Primary Generation)**:
- **A1**: Main generation prompt with core variables
- **A2**: Material topics context (conditional)
- **A3**: Non-material topics context (conditional) 
- **A4**: Existing report text reference (conditional)
- **A5**: Conditional datapoint instructions (conditional)
- **A6**: Related datapoints context (conditional)

**B-Series Prompts (Enhancement)**:
- **B1**: MDR formatting improvement (MDR types only)

**Variable System**:
```typescript
const unifiedVariables = {
  esrsDatapoint,
  language: project.primaryContentLanguage,
  reportTextGenerationRules: project.reportTextGenerationRules,
  generalCompanyProfile,
  reportingYear: project.reportingYear,
  customUserRemark: datapointRequest.customUserRemark,
  documentContext: linkedChunksContext,
  currentContent: datapointRequest.content,
  otherDatapoints,
  relatedDatapoints
};
```

#### 6. Citation and Source Management

**Citation Extraction**:
```typescript
let { reportText: generatedContent, citation: generatedCitation } =
  extractCitationsFromReportTextGeneration(
    datapointGenerationResponse.response['datapoint'],
    documentChunksIndex
  );
```

**Citation Formats**:
- `<source>["chunk-1", "chunk-2"]</source>` - Simple source references
- `<sources-options>{"active":[{"chunk-3":"60%"}], "inactive":[{"chunk-6":"50%"}]}</sources-options>` - Weighted source options

**Citation Processing**:
- Converts AI-generated chunk references to actual document IDs
- Maintains active/inactive source distinctions
- Supports percentage-based relevance weighting

### Advanced Features

#### Controlled Generation (Dry Run Mode)
```typescript
async generateDatapointContentWithControlledAI({
  dryRun = false,
  testPrompts
})
```
- Allows testing with custom prompts without saving
- Returns generation results for preview
- Supports A/B testing of prompt variations

#### Performance Optimization
- **Rate Limiting**: Prevents API overuse via `LlmRateLimiterService`
- **Timing Tracking**: Monitors generation performance
- **Chunking Limits**: Manages document context size (300,000 character limit)
- **Parallel Processing**: Uses throat library for controlled concurrency

## 2. reviewDatapointContentWithAI Function

### Purpose
Analyzes completed datapoint content to identify gaps, inconsistencies, and areas for improvement, generating actionable feedback as system comments.

### Function Signature
```typescript
async reviewDatapointContentWithAI({
  datapointRequestId: string,
  userId: string,
  workspaceId: string
}): Promise<Comment[]>
```

### Core Workflow

#### 1. Content Validation
```typescript
const datapointRequest = await this.datapointRequestRepository.findOne({
  where: {
    id: datapointRequestId,
    status: Not(DatapointRequestStatus.NotResponded)
  }
});
```

#### 2. Gap Analysis Type Detection

**MDR Analysis**:
- Detects MDR datapoints with existing policies (h2 count > 0)
- Uses specialized MDR gap analysis prompts
- Focuses on policy completeness and compliance

**Standard Analysis**:
- General datapoint gap analysis
- Comprehensive requirement checking
- Cross-references with related datapoints

#### 3. Materiality-Aware Review

The review system considers materiality status:

```typescript
if (materialTopicsInHierarchy.length > 0) {
  prompts.push({
    role: 'system',
    content: this.promptService.datapointRequestGapAnalysisSystemPromptMaterialTopics({
      topics: materialTopicsInHierarchy,
      material: true
    })
  });
}
```

**Material Topics**: Strict compliance requirements
**Non-Material Topics**: Disclosure rationale validation

#### 4. Conditional Datapoint Handling

```typescript
if (esrsDatapoint.conditional) {
  prompts.push({
    role: 'system',
    content: `This is a conditional datapoint. If there is information missing, 
             it is up to the user whether they are ok with a gap, or not. 
             The conditions typically have "if relevant" or similar conditionals...`
  });
}
```

#### 5. Gap Analysis Response Processing

**Response Structure**:
```typescript
type GapAnalysis = {
  gapIdentified: boolean;
  gap?: string;
  actions?: string[];
  exampleText?: string;
  text?: string;
  disclaimer?: string;
  title?: string;
}
```

**Comment Generation**:
- **Gap Identified**: Creates structured feedback with actions and examples
- **No Gap**: Provides validation confirmation
- **HTML Formatting**: Structures comments for readability

```typescript
// Gap identified format
gapHtml = 
  (gapAnalysis.title ? "<h3>" + gapAnalysis.title + "</h3>" : "") +
  "<p><strong>Gap Identified:</strong> " + gapAnalysis.gap + "</p>" +
  "<p><strong>Recommended Actions:</strong></p>" +
  "<ul>" + gapAnalysis.actions.map(action => "<li>" + action + "</li>").join('') + "</ul>" +
  "<p><strong>Example Text:</strong>" + gapAnalysis.exampleText + "</p>";
```

## Data Types and Their Implications

### Quantitative Data Types

#### Energy and Emissions
- `energy`: Measured in standard energy units (MWh, GJ)
- `ghgEmissions`: CO2 equivalent measurements
- `intensity`: Ratio-based metrics (emissions per unit)

**Generation Approach**: Emphasizes precise measurement methodologies and calculation transparency

#### Monetary Types
- `monetary`: Financial impacts and investments
- `narrative/monetary`: Combined qualitative and financial reporting

**Generation Approach**: Requires financial validation and audit trail clarity

#### Physical Measurements
- `mass`: Weight-based metrics (tonnes, kg)
- `volume`: Volumetric measurements (liters, m³)
- `area`: Surface area measurements (hectares, m²)

**Generation Approach**: Focuses on measurement accuracy and unit consistency

### Qualitative Data Types

#### Narrative Types
- `narrative`: Pure text descriptions
- `semi-narrative`: Structured narrative with bullet points
- `narrative/numerical`: Combined text and figures

**Generation Approach**: Emphasizes clarity, compliance language, and stakeholder communication

#### Table Formats
- `table`: Basic tabular data
- `table 1/decimal`: Single-level table with decimal values
- `table 2/ghgEmissions`: Two-level table with emissions data
- `table 4/decimal/integer`: Complex multi-type table

**Generation Approach**: Structured formatting with proper headers and data validation

### Boolean and Temporal Types
- `boolean`: Yes/No disclosures
- `date`: Specific date requirements
- `gYear`: Year-based reporting

**Generation Approach**: Simple, direct responses with proper formatting

## ESRS Standards Integration

### Topic Hierarchy Structure

**E-Series (Environmental)**:
- **E1**: Climate Change - Transition plans, GHG emissions, climate risks
- **E2**: Pollution - Air, water, soil pollution management
- **E3**: Water and Marine Resources - Water consumption, marine impact
- **E4**: Biodiversity and Ecosystems - Nature-related impacts and dependencies  
- **E5**: Resource Use and Circular Economy - Material flows and circularity

**S-Series (Social)**:
- **S1**: Own Workforce - Employee-related disclosures
- **S2**: Workers in Value Chain - Supplier workforce conditions
- **S3**: Affected Communities - Community impact and engagement
- **S4**: Consumers and End-users - Product and service impacts

**G-Series (Governance)**:
- **G1**: Business Conduct - Ethics, corruption, lobbying

**General Disclosures**: Cross-cutting governance and strategy elements

### Disclosure Requirement Mapping

Each datapoint belongs to a specific Disclosure Requirement (DR):
```typescript
// Example: E1.1-5 belongs to E1-1 (Transition plan for climate change mitigation)
const drRelation = esrsDatapoint.esrsDisclosureRequirement;
```

**DR Categories**:
- **IRO**: Impacts, Risks, and Opportunities assessment
- **Policies**: Management approach disclosures  
- **Actions**: Implementation measures and resources
- **Metrics**: Quantitative performance indicators
- **Targets**: Forward-looking commitments

## System Integration Points

### Database Schema Integration
- **DatapointRequest**: Core entity with content and metadata
- **ESRSDatapoint**: Regulatory requirement definitions
- **ESRSTopic**: Hierarchical topic structure
- **MaterialESRSTopic**: Company-specific materiality decisions
- **DocumentChunk**: Source document fragments with vector embeddings

### Prompt Management System
- **LlmPrompt**: Database-stored prompt templates
- **Chain Identifiers**: A1-A6, B1 prompt sequencing
- **Feature Types**: DP_MDRA_GENERATION, DP_NUMERIC_GENERATION, etc.
- **Variable Interpolation**: Dynamic content insertion

### Rate Limiting and Performance
- **Concurrency Control**: throat(10) for parallel operations
- **API Rate Limiting**: Prevents LLM API overuse
- **Document Size Limits**: 300,000 character context maximum
- **Timing Tracking**: Performance monitoring and optimization

### User Role Integration
- **SuperAdmin**: Access to controlled generation and evaluation features
- **AiContributor**: Advanced AI functionality access
- **Standard Users**: Basic generation and review capabilities

## Error Handling and Edge Cases

### Validation Failures
- Missing datapoint entities throw `NotFoundException`
- Invalid user permissions block generation attempts
- Rate limit exceeded triggers delayed retry logic

### Content Processing Edge Cases
- Empty document context falls back to general knowledge
- Malformed citations use fallback source attribution
- MDR detection failures default to standard generation

### Materiality Edge Cases
- Conditional datapoints receive special handling in gap analysis
- Non-material topics get abbreviated treatment
- Missing materiality data triggers warning notifications

## Best Practices and Optimization

### Prompt Engineering
- Use specific, legally accurate language in prompts
- Include comprehensive context variables
- Maintain consistent citation formatting requirements
- Leverage conditional prompt chains for efficiency

### Performance Optimization
- Pre-filter document chunks for relevance
- Use appropriate LLM models based on complexity
- Implement proper caching for repeated requests
- Monitor and optimize prompt token usage

### Quality Assurance
- Validate generated content against legal requirements
- Cross-reference citations with source documents
- Ensure materiality considerations are properly applied
- Maintain audit trails for generation decisions

## Conclusion

The AI datapoint generation and review system represents a sophisticated integration of regulatory knowledge, document intelligence, and natural language generation. By understanding the intricate relationships between data types, materiality assessments, ESRS standards, and prompt management, users can effectively leverage this system to produce high-quality, compliant sustainability reports.

The system's modular architecture allows for continuous improvement and adaptation to evolving regulatory requirements while maintaining consistency and reliability in report generation. 