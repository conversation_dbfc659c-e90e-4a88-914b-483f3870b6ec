groups:
  # Critical alerts that require immediate attention
  - name: pm2_cluster_critical
    interval: 30s
    rules:
      # Worker is completely down
      - alert: WorkerDown
        expr: up{job="backend-workers"} == 0
        for: 1m
        labels:
          severity: critical
          team: backend
        annotations:
          summary: "PM2 Worker {{ $labels.worker_id }} is down"
          description: "Worker {{ $labels.worker_id }} has been down for more than 1 minute. This affects 33% of backend capacity."
          runbook_url: "https://wiki.company.com/runbooks/pm2-worker-down"

      # Worker memory usage is critically high
      - alert: WorkerHighMemory
        expr: process_resident_memory_bytes{job="backend-workers"} > 1.3e9
        for: 5m
        labels:
          severity: critical
          team: backend
        annotations:
          summary: "Worker {{ $labels.worker_id }} memory critically high"
          description: "Worker {{ $labels.worker_id }} memory usage is {{ $value | humanize }} (threshold: 1.3GB). Risk of OOM kill."

      # Worker is in a restart loop
      - alert: WorkerRestartLoop
        expr: increase(pm2_restart_count{name="climate-assistant-backend"}[5m]) > 2
        for: 5m
        labels:
          severity: critical
          team: backend
        annotations:
          summary: "Worker {{ $labels.instance }} is in restart loop"
          description: "Worker {{ $labels.instance }} has restarted {{ $value }} times in the last 5 minutes"

      # All workers are down
      - alert: AllWorkersDown
        expr: count(up{job="backend-workers"} == 1) == 0
        for: 30s
        labels:
          severity: critical
          team: backend
          page: true
        annotations:
          summary: "All PM2 workers are down - Complete backend outage!"
          description: "No PM2 workers are responding. Complete backend service outage."

  # Warning alerts that need attention but not immediate
  - name: pm2_cluster_warnings
    interval: 1m
    rules:
      # Load imbalance across workers
      - alert: LoadImbalance
        expr: |
          (
            stddev(rate(http_requests_total{job="backend-workers"}[5m])) 
            / 
            avg(rate(http_requests_total{job="backend-workers"}[5m]))
          ) > 0.2
        for: 10m
        labels:
          severity: warning
          team: backend
        annotations:
          summary: "Request load imbalance detected"
          description: "Request distribution variance across workers is {{ $value | humanizePercentage }}. Some workers may be overloaded."

      # High response time
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="backend-workers"}[5m])) > 1
        for: 5m
        labels:
          severity: warning
          team: backend
        annotations:
          summary: "High response time on worker {{ $labels.worker_id }}"
          description: "95th percentile response time is {{ $value | humanizeDuration }} on worker {{ $labels.worker_id }}"

      # Database connections approaching limit
      - alert: DatabaseConnectionsHigh
        expr: |
          sum(pg_stat_activity_count{state="active"}) by (worker_id) > 7
        for: 5m
        labels:
          severity: warning
          team: backend
        annotations:
          summary: "High database connections from worker {{ $labels.worker_id }}"
          description: "Worker {{ $labels.worker_id }} is using {{ $value }}/8 database connections"

      # Worker CPU usage high
      - alert: WorkerHighCPU
        expr: rate(process_cpu_seconds_total{job="backend-workers"}[5m]) * 100 > 80
        for: 10m
        labels:
          severity: warning
          team: backend
        annotations:
          summary: "Worker {{ $labels.worker_id }} CPU usage high"
          description: "Worker {{ $labels.worker_id }} CPU usage is {{ $value | humanize }}%"

      # Queue processing stuck
      - alert: QueueProcessingStuck
        expr: |
          sum(rate(queue_jobs_processed_total{job="backend-workers",status="completed"}[5m])) by (worker_id) == 0
          AND
          sum(queue_jobs_waiting) > 10
        for: 10m
        labels:
          severity: warning
          team: backend
        annotations:
          summary: "Queue processing stuck on worker {{ $labels.worker_id }}"
          description: "Worker {{ $labels.worker_id }} hasn't processed any queue jobs in 10 minutes while {{ $value }} jobs are waiting"

      # Error rate spike
      - alert: HighErrorRate
        expr: |
          (
            sum(rate(http_requests_total{job="backend-workers",status=~"5.."}[5m])) by (worker_id)
            /
            sum(rate(http_requests_total{job="backend-workers"}[5m])) by (worker_id)
          ) > 0.05
        for: 5m
        labels:
          severity: warning
          team: backend
        annotations:
          summary: "High error rate on worker {{ $labels.worker_id }}"
          description: "Worker {{ $labels.worker_id }} error rate is {{ $value | humanizePercentage }}"

      # Database pool exhaustion risk
      - alert: DatabasePoolExhaustion
        expr: |
          sum(pg_stat_activity_count{}) > 20
        for: 5m
        labels:
          severity: warning
          team: backend
        annotations:
          summary: "Database connection pool near exhaustion"
          description: "Total database connections: {{ $value }}/24. Risk of connection pool exhaustion."

  # Informational alerts for tracking
  - name: pm2_cluster_info
    interval: 5m
    rules:
      # Worker has been up for more than 24 hours (daily restart reminder)
      - alert: WorkerLongUptime
        expr: (time() - process_start_time_seconds{job="backend-workers"}) > 86400
        for: 5m
        labels:
          severity: info
          team: backend
        annotations:
          summary: "Worker {{ $labels.worker_id }} uptime exceeds 24 hours"
          description: "Worker {{ $labels.worker_id }} has been running for {{ $value | humanizeDuration }}. Consider monitoring for memory leaks."

      # Cron job execution tracking
      - alert: CronJobExecuted
        expr: increase(cron_job_executions_total{job="backend-workers",worker_id="0"}[5m]) > 0
        for: 1m
        labels:
          severity: info
          team: backend
        annotations:
          summary: "Cron job '{{ $labels.job_name }}' executed"
          description: "Cron job '{{ $labels.job_name }}' was executed on main worker (Worker 0)"

      # Worker recently restarted
      - alert: WorkerRestarted
        expr: changes(process_start_time_seconds{job="backend-workers"}[5m]) > 0
        for: 1m
        labels:
          severity: info
          team: backend
        annotations:
          summary: "Worker {{ $labels.worker_id }} restarted"
          description: "Worker {{ $labels.worker_id }} was restarted. New PID: {{ $labels.worker_pid }}" 