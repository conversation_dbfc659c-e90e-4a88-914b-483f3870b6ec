import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1741958222665 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'where applicable, a statement indicating, together with the related revenues, that the undertaking is active in:

            i.
            
            the fossil fuel (coal, oil and gas) sector ( 16 ) , (i.e., it derives revenues from exploration, mining, extraction, production, processing, storage, refining or distribution, including transportation, storage and trade, of fossil fuels as defined in Article 2, point (62), of Regulation (EU) 2018/1999 of the European Parliament and the Council ( 17 ) ), including a disaggregation of revenues derived from coal, from oil and from gas, as well as the revenues derived from Taxonomy-aligned economic activities related to fossil gas as required under Article 8(7)(a) of Commission Delegated Regulation 2021/2178 ( 18 ) ;' WHERE "datapointId" = 'SBM-1_09';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'where applicable, a statement indicating, together with the related revenues, that the undertaking is active in:
            
            i.
            
            the fossil fuel (coal, oil and gas) sector ( 16 ) , (i.e., it derives revenues from exploration, mining, extraction, production, processing, storage, refining or distribution, including transportation, storage and trade, of fossil fuels as defined in Article 2, point (62), of Regulation (EU) 2018/1999 of the European Parliament and the Council ( 17 ) ), including a disaggregation of revenues derived from coal, from oil and from gas, as well as the revenues derived from Taxonomy-aligned economic activities related to fossil gas as required under Article 8(7)(a) of Commission Delegated Regulation 2021/2178 ( 18 ) ;' WHERE "datapointId" = 'SBM-1_10';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'where applicable, a statement indicating, together with the related revenues, that the undertaking is active in:
            
            i.
            
            the fossil fuel (coal, oil and gas) sector ( 16 ) , (i.e., it derives revenues from exploration, mining, extraction, production, processing, storage, refining or distribution, including transportation, storage and trade, of fossil fuels as defined in Article 2, point (62), of Regulation (EU) 2018/1999 of the European Parliament and the Council ( 17 ) ), including a disaggregation of revenues derived from coal, from oil and from gas, as well as the revenues derived from Taxonomy-aligned economic activities related to fossil gas as required under Article 8(7)(a) of Commission Delegated Regulation 2021/2178 ( 18 ) ;' WHERE "datapointId" = 'SBM-1_11';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'where applicable, a statement indicating, together with the related revenues, that the undertaking is active in:
            
            i.
            
            the fossil fuel (coal, oil and gas) sector ( 16 ) , (i.e., it derives revenues from exploration, mining, extraction, production, processing, storage, refining or distribution, including transportation, storage and trade, of fossil fuels as defined in Article 2, point (62), of Regulation (EU) 2018/1999 of the European Parliament and the Council ( 17 ) ), including a disaggregation of revenues derived from coal, from oil and from gas, as well as the revenues derived from Taxonomy-aligned economic activities related to fossil gas as required under Article 8(7)(a) of Commission Delegated Regulation 2021/2178 ( 18 ) ;' WHERE "datapointId" = 'SBM-1_12';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'where applicable, a statement indicating, together with the related revenues, that the undertaking is active in:
            
            i.
            
            the fossil fuel (coal, oil and gas) sector ( 16 ) , (i.e., it derives revenues from exploration, mining, extraction, production, processing, storage, refining or distribution, including transportation, storage and trade, of fossil fuels as defined in Article 2, point (62), of Regulation (EU) 2018/1999 of the European Parliament and the Council ( 17 ) ), including a disaggregation of revenues derived from coal, from oil and from gas, as well as the revenues derived from Taxonomy-aligned economic activities related to fossil gas as required under Article 8(7)(a) of Commission Delegated Regulation 2021/2178 ( 18 ) ;' WHERE "datapointId" = 'SBM-1_13';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'where applicable, a statement indicating, together with the related revenues, that the undertaking is active in:
            
            i.
            
            the fossil fuel (coal, oil and gas) sector ( 16 ) , (i.e., it derives revenues from exploration, mining, extraction, production, processing, storage, refining or distribution, including transportation, storage and trade, of fossil fuels as defined in Article 2, point (62), of Regulation (EU) 2018/1999 of the European Parliament and the Council ( 17 ) ), including a disaggregation of revenues derived from coal, from oil and from gas, as well as the revenues derived from Taxonomy-aligned economic activities related to fossil gas as required under Article 8(7)(a) of Commission Delegated Regulation 2021/2178 ( 18 ) ;' WHERE "datapointId" = 'SBM-1_14';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'the anticipated financial effects of the undertaking’s material risks and opportunities on its financial position, financial performance and cash flows over the short-, medium- and long-term, including the reasonably expected time horizons for those effects. This shall include how the undertaking expects its financial position, financial performance and cash flows to change over the short, medium- and long-term, given its strategy to manage risks and opportunities, taking into consideration:
            
            i.
            
            its investment and disposal plans (for example, capital expenditure, major acquisitions and divestments, joint ventures, business transformation, innovation, new business areas and asset retirements), including plans the undertaking is not contractually committed to; and
            
            ii.
            
            its planned sources of funding to implement its strategy.' WHERE "datapointId" = 'SBM-3_09';`);

    await queryRunner.query(
      `UPDATE "esrs_datapoint" SET "lawText" = 'The undertaking shall disclose whether and how climate-related considerations are factored into the remuneration of members of the administrative, management and supervisory bodies , including if their performance has been assessed against the GHG emission reduction targets reported under Disclosure Requirement E1-4 and the percentage of the remuneration recognised in the current period that is linked to climate related considerations, with an explanation of what the climate considerations are.' WHERE "datapointId" = 'E1.GOV-3_01';`
    );

    await queryRunner.query(
      `UPDATE "esrs_datapoint" SET "lawText" = 'The undertaking shall disclose whether and how climate-related considerations are factored into the remuneration of members of the administrative, management and supervisory bodies , including if their performance has been assessed against the GHG emission reduction targets reported under Disclosure Requirement E1-4 and the percentage of the remuneration recognised in the current period that is linked to climate related considerations, with an explanation of what the climate considerations are.' WHERE "datapointId" = 'E1.GOV-3_02';`
    );

    await queryRunner.query(
      `UPDATE "esrs_datapoint" SET "lawText" = 'The undertaking shall disclose whether and how climate-related considerations are factored into the remuneration of members of the administrative, management and supervisory bodies , including if their performance has been assessed against the GHG emission reduction targets reported under Disclosure Requirement E1-4 and the percentage of the remuneration recognised in the current period that is linked to climate related considerations, with an explanation of what the climate considerations are.' WHERE "datapointId" = 'E1.GOV-3_03';`
    );

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'The undertaking shall describe the process to identify and assess climate-related impacts, risks and opportunities . This description shall include its process in relation to:
            climate-related physical risks in own operations and along the upstream and downstream value chain , in particular:
            
            i.
            
            the identification of climate-related hazards, considering at least high emission climate scenarios ; and
            
            ii.
            
            the assessment of how its assets and business activities may be exposed and are sensitive to these climate-related hazards, creating gross physical risks for the undertaking.' WHERE "datapointId" = 'E1.IRO-1_02';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'When disclosing the information on the processes to identify and assess physical risks as required under paragraph 20 (b), the undertaking shall explain whether and how:
            (a)
            
            it has identified climate-related hazards (see table below) over the short-, medium- and long-term and screened whether its assets and business activities may be exposed to these hazards;' WHERE "datapointId" = 'E1.IRO-1_03';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'When disclosing the information on the processes to identify and assess physical risks as required under paragraph 20 (b), the undertaking shall explain whether and how:
            (a)
            
            it has identified climate-related hazards (see table below) over the short-, medium- and long-term and screened whether its assets and business activities may be exposed to these hazards;' WHERE "datapointId" = 'E1.IRO-1_04';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'The undertaking shall describe the process to identify and assess climate-related impacts, risks and opportunities . This description shall include its process in relation to:
            climate-related transition risks and opportunities in own operations and along the upstream and downstream value chain , in particular:
            
            i.
            
            the identification of climate-related transition events, considering at least a climate scenario in line with limiting global warming to 1.5°C with no or limited overshoot; and
            
            ii.
            
            the assessment of how its assets and business activities may be exposed to these climate-related transition events, creating gross transition risks or opportunities for the undertaking.' WHERE "datapointId" = 'E1.IRO-1_09';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawTextAR" = 'When disclosing the information on the processes to identify transition risks and opportunities as required under paragraph 20 (c), the undertaking shall explain whether and how it has:
            (a)
            
            identified transition events (see the table with examples below) over the short-, medium- and long-term and screened whether its assets and business activities may be exposed to these events. In case of transition risks and opportunities, what is considered long-term may cover more than 10 years and may be aligned with climate-related public policy goals;' WHERE "datapointId" = 'E1.IRO-1_10';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'The undertaking shall indicate whether and how its policies address the following areas:
            (a)
            
            climate change mitigation ;
            
            (b)
            
            climate change adaptation ;
            
            (c)
            
            energy efficiency;
            
            (d)
            
            renewable energy deployment; and
            
            (e)
            
            other' WHERE "datapointId" = 'E1-2_01';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'In addition to ESRS 2 MDR-A, the undertaking shall:
            relate significant monetary amounts of CapEx and OpEx required to implement the actions taken or planned to:iii.
            
            if applicable, the CapEx plan required by Commission Delegated Regulation (EU) 2021/2178. 16c: The information required by paragraph 14 shall include:' WHERE "datapointId" = 'E1-3_07';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'In addition to ESRS 2 MDR-A, the undertaking shall:
            relate significant monetary amounts of CapEx and OpEx required to implement the actions taken or planned to:iii.
            
            if applicable, the CapEx plan required by Commission Delegated Regulation (EU) 2021/2178. 16c: The information required by paragraph 14 shall include:
            by reference to the climate change mitigation actions (as required by Disclosure Requirement E1-3), an explanation and quantification of the undertaking’s investments and funding supporting the implementation of its transition plan, with a reference to the key performance indicators of taxonomy-aligned CapEx, and where relevant the CapEx plans, that the undertaking discloses in accordance with Commission Delegated Regulation (EU) 2021/2178;' WHERE "datapointId" = 'E1-3_08';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'If the undertaking has set GHG emission reduction targets ( 39 ) , ESRS 2 MDR-T and the following requirements shall apply:
            (a)
            
            GHG emission reduction targets shall be disclosed in absolute value (either in tonnes of CO2eq or as a percentage of the emissions of a base year) and, where relevant, in intensity value;
            
            (b)
            
            GHG emission reduction targets shall be disclosed for Scope 1, 2 , and 3 GHG emissions, either separately or combined. The undertaking shall specify, in case of combined GHG emission reduction targets , which GHG emission Scopes (1, 2 and/or 3) are covered by the target, the share related to each respective GHG emission Scope and which GHGs are covered. The undertaking shall explain how the consistency of these targets with its GHG inventory boundaries is ensured (as required by Disclosure Requirement E1-6). The GHG emission reduction targets shall be gross targets, meaning that the undertaking shall not include GHG removals, carbon credits or avoided emissions as a means of achieving the GHG emission reduction targets;' WHERE "datapointId" = 'E1-4_03';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'If the undertaking has set GHG emission reduction targets ( 39 ) , ESRS 2 MDR-T and the following requirements shall apply:
            (a)
            
            GHG emission reduction targets shall be disclosed in absolute value (either in tonnes of CO2eq or as a percentage of the emissions of a base year) and, where relevant, in intensity value;
            
            (b)
            
            GHG emission reduction targets shall be disclosed for Scope 1, 2 , and 3 GHG emissions, either separately or combined. The undertaking shall specify, in case of combined GHG emission reduction targets , which GHG emission Scopes (1, 2 and/or 3) are covered by the target, the share related to each respective GHG emission Scope and which GHGs are covered. The undertaking shall explain how the consistency of these targets with its GHG inventory boundaries is ensured (as required by Disclosure Requirement E1-6). The GHG emission reduction targets shall be gross targets, meaning that the undertaking shall not include GHG removals, carbon credits or avoided emissions as a means of achieving the GHG emission reduction targets;' WHERE "datapointId" = 'E1-4_04';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'If the undertaking has set GHG emission reduction targets ( 39 ) , ESRS 2 MDR-T and the following requirements shall apply:
            (a)
            
            GHG emission reduction targets shall be disclosed in absolute value (either in tonnes of CO2eq or as a percentage of the emissions of a base year) and, where relevant, in intensity value;
            
            (b)
            
            GHG emission reduction targets shall be disclosed for Scope 1, 2 , and 3 GHG emissions, either separately or combined. The undertaking shall specify, in case of combined GHG emission reduction targets , which GHG emission Scopes (1, 2 and/or 3) are covered by the target, the share related to each respective GHG emission Scope and which GHGs are covered. The undertaking shall explain how the consistency of these targets with its GHG inventory boundaries is ensured (as required by Disclosure Requirement E1-6). The GHG emission reduction targets shall be gross targets, meaning that the undertaking shall not include GHG removals, carbon credits or avoided emissions as a means of achieving the GHG emission reduction targets;' WHERE "datapointId" = 'E1-4_05';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'If the undertaking has set GHG emission reduction targets ( 39 ) , ESRS 2 MDR-T and the following requirements shall apply:
            (a)
            
            GHG emission reduction targets shall be disclosed in absolute value (either in tonnes of CO2eq or as a percentage of the emissions of a base year) and, where relevant, in intensity value;
            
            (b)
            
            GHG emission reduction targets shall be disclosed for Scope 1, 2 , and 3 GHG emissions, either separately or combined. The undertaking shall specify, in case of combined GHG emission reduction targets , which GHG emission Scopes (1, 2 and/or 3) are covered by the target, the share related to each respective GHG emission Scope and which GHGs are covered. The undertaking shall explain how the consistency of these targets with its GHG inventory boundaries is ensured (as required by Disclosure Requirement E1-6). The GHG emission reduction targets shall be gross targets, meaning that the undertaking shall not include GHG removals, carbon credits or avoided emissions as a means of achieving the GHG emission reduction targets;' WHERE "datapointId" = 'E1-4_06';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'If the undertaking has set GHG emission reduction targets ( 39 ) , ESRS 2 MDR-T and the following requirements shall apply:
            (a)
            
            GHG emission reduction targets shall be disclosed in absolute value (either in tonnes of CO2eq or as a percentage of the emissions of a base year) and, where relevant, in intensity value;
            
            (b)
            
            GHG emission reduction targets shall be disclosed for Scope 1, 2 , and 3 GHG emissions, either separately or combined. The undertaking shall specify, in case of combined GHG emission reduction targets , which GHG emission Scopes (1, 2 and/or 3) are covered by the target, the share related to each respective GHG emission Scope and which GHGs are covered. The undertaking shall explain how the consistency of these targets with its GHG inventory boundaries is ensured (as required by Disclosure Requirement E1-6). The GHG emission reduction targets shall be gross targets, meaning that the undertaking shall not include GHG removals, carbon credits or avoided emissions as a means of achieving the GHG emission reduction targets;' WHERE "datapointId" = 'E1-4_07';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'If the undertaking has set GHG emission reduction targets ( 39 ) , ESRS 2 MDR-T and the following requirements shall apply:
            (a)
            
            GHG emission reduction targets shall be disclosed in absolute value (either in tonnes of CO2eq or as a percentage of the emissions of a base year) and, where relevant, in intensity value;
            
            (b)
            
            GHG emission reduction targets shall be disclosed for Scope 1, 2 , and 3 GHG emissions, either separately or combined. The undertaking shall specify, in case of combined GHG emission reduction targets , which GHG emission Scopes (1, 2 and/or 3) are covered by the target, the share related to each respective GHG emission Scope and which GHGs are covered. The undertaking shall explain how the consistency of these targets with its GHG inventory boundaries is ensured (as required by Disclosure Requirement E1-6). The GHG emission reduction targets shall be gross targets, meaning that the undertaking shall not include GHG removals, carbon credits or avoided emissions as a means of achieving the GHG emission reduction targets;' WHERE "datapointId" = 'E1-4_08';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'If the undertaking has set GHG emission reduction targets ( 39 ) , ESRS 2 MDR-T and the following requirements shall apply:
            (a)
            
            GHG emission reduction targets shall be disclosed in absolute value (either in tonnes of CO2eq or as a percentage of the emissions of a base year) and, where relevant, in intensity value;
            
            (b)
            
            GHG emission reduction targets shall be disclosed for Scope 1, 2 , and 3 GHG emissions, either separately or combined. The undertaking shall specify, in case of combined GHG emission reduction targets , which GHG emission Scopes (1, 2 and/or 3) are covered by the target, the share related to each respective GHG emission Scope and which GHGs are covered. The undertaking shall explain how the consistency of these targets with its GHG inventory boundaries is ensured (as required by Disclosure Requirement E1-6). The GHG emission reduction targets shall be gross targets, meaning that the undertaking shall not include GHG removals, carbon credits or avoided emissions as a means of achieving the GHG emission reduction targets;' WHERE "datapointId" = 'E1-4_09';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'If the undertaking has set GHG emission reduction targets ( 39 ) , ESRS 2 MDR-T and the following requirements shall apply:
            (a)
            
            GHG emission reduction targets shall be disclosed in absolute value (either in tonnes of CO2eq or as a percentage of the emissions of a base year) and, where relevant, in intensity value;
            
            (b)
            
            GHG emission reduction targets shall be disclosed for Scope 1, 2 , and 3 GHG emissions, either separately or combined. The undertaking shall specify, in case of combined GHG emission reduction targets , which GHG emission Scopes (1, 2 and/or 3) are covered by the target, the share related to each respective GHG emission Scope and which GHGs are covered. The undertaking shall explain how the consistency of these targets with its GHG inventory boundaries is ensured (as required by Disclosure Requirement E1-6). The GHG emission reduction targets shall be gross targets, meaning that the undertaking shall not include GHG removals, carbon credits or avoided emissions as a means of achieving the GHG emission reduction targets;' WHERE "datapointId" = 'E1-4_10';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'If the undertaking has set GHG emission reduction targets ( 39 ) , ESRS 2 MDR-T and the following requirements shall apply:
            (a)
            
            GHG emission reduction targets shall be disclosed in absolute value (either in tonnes of CO2eq or as a percentage of the emissions of a base year) and, where relevant, in intensity value;
            
            (b)
            
            GHG emission reduction targets shall be disclosed for Scope 1, 2 , and 3 GHG emissions, either separately or combined. The undertaking shall specify, in case of combined GHG emission reduction targets , which GHG emission Scopes (1, 2 and/or 3) are covered by the target, the share related to each respective GHG emission Scope and which GHGs are covered. The undertaking shall explain how the consistency of these targets with its GHG inventory boundaries is ensured (as required by Disclosure Requirement E1-6). The GHG emission reduction targets shall be gross targets, meaning that the undertaking shall not include GHG removals, carbon credits or avoided emissions as a means of achieving the GHG emission reduction targets;' WHERE "datapointId" = 'E1-4_11';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'If the undertaking has set GHG emission reduction targets ( 39 ) , ESRS 2 MDR-T and the following requirements shall apply:
            (a)
            
            GHG emission reduction targets shall be disclosed in absolute value (either in tonnes of CO2eq or as a percentage of the emissions of a base year) and, where relevant, in intensity value;
            
            (b)
            
            GHG emission reduction targets shall be disclosed for Scope 1, 2 , and 3 GHG emissions, either separately or combined. The undertaking shall specify, in case of combined GHG emission reduction targets , which GHG emission Scopes (1, 2 and/or 3) are covered by the target, the share related to each respective GHG emission Scope and which GHGs are covered. The undertaking shall explain how the consistency of these targets with its GHG inventory boundaries is ensured (as required by Disclosure Requirement E1-6). The GHG emission reduction targets shall be gross targets, meaning that the undertaking shall not include GHG removals, carbon credits or avoided emissions as a means of achieving the GHG emission reduction targets;' WHERE "datapointId" = 'E1-4_12';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'If the undertaking has set GHG emission reduction targets ( 39 ) , ESRS 2 MDR-T and the following requirements shall apply:
            (a)
            
            GHG emission reduction targets shall be disclosed in absolute value (either in tonnes of CO2eq or as a percentage of the emissions of a base year) and, where relevant, in intensity value;
            
            (b)
            
            GHG emission reduction targets shall be disclosed for Scope 1, 2 , and 3 GHG emissions, either separately or combined. The undertaking shall specify, in case of combined GHG emission reduction targets , which GHG emission Scopes (1, 2 and/or 3) are covered by the target, the share related to each respective GHG emission Scope and which GHGs are covered. The undertaking shall explain how the consistency of these targets with its GHG inventory boundaries is ensured (as required by Disclosure Requirement E1-6). The GHG emission reduction targets shall be gross targets, meaning that the undertaking shall not include GHG removals, carbon credits or avoided emissions as a means of achieving the GHG emission reduction targets;' WHERE "datapointId" = 'E1-4_13';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'If the undertaking has set GHG emission reduction targets ( 39 ) , ESRS 2 MDR-T and the following requirements shall apply:
            (a)
            
            GHG emission reduction targets shall be disclosed in absolute value (either in tonnes of CO2eq or as a percentage of the emissions of a base year) and, where relevant, in intensity value;
            
            (b)
            
            GHG emission reduction targets shall be disclosed for Scope 1, 2 , and 3 GHG emissions, either separately or combined. The undertaking shall specify, in case of combined GHG emission reduction targets , which GHG emission Scopes (1, 2 and/or 3) are covered by the target, the share related to each respective GHG emission Scope and which GHGs are covered. The undertaking shall explain how the consistency of these targets with its GHG inventory boundaries is ensured (as required by Disclosure Requirement E1-6). The GHG emission reduction targets shall be gross targets, meaning that the undertaking shall not include GHG removals, carbon credits or avoided emissions as a means of achieving the GHG emission reduction targets;' WHERE "datapointId" = 'E1-4_14';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'If the undertaking has set GHG emission reduction targets ( 39 ) , ESRS 2 MDR-T and the following requirements shall apply:
            (a)
            
            GHG emission reduction targets shall be disclosed in absolute value (either in tonnes of CO2eq or as a percentage of the emissions of a base year) and, where relevant, in intensity value;
            
            (b)
            
            GHG emission reduction targets shall be disclosed for Scope 1, 2 , and 3 GHG emissions, either separately or combined. The undertaking shall specify, in case of combined GHG emission reduction targets , which GHG emission Scopes (1, 2 and/or 3) are covered by the target, the share related to each respective GHG emission Scope and which GHGs are covered. The undertaking shall explain how the consistency of these targets with its GHG inventory boundaries is ensured (as required by Disclosure Requirement E1-6). The GHG emission reduction targets shall be gross targets, meaning that the undertaking shall not include GHG removals, carbon credits or avoided emissions as a means of achieving the GHG emission reduction targets;' WHERE "datapointId" = 'E1-4_15';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'If the undertaking has set GHG emission reduction targets ( 39 ) , ESRS 2 MDR-T and the following requirements shall apply:
            (a)
            
            GHG emission reduction targets shall be disclosed in absolute value (either in tonnes of CO2eq or as a percentage of the emissions of a base year) and, where relevant, in intensity value;
            
            (b)
            
            GHG emission reduction targets shall be disclosed for Scope 1, 2 , and 3 GHG emissions, either separately or combined. The undertaking shall specify, in case of combined GHG emission reduction targets , which GHG emission Scopes (1, 2 and/or 3) are covered by the target, the share related to each respective GHG emission Scope and which GHGs are covered. The undertaking shall explain how the consistency of these targets with its GHG inventory boundaries is ensured (as required by Disclosure Requirement E1-6). The GHG emission reduction targets shall be gross targets, meaning that the undertaking shall not include GHG removals, carbon credits or avoided emissions as a means of achieving the GHG emission reduction targets;' WHERE "datapointId" = 'E1-4_16';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'If the undertaking has set GHG emission reduction targets ( 39 ) , ESRS 2 MDR-T and the following requirements shall apply:
            (a)
            
            GHG emission reduction targets shall be disclosed in absolute value (either in tonnes of CO2eq or as a percentage of the emissions of a base year) and, where relevant, in intensity value;
            
            (b)
            
            GHG emission reduction targets shall be disclosed for Scope 1, 2 , and 3 GHG emissions, either separately or combined. The undertaking shall specify, in case of combined GHG emission reduction targets , which GHG emission Scopes (1, 2 and/or 3) are covered by the target, the share related to each respective GHG emission Scope and which GHGs are covered. The undertaking shall explain how the consistency of these targets with its GHG inventory boundaries is ensured (as required by Disclosure Requirement E1-6). The GHG emission reduction targets shall be gross targets, meaning that the undertaking shall not include GHG removals, carbon credits or avoided emissions as a means of achieving the GHG emission reduction targets;' WHERE "datapointId" = 'E1-4_17';`);

    await queryRunner.query(
      `UPDATE "esrs_datapoint" SET "lawText" = 'If the undertaking has set GHG emission reduction targets ( 39 ) , ESRS 2 MDR-T and the following requirements shall apply:GHG emission reduction targets shall be disclosed for Scope 1, 2 , and 3 GHG emissions, either separately or combined. The undertaking shall specify, in case of combined GHG emission reduction targets , which GHG emission Scopes (1, 2 and/or 3) are covered by the target, the share related to each respective GHG emission Scope and which GHGs are covered. The undertaking shall explain how the consistency of these targets with its GHG inventory boundaries is ensured (as required by Disclosure Requirement E1-6). The GHG emission reduction targets shall be gross targets, meaning that the undertaking shall not include GHG removals, carbon credits or avoided emissions as a means of achieving the GHG emission reduction targets;' WHERE "datapointId" = 'E1-4_18:';`
    );

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'The undertaking shall disclose in metric tonnes of CO2eq its ( 45 ) :
            (a)
            
            gross Scope 1 GHG emissions;
            
            (b)
            
            gross Scope 2 GHG emissions;
            
            (c)
            
            gross Scope 3 GHG emissions; and
            
            (d)
            
            total GHG emissions.', "lawTextAR" = 'When preparing the information for reporting GHG emissions as required by paragraph 44, the undertaking shall:
            (a)
            
            consider the principles, requirements and guidance provided by the GHG Protocol Corporate Standard (version 2004). The undertaking may consider Commission Recommendation (EU) 2021/2279 ( 58 ) or the requirements stipulated by EN ISO 14064-1:2018. If the undertaking already applies the GHG accounting methodology of ISO 14064- 1: 2018, it shall nevertheless comply with the requirements of this standard (e.g., regarding reporting boundaries and the disclosure of market-based Scope 2 GHG emissions);
            
            (b)
            
            disclose the methodologies, significant assumptions and emissions factors used to calculate or measure GHG emissions accompanied by the reasons why they were chosen, and provide a reference or link to any calculation tools used;
            
            (c)
            
            include emissions of CO2, CH4, N2O, HFCs, PFCs, SF6, and NF3. Additional GHG may be considered when significant; and
            
            (d)
            
            use the most recent Global Warming Potential (GWP) values published by the IPCC based on a 100-year time horizon to calculate CO2eq emissions of non-CO2 gases.' WHERE "datapointId" = 'E1-6_01';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'For Scope 1 and Scope 2 emissions disclosed as required by paragraphs 44 (a) and (b) the undertaking shall disaggregate the information, separately disclosing emissions from:
            (a)
            
            the consolidated accounting group (the parent and subsidiaries); and
            
            (b)
            
            investees such as associates, joint ventures, or unconsolidated subsidiaries that are not fully consolidated in the financial statements of the consolidated accounting group, as well as contractual arrangements that are joint arrangements not structured through an entity (i.e., jointly controlled operations and assets), for which it has operational control.' WHERE "datapointId" = 'E1-6_02';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawTextAR" = 'When preparing the information on the total GHG emissions required under paragraph 52, the undertaking shall:
            (a)
            
            apply the following formulas to calculate the total GHG emissions:
            
            
            Image 6
            
            
            Image 7
            
            (b)
            
            disclose total GHG emissions with a distinction between emissions derived from the location-based and market-based methods applied while measuring the underlying Scope 2 GHG emissions.' WHERE "datapointId" = 'E1-6_12';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawTextAR" = 'When preparing the information on the total GHG emissions required under paragraph 52, the undertaking shall:
            (a)
            
            apply the following formulas to calculate the total GHG emissions:
            
            
            Image 6
            
            
            Image 7
            
            (b)
            
            disclose total GHG emissions with a distinction between emissions derived from the location-based and market-based methods applied while measuring the underlying Scope 2 GHG emissions.' WHERE "datapointId" = 'E1-6_13';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawTextAR" = 'When preparing the information on gross Scope 3 GHG emissions required under paragraph 51, the undertaking shall:
            (a)
            
            consider the principles and provisions of the GHG Protocol Corporate Value Chain (Scope 3) Accounting and Reporting Standard (Version 2011); and it may consider Commission Recommendation (EU) 2021/2279 or the relevant requirements for the quantification of indirect GHG emissions from EN ISO 14064-1:2018;
            
            (b)
            
            if it is a financial institution, consider the GHG Accounting and Reporting Standard for the Financial Industry from the Partnership for Carbon Accounting Financial (PCAF), specifically part A “Financed Emissions” (version December 2022);
            
            (c)
            
            screen its total Scope 3 GHG emissions based on the 15 Scope 3 categories identified by the GHG Protocol Corporate Standard and GHG Protocol Corporate Value Chain (Scope 3) Accounting and Reporting Standard (Version 2011) using appropriate estimates. Alternatively, it may screen its indirect GHG emissions based on the categories provided by EN ISO 14064-1:2018 clause 5.2.4 (excluding indirect GHG emissions from imported energy);
            
            (d)
            
            identify and disclose its significant Scope 3 categories based on the magnitude of their estimated GHG emissions and other criteria provided by GHG Protocol Corporate Value Chain (Scope 3) Accounting and Reporting Standard (Version 2011, p. 61 and 65-68) or EN ISO 14064-1:2018 Annex H.3.2, such as financial spend, influence, related transition risks and opportunities or stakeholder views;
            
            (e)
            
            calculate or estimate GHG emissions in significant Scope 3 categories using suitable emissions factors;
            
            (f)
            
            update Scope 3 GHG emissions in each significant category every year on the basis of current activity data; update the full Scope 3 GHG inventory at least every 3 years or on the occurrence of a significant event or a significant change in circumstances (a significant event or significant change in circumstances can, for example, relate to changes in the undertaking’s activities or structure, changes in the activities or structure of its upstream and downstream value chain(s), a change in calculation methodology or in the discovery of errors);”);
            
            (g)
            
            disclose the extent to which the undertaking’s Scope 3 GHG emissions are measured using inputs from specific activities within the entity’s upstream and downstream value chain, and disclose the percentage of emissions calculated using primary data obtained from suppliers or other value chain partners.
            
            (h)
            
            for each significant Scope 3 GHG category, disclose the reporting boundaries considered, the calculation methods for estimating the GHG emissions as well as if and which calculation tools were applied. The Scope 3 categories should be consistent with the GHGP and include:
            
            i.
            
            indirect Scope 3 GHG emissions from the consolidated accounting group (the parent and its subsidiaries),
            
            ii.
            
            indirect Scope 3 GHG emissions from associates, joint ventures, and unconsolidated subsidiaries for which the undertaking has the ability to control the operational activities and relationships (i.e., operational control),
            
            iii.
            
            Scope 1, 2 and 3 GHG emissions from associates, joint ventures, unconsolidated subsidiaries (investment entities) and joint arrangements for which the undertaking does not have operational control and when these entities are part of the undertaking’s upstream and dopwnstream value chain.
            
            (i)
            
            disclose a list of Scope 3 GHG emissions categories included in and excluded from the inventory with a justification for excluded Scope 3 categories;
            
            (j)
            
            disclose biogenic emissions of CO 2 from the combustion or biodegradation of biomass that occur in its upstream and downstream value chain separately from the gross Scope 3 GHG emissions, and include emissions of other types of GHG (such as CH 4 and N2O), and emissions of CO2 that occur in the life cycle of biomass other than from combustion or biodegradation (such as GHG emissions from processing or transporting biomass) in the calculation of Scope 3 GHG emissions;
            
            (k)
            
            not include any removals, or any purchased, sold or transferred carbon credits or GHG allowances in the calculation of Scope 3 GHG emissions;' WHERE "datapointId" = 'E1-6_11';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'or each significant Scope 3 GHG category, disclose the reporting boundaries considered, the calculation methods for estimating the GHG emissions as well as if and which calculation tools were applied. The Scope 3 categories should be consistent with the GHGP and include:
            
            i.
            
            indirect Scope 3 GHG emissions from the consolidated accounting group (the parent and its subsidiaries),
            
            ii.
            
            indirect Scope 3 GHG emissions from associates, joint ventures, and unconsolidated subsidiaries for which the undertaking has the ability to control the operational activities and relationships (i.e., operational control),
            
            iii.
            
            Scope 1, 2 and 3 GHG emissions from associates, joint ventures, unconsolidated subsidiaries (investment entities) and joint arrangements for which the undertaking does not have operational control and when these entities are part of the undertaking’s upstream and dopwnstream value chain.' WHERE "datapointId" = 'E1-6_29:';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawTextAR" = 'AR 65.
            
            When disclosing the information required under paragraphs 62 and 63, if applicable, the undertaking shall briefly explain whether and how the carbon prices used in internal carbon pricing schemes are consistent with those used in financial statements. This shall be done in respect of the internal carbon prices used for,
            (a)
            
            the assessment of the useful life and residual value of its assets (intangibles, property, plant and equipment);
            
            (b)
            
            the impairment of assets; and
            
            (c)
            
            the fair value measurement of assets acquired through business acquisitions.' WHERE "datapointId" = 'E1-8_09';`);

    await queryRunner.query(
      `UPDATE "esrs_datapoint" SET "lawText" = 'When disclosing the information on potential liabilities from material transition risks required under paragraph 67(d):In assessing its potential future liabilities, the undertaking may consider and disclose the number of Scope 1 GHG emission allowances within regulated emission trading schemes and the cumulative number of emission allowances stored (from previous allowances) at the beginning of the reporting period;' WHERE "datapointId" = 'E1-9_24';`
    );

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawTextAR" = 'When disclosing information under paragraph 12, the undertaking may include contextual information on the relations between its policies implemented and how they may contribute to the EU Action Plan “Towards a Zero Pollution for Air, Water and Soil” with for instance elements on:
            (a)
            
            how it is or may be affected by the targets and measures of the EU Action Plan and the revision of existing directives (e.g., the Industrial Emissions Directive);
            
            (b)
            
            how it intends to reduce its pollution footprint to contribute to these targets.' WHERE "datapointId" = 'E2-1_04';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = '19.
            
            In addition to ESRS 2 MDR-A, the undertaking may specify to which layer in the following mitigation hierarchy an action and resources can be allocated:
            (a)
            
            avoid pollution including any phase out of materials or compounds that have a negative impact (prevention of pollution at source);' WHERE "datapointId" = 'E2-2_01';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = '19.
            
            In addition to ESRS 2 MDR-A, the undertaking may specify to which layer in the following mitigation hierarchy an action and resources can be allocated:
            (a)
            
            avoid pollution including any phase out of materials or compounds that have a negative impact (prevention of pollution at source);' WHERE "datapointId" = 'E2-2_03';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'The undertaking shall specifically disclose:
            (a)
            
            whether or not it has sites located in or near biodiversity-sensitive areas and whether activities related to these sites negatively affect these areas by leading to the deterioration of natural habitats and the habitats of species and to the disturbance of the species for which a protected area has been designated; and' WHERE "datapointId" = 'E4.IRO-1_14';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'The undertaking shall disclose:
            (a)
            
            a list of material sites in its own operations, including sites under its operational control, based on the results of paragraph 17(a). The undertaking shall disclose these locations by:
            
            i.
            
            specifying the activities negatively affecting biodiversity sensitive areas ( 80 ) ;
            
            ii.
            
            providing a breakdown of sites according to the impacts and dependencies identified, and to the ecological status of the areas (with reference to the specific ecosystem baseline level) where they are located; and
            
            iii.
            
            specifying the biodiversity-sensitive areas impacted, for users to be able to determine the location and the responsible competent authority with regards to the activities specified in paragraph 16(a) i.
            
            (b)
            
            whether it has identified material negative impacts with regards to land degradation , desertification or soil sealing ( 81 ) ; and
            
            (c)
            
            whether it has operations that affect threatened species ( 82 ) .' WHERE "datapointId" = 'E4-2_11';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawTextAR" = 'The undertaking may disclose whether it considers an “avoidance” action plan, which prevents damaging actions before they take place. Avoidance often involves a decision to deviate from the business-as-usual project development path. An example of avoidance is altering the biodiversity and ecosystem footprint of a project to avoid destruction of natural habitat on the site and/or establishing set-asides where priority biodiversity values are present and will be conserved. At a minimum, avoidance should be considered where there are biodiversity and ecosystem-related values that are in one of the following categories: particularly vulnerable and irreplaceable, of particular concern to stakeholders , or where a cautious approach is warranted due to uncertainty about impact assessment or about the efficacy of management measures. The three main types of avoidance are defined below:
            (a)
            
            avoidance through site selection (Locate the entire project away from biodiversity-sensitive areas);
            
            (b)
            
            avoidance through project design (Configure infrastructure to preserve biodiversity-sensitive areas); and
            
            (c)
            
            avoidance through scheduling (Time project activities to account for patterns of species behaviour (e.g., breeding, migration) or ecosystem functions (e.g., river dynamics).' WHERE "datapointId" = 'E4-3_0';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'The disclosure shall include:
            (a) a quantification of the anticipated financial effects in monetary terms before considering biodiversity and ecosystems -related actions or where not possible without undue cost or effort, qualitative information. For financial effects arising from material opportunities, a quantification is not required if it would result in disclosure that does not meet the qualitative characteristics of information (see ESRS 1 Appendix B Qualitative characteristics of information ). The quantification of the anticipated financial effects in monetary terms may be a single amount or a range;' WHERE "datapointId" = 'E4-6_02:';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'the total amount by weight diverted from disposal, with a breakdown between hazardous waste and non-hazardous waste and a breakdown by the following recovery operation types:
            
            i.
            
            preparation for reuse;
            
            ii.
            
            recycling ; and
            
            iii.
            
            other recovery operations.' WHERE "datapointId" = 'E5-5_08';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'the amount by weight directed to disposal by waste treatment type and the total amount summing all three types, with a breakdown between hazardous waste and non-hazardous waste. The waste treatment types to be disclosed are:
            
            i.
            
            incineration ;
            
            ii.
            
            landfill; and
            
            iii.
            
            other disposal operations;' WHERE "datapointId" = 'E5-5_09';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawTextAR" = 'When providing contextual information under paragraph 40 the undertaking may:
            (a)
            
            explain the reasons for high weights of waste directed to disposal (e.g., local regulations that prohibit landfill of specific types of waste);
            
            (b)
            
            describe sector practices, sector standards, or external regulations that mandate a specific disposal operation; and
            
            (c)
            
            specify whether the data has been modelled or sourced from direct measurements, such as waste transfer notes from contracted waste collectors.' WHERE "datapointId" = 'E5-5_17';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'The disclosure shall include:
            (a)
            
            a quantification of the anticipated financial effects in monetary terms before considering resource use and circular economy-related actions, or where not possible without undue cost or effort, qualitative information. For financial effects arising from material opportunities, a quantification is not required if it would result in disclosure that does not meet the qualitative characteristics of information (see ESRS 1 Appendix B Qualitative characteristics of information );' WHERE "datapointId" = 'E5-6_02';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawTextAR" = 'AR 44.
            
            When disclosing the material risks and opportunities related to its impacts or dependencies on its own workforce , the undertaking may consider the following:
            (a)
            
            risks related to the undertaking’s impacts on its own workforce may include the reputational or legal exposure where people in the undertaking’s workforce are found to be subject to forced labour or child labour ;
            
            (b)
            
            risks related to the undertaking’s dependencies on its own workforce may include disruption of business operations where significant employee turnover or lack of skills/ training development threaten the undertaking’s business; and
            
            (c)
            
            opportunities related to the undertaking’s impacts on its own workforce may include market differentiation and greater customer appeal from guaranteeing decent pay and conditions for non-employees.' WHERE "datapointId" = 'S1.SBM-3_05';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'the total number by head count or full time equivalent (FTE) of:
            
            i.
            
            permanent employees, and breakdown by gender;
            
            ii.
            
            temporary employees, and breakdown by gender; and
            
            iii.
            
            non-guaranteed hours employees, and breakdown by gender.' WHERE "datapointId" = 'S1-6_07';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawTextAR" = 'The percentage of employees covered by collective bargaining agreements is calculated using the following formula:
            Number of employees covered by collective bargaining agreements / Number of employees x100' WHERE "datapointId" = 'S1-8_01';`);

    await queryRunner.query(
      `UPDATE "esrs_datapoint" SET "lawText" = 'If not all its employees are paid an adequate wage in line with applicable benchmarks, the undertaking shall disclose the countries where employees earn below the applicable adequate wage benchmark and the percentage of employees that earn below the applicable adequate wage benchmark for each of these countries.' WHERE "datapointId" = 'S1-10_02';`
    );

    await queryRunner.query(
      `UPDATE "esrs_datapoint" SET "lawText" = 'The undertaking may disclose breakdowns by employee category for the percentage of employees that participated in regular performance and career development and for the average number of training hours per employee.' WHERE "datapointId" = 'S1-13_05';`
    );

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawTextAR" = 'AR 101.
            
            When compiling the information required by paragraph 97 (b), the undertaking shall:
            (a)
            
            include all employees ;
            
            (b)
            
            consider, depending on the undertaking’s remuneration policies, all of the following:
            
            i.
            
            base salary, which is the sum of guaranteed, short-term, and non-variable cash compensation;
            
            ii.
            
            benefits in cash, which is the sum of the base salary and cash allowances, bonuses, commissions, cash profit-sharing, and other forms of variable cash payments;
            
            iii.
            
            benefits in kind, such as cars, private health insurance, life insurance, wellness programs; and
            
            iv.
            
            direct remuneration, which is the sum of benefits in cash, benefits in kind and total fair value of all annual long-term incentives (for example, stock option awards, restricted stock shares or units, performance stock shares or units, phantom stock shares, stock appreciation rights, and long-term cash awards).
            
            (c)
            
            apply the following formula for the annual total remuneration ratio:
            
            
            Image 14' WHERE "datapointId" = 'S1-16_02';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawTextAR" = 'The undertaking shall disclose:
            (a)
            
            the total number of incidents of discrimination , including harassment , reported in the reporting period ( 106 ) ;
            
            (b)
            
            the number of complaints filed through channels for people in the undertaking’s own workforce to raise concerns (including grievance mechanisms) and, where applicable, to the National Contact Points for OECD Multinational Enterprises related to the matters defined in paragraph 2 of this Standard, excluding those already reported in (a) above;
            
            (c)
            
            the total amount of fines, penalties, and compensation for damages as a result of the incidents and complaints disclosed above, and a reconciliation of such monetary amounts disclosed with the most relevant amount presented in the financial statements; and
            
            (d)
            
            where applicable, contextual information necessary to understand the data and how such data has been compiled.' WHERE "datapointId" = 'S1-17_13';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'how the undertaking protects whistleblowers, including:
            
            i.
            
            details on the establishment of internal whistleblower reporting channels, including whether the undertaking provides for information and training to its own workers and information about the designation and training of staff receiving reports; and
            
            ii.
            
            measures to protect against retaliation its own workers who are whistleblowers in accordance with the applicable law transposing Directive (EU) 2019/1937 of the European Parliament and of the Council ( 128 ) ;' WHERE "datapointId" = 'G1-1_05';`);

    await queryRunner.query(
      `UPDATE "esrs_datapoint" SET "lawText" = 'In relation to material impacts, the undertaking shall describe: actions taken, planned or underway to prevent or mitigate material negative impacts on affected communities;', "lawTextAR" = 'When disclosing the intended or achieved positive outcomes of its actions for affected communities a distinction is to be made between evidence of certain activities having occurred (for example, that x number of women community members have been provided with training on how to become local suppliers to the undertaking) from evidence of actual outcomes for affected communities (for example, that x women community members have set up small businesses and have had their contracts with the undertaking renewed year-on-year).' WHERE "datapointId" = 'S3-4_01';`
    );

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawTextAR" = 'Impacts on communities may stem from environmental matters which are disclosed by the undertaking under the ESRS E1 to E5. Examples include:
            (a)
            
            ESRS E1 Climate Change : The implementation of climate change mitigation plans may require the undertaking to invest in renewable energy projects that may affect the lands, territories and natural resources of indigenous peoples . If the undertaking does not consult with the affected indigenous peoples , it could negatively impact the affected communities’ right to free, prior and informed consent ;
            
            (b)
            
            ESRS E2 Pollution : The undertaking may negatively impact affected communities by failing to protect them from pollution from a particular production facility that causes them health-related issues;
            
            (c)
            
            ESRS E3 Water and marine sources : The undertaking may negatively impact the access to clean drinking water of communities when withdrawing water in water stressed areas;
            
            (d)
            
            ESRS E4 Biodiversity and ecosystems : The undertaking may negatively affect the livelihood of local farmers through operations that contaminate soil . Additional examples include the sealing of land through building new infrastructure, which can eradicate plant species that are critical for, for example, local biodiversity or to filter water for communities; or the introduction of invasive species (whether plants or animals) that can impact ecosystems and cause subsequent harm;
            
            (e)
            
            ESRS E5 Resource use and circular economy : The undertaking may negatively impact the lives of communities by affecting their health through the mismanagement of hazardous waste.' WHERE "datapointId" = 'S3-4_14';`);

    await queryRunner.query(`UPDATE "esrs_datapoint" SET "lawText" = 'In relation to material risks and opportunities, the undertaking shall describe:
            (a)
            
            what action is planned or underway to mitigate material risks for the undertaking arising from its impacts and dependencies on value chain workers and how it tracks effectiveness in practice', "lawTextAR" = 'The undertaking shall consider whether and how its process(es) to manage material risks related to value chain workers are integrated into its existing risk management process(es).' WHERE "datapointId" = 'S2-4_08';`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
