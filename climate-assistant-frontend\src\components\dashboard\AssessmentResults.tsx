import { useState } from 'react';
import { Button } from '../ui/button';
import { assessDatapointScore } from '@/api/datapoint/datapoint-request.api';

interface AssessmentDimension {
  dimension_id: string;
  score: number;
  gap_to_next_level: string;
  confidence: number;
}

interface AssessmentResult {
  overall_score: number;
  dimensions: AssessmentDimension[];
  reasoning: string;
  confidence: number;
  metadata: Record<string, any>;
}

interface AssessmentResultsProps {
  generationId: string;
  content: string;
}

export const AssessmentResults = ({
  generationId,
  content,
}: AssessmentResultsProps) => {
  const [assessmentResult, setAssessmentResult] =
    useState<AssessmentResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleAssessScore = async () => {
    try {
      setIsLoading(true);
      const result = await assessDatapointScore({
        datapointGenerationId: generationId,
        textContent: content,
      });
      console.log('Assessment Score Result:', result);
      setAssessmentResult(result);
    } catch (error) {
      console.error('Error assessing score:', error);
      alert('Error assessing score. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDimensionName = (dimensionId: string) => {
    return dimensionId
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const getScoreColor = (score: number) => {
    if (score >= 3.5) return 'text-green-600';
    if (score >= 2.5) return 'text-yellow-600';
    if (score >= 1.5) return 'text-orange-600';
    return 'text-red-600';
  };

  const getScoreBackground = (score: number) => {
    if (score >= 3.5) return 'bg-green-50 border-green-200';
    if (score >= 2.5) return 'bg-yellow-50 border-yellow-200';
    if (score >= 1.5) return 'bg-orange-50 border-orange-200';
    return 'bg-red-50 border-red-200';
  };

  return (
    <>
      <Button
        variant="outline"
        onClick={handleAssessScore}
        disabled={isLoading}
      >
        {isLoading ? 'Assessing...' : 'Assess Score'}
      </Button>

      {/* Assessment Results Display */}
      {assessmentResult && (
        <div
          className={`mt-4 p-4 border rounded-lg ${getScoreBackground(assessmentResult.overall_score)}`}
        >
          <div className="mb-4">
            <h4 className="text-lg font-semibold mb-2">Assessment Results</h4>
            <div className="flex items-center gap-2 mb-2">
              <span className="text-sm font-medium">Overall Score:</span>
              <span
                className={`text-lg font-bold ${getScoreColor(assessmentResult.overall_score)}`}
              >
                {assessmentResult.overall_score.toFixed(2)}/4.0
              </span>
              <span className="text-sm text-gray-600">
                (Confidence: {(assessmentResult.confidence * 100).toFixed(1)}%)
              </span>
            </div>

            {/* Reasoning */}
            <div className="mb-4">
              <h5 className="font-medium mb-1">Summary:</h5>
              <p className="text-sm text-gray-700 whitespace-pre-line">
                {assessmentResult.reasoning}
              </p>
            </div>

            {/* Dimensions */}
            <div>
              <h5 className="font-medium mb-2">Dimension Scores:</h5>
              <div className="space-y-3">
                {assessmentResult.dimensions.map((dimension) => (
                  <div
                    key={dimension.dimension_id}
                    className="border rounded p-3 bg-white"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-sm">
                        {formatDimensionName(dimension.dimension_id)}
                      </span>
                      <div className="flex items-center gap-2">
                        <span
                          className={`font-bold ${getScoreColor(dimension.score)}`}
                        >
                          {dimension.score}/4
                        </span>
                        <span className="text-xs text-gray-500">
                          ({(dimension.confidence * 100).toFixed(0)}%
                          confidence)
                        </span>
                      </div>
                    </div>
                    <p className="text-xs text-gray-600">
                      <strong>Improvement needed:</strong>{' '}
                      {dimension.gap_to_next_level}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
