import { MiddlewareConsumer, Module } from '@nestjs/common';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { WorkspaceModule } from './workspace/workspace.module';
import { APP_GUARD } from '@nestjs/core';
import { AuthGuard } from './auth/auth.guard';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { PostgresConnectionCredentialsOptions } from 'typeorm/driver/postgres/PostgresConnectionCredentialsOptions';
import { ChatModule } from './chat/chat.module';
import {
  createDataSourceWithVectorSupport,
  getDBHost,
  getEnvFilePath,
  getRedisHost,
  isDevelopment,
} from './env-helper';
import { DataSourceOptions } from 'typeorm';
import { KnowledgeBaseModule } from './knowledge-base/knowledge-base.module';
import { DocumentModule } from './document/document.module';
import { PromptModule } from './prompts/prompts.module';
import { DatapointDocumentChunkModule } from './datapoint-document-chunk/datapoint-document-chunk.module';
import { ProjectModule } from './project/project.module';
import { DatapointRequestModule } from './datapoint/datapoint-request.module';
import { DataRequestModule } from './data-request/data-request.module';
import { LoggerMiddleware } from './middleware/logger.middleware';
import { EmailModule } from './external/email.module';
import { BullModule } from '@nestjs/bull';
import { CronModule } from './cron/cron.module';
import { BullBoardModule } from '@bull-board/nestjs';
import { ExpressAdapter } from '@bull-board/express';
import { BullAdapter } from '@bull-board/api/bullAdapter';
import { ProcessQueueModule } from './process-queue/queue.module';
import { LlmRateLimiterModule } from './llm-rate-limiter/llm-rate-limiter.module';
import { PromptManagementModule } from './prompt-management/prompt-management.module';
import { HealthModule } from './health/health.module';
import { EventsModule } from './events/events.module';
import { CacheModule } from '@nestjs/cache-manager';
import * as redisStore from 'cache-manager-redis-store';
import { JobProcessor } from './types/jobs';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: getEnvFilePath(),
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const connectionCredentials: PostgresConnectionCredentialsOptions = {
          host: getDBHost(),
          port: configService.get('BACKEND_DB_PORT'),
          username: configService.get('BACKEND_DB_USER'),
          password: configService.get('BACKEND_DB_PASSWORD'),
          database: configService.get('BACKEND_DB_NAME'),
          // url: configService.get('BACKEND_DB_URL'),
        };

        return {
          type: 'postgres',
          ...connectionCredentials,

          // Connection pooling optimization for PM2 cluster mode (3 workers)
          extra: {
            max: 8, // Max connections per worker (8×3 = 24 total)
            min: 2, // Min connections per worker
            acquire: 30000, // Max time to get connection (30s)
            idle: 10000, // Max idle time before closing connection (10s)
            evict: 10000, // How often to check for idle connections (10s)
            acquireTimeoutMillis: 30000,
            idleTimeoutMillis: 10000,
          },

          // Query optimization 'query', 'error'
          logging: isDevelopment ? ['error'] : undefined,

          // Migration settings
          synchronize: false,
          autoLoadEntities: true,
          entities: ['dist/**/*.entity{.ts,.js}'],
          migrations: ['database/migrations/**/*{.ts,.js}'],
        };
      },
      dataSourceFactory: async (options: DataSourceOptions) =>
        createDataSourceWithVectorSupport(options),
      inject: [ConfigService],
    }),
    EmailModule,
    ConfigModule.forRoot(),
    BullModule.forRoot({
      redis: {
        host: getRedisHost(),
        port: 6379,
      },
    }),
    BullBoardModule.forRoot({
      route: '/api/queues',
      adapter: ExpressAdapter,
    }),
    BullBoardModule.forFeature(
      {
        name: JobProcessor.ChunkExtraction,
        adapter: BullAdapter,
      },
      {
        name: JobProcessor.ChunkDpLinking,
        adapter: BullAdapter,
      },
      {
        name: JobProcessor.DatapointGeneration,
        adapter: BullAdapter,
      },
      {
        name: JobProcessor.DatapointReview,
        adapter: BullAdapter,
      },
      {
        name: JobProcessor.LlmRequest,
        adapter: BullAdapter,
      }
    ),
    AuthModule,
    UsersModule,
    WorkspaceModule,
    ChatModule,
    KnowledgeBaseModule,
    DocumentModule,
    PromptModule,
    ProjectModule,
    DataRequestModule,
    LlmRateLimiterModule,
    DatapointRequestModule,
    DatapointDocumentChunkModule,
    CronModule,
    ProcessQueueModule,
    PromptManagementModule,
    HealthModule,
    EventsModule,
    CacheModule.register({
      isGlobal: true,
      store: redisStore,
      host: getRedisHost(),
      port: 6379,
    }),
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
  ],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(LoggerMiddleware).forRoutes('*');
  }
}
