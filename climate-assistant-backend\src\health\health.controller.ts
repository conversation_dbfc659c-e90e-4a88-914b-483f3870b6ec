import { Controller, Get, OnModuleD<PERSON>roy } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { DataSource } from 'typeorm';
import { InjectDataSource } from '@nestjs/typeorm';
import { Redis } from 'ioredis';
import { LLM_MODELS } from 'src/constants';
import { getRedisHost } from '../env-helper';
import {
  MODEL_LIMITS,
  RATE_LIMIT_CONFIG,
  getRedisKey,
  getModelStatus,
  calculateUsagePercentages,
} from '../utils/llm-rate-limit.constants';
import { Public } from '../auth/helpers';

@ApiTags('health')
@Controller('health')
export class HealthController implements OnModuleDestroy {
  private readonly redis: Redis;

  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {
    this.redis = new Redis({
      host: getRedisHost(),
      port: 6379,
    });
  }

  @Get()
  @Public()
  @ApiOperation({ summary: 'Health check endpoint' })
  @ApiResponse({
    status: 200,
    description: 'Service health status',
  })
  async getHealth() {
    const workerId = process.env.INSTANCE_ID || process.pid;

    try {
      // Test database connection
      const dbResult = await this.dataSource.query('SELECT 1 as test');
      const isDbConnected = dbResult?.[0]?.test === 1;

      // Get database connection info
      const connectionInfo = await this.dataSource.query(`
        SELECT 
          count(*) as active_connections,
          current_database() as database_name,
          version() as postgres_version
        FROM pg_stat_activity 
        WHERE state = 'active' AND datname = current_database()
      `);

      // Test Redis connection
      const redisPing = await this.redis.ping();
      const isRedisConnected = redisPing === 'PONG';

      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        worker: {
          id: workerId,
          pid: process.pid,
          uptime: Math.floor(process.uptime()),
          memoryUsage: process.memoryUsage(),
          // nodeVersion: process.version,
        },
        database: {
          connected: isDbConnected,
          activeConnections: connectionInfo[0]?.active_connections || 0,
          // databaseName: connectionInfo[0]?.database_name,
          // postgresVersion: connectionInfo[0]?.postgres_version?.split(' ')[0],
        },
        redis: {
          connected: isRedisConnected,
        },
        // environment: process.env.NODE_ENV,
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        worker: {
          id: workerId,
          pid: process.pid,
          uptime: Math.floor(process.uptime()),
          memoryUsage: process.memoryUsage(),
          nodeVersion: process.version,
        },
        database: {
          connected: false,
          error: error.message,
        },
        redis: {
          connected: false,
          error: error.message,
        },
        environment: process.env.NODE_ENV,
      };
    }
  }

  @Get('cluster')
  @Public()
  @ApiOperation({ summary: 'Cluster information' })
  @ApiResponse({
    status: 200,
    description: 'PM2 cluster information',
  })
  async getClusterInfo() {
    const isMainWorker =
      process.env.INSTANCE_ID === '0' || !process.env.INSTANCE_ID;

    return {
      worker: {
        id: process.env.INSTANCE_ID || process.pid,
        pid: process.pid,
        isMainWorker,
        startTime: new Date(Date.now() - process.uptime() * 1000).toISOString(),
        uptime: Math.floor(process.uptime()),
        roles: {
          cronJobs: isMainWorker,
          queueProcessing: true,
          apiRequests: true,
        },
      },
      cluster: {
        isClusterMode: !!process.env.INSTANCE_ID,
        instanceId: process.env.INSTANCE_ID,
      },
      system: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        cpus: require('os').cpus().length,
        totalMemory: require('os').totalmem(),
        freeMemory: require('os').freemem(),
      },
    };
  }

  @Get('llm-stats')
  @Public()
  @ApiOperation({ summary: 'LLM rate limiting statistics across all workers' })
  @ApiResponse({
    status: 200,
    description: 'Current LLM usage statistics from Redis',
  })
  async getLlmStats() {
    try {
      const stats = {};

      // Get stats for each model
      for (const model of Object.values(LLM_MODELS)) {
        const pipeline = this.redis.pipeline();
        pipeline.get(getRedisKey(model, 'tokens'));
        pipeline.get(getRedisKey(model, 'requests'));
        pipeline.get(getRedisKey(model, 'lastReset'));

        const results = await pipeline.exec();

        const tokensUsed = parseInt(results[0][1] as string) || 0;
        const requestsUsed = parseInt(results[1][1] as string) || 0;
        const lastReset = parseInt(results[2][1] as string) || 0;

        const limits = MODEL_LIMITS[model];
        const timeSinceReset = Date.now() - lastReset;
        const percentages = calculateUsagePercentages(
          tokensUsed,
          requestsUsed,
          limits
        );

        stats[model] = {
          usage: {
            tokensUsed,
            requestsUsed,
            ...percentages,
          },
          limits: limits || {
            maxTokensPerMinute: 'Unknown',
            maxRequestsPerMinute: 'Unknown',
          },
          resetInfo: {
            lastReset: lastReset ? new Date(lastReset).toISOString() : 'Never',
            timeSinceReset:
              timeSinceReset > 0
                ? `${Math.floor(timeSinceReset / 1000)}s`
                : 'Unknown',
            nextResetIn: lastReset
              ? Math.max(
                  0,
                  RATE_LIMIT_CONFIG.RESET_INTERVAL / 1000 -
                    Math.floor(timeSinceReset / 1000)
                ) + 's'
              : 'Unknown',
          },
          status: getModelStatus(tokensUsed, requestsUsed, limits),
        };
      }

      // Get Redis connection info
      const redisInfo = await this.redis.info('memory');
      const memoryUsage = redisInfo
        .split('\n')
        .find((line) => line.startsWith('used_memory_human:'))
        ?.split(':')[1]
        ?.trim();

      return {
        timestamp: new Date().toISOString(),
        worker: {
          id: process.env.INSTANCE_ID || process.pid,
          pid: process.pid,
        },
        redis: {
          connected: true,
          memoryUsage: memoryUsage || 'Unknown',
        },
        models: stats,
        summary: {
          totalActiveModels: Object.values(stats).filter(
            (stat: any) =>
              stat.usage.tokensUsed > 0 || stat.usage.requestsUsed > 0
          ).length,
          highUsageModels: Object.entries(stats)
            .filter(
              ([_, stat]: [string, any]) =>
                stat.usage.tokensPercentage > 70 ||
                stat.usage.requestsPercentage > 70
            )
            .map(([model, _]) => model),
        },
      };
    } catch (error) {
      return {
        timestamp: new Date().toISOString(),
        worker: {
          id: process.env.INSTANCE_ID || process.pid,
          pid: process.pid,
        },
        redis: {
          connected: false,
          error: error.message,
        },
        models: {},
        summary: {
          totalActiveModels: 0,
          highUsageModels: [],
        },
      };
    }
  }

  async onModuleDestroy() {
    await this.redis.quit();
  }
}
