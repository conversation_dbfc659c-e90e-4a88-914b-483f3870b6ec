import { Injectable } from '@nestjs/common';
import * as TurndownService from 'turndown';
import * as turndownPluginGfm from 'joplin-turndown-plugin-gfm';
import { ESRSDatapoint } from 'src/datapoint/entities/esrs-datapoint.entity';
import { CITATION_CLIENT_REGEX } from 'src/util/llm-response-util';
import { Language } from 'src/project/entities/project.entity';
import { LANGUAGE_MAP } from 'src/constants';

@Injectable()
export class TablePromptService {
  private readonly GENERATION_LANGUAGE = 'German';
  private readonly turndownService: TurndownService;
  private languageCodeToLanguage(languageCode: Language): string {
    return LANGUAGE_MAP[languageCode];
  }

  constructor() {
    this.turndownService = new TurndownService({
      headingStyle: 'atx',
      bulletListMarker: '*',
    });
    const tables = turndownPluginGfm.tables;
    const strikethrough = turndownPluginGfm.strikethrough;
    this.turndownService.use([tables, strikethrough]);
  }

  // old, not in use anymore
  generateTableDatapointContentGenerationSystemPrompt1() {
    return `**Role:**

  You are a language model that acts like a senior sustainability consultant with more than 20 years of experience in social topics for the industry the company is operating. Your task is to write a comprehensive ESG report to fulfill all the disclosure requirements for the report. Each disclosure requirement needs to include several datapoints. Some of these datapoints are reported together as one table. You generate those tables in markdown format.

  **Task**:
  Your task is to create tables for a combination of **Datapoints** that contains all relevant information according to the legal requirements. Optionally add potentially important considerations as specified by the company in the corresponding use case. The results should be formulated in such a way that they can be directly integrated into the company’s sustainability report. For the given Datapoint all relevant requirements must be considered. In order to improve your performance utilise chain-of-thought: In order to allow for automated processing, return nothing else than a json with these three keys: "chainOfThought", "missing data", "datapoint". The "chainOfThought" should be a list of the most important (data)points you have identified in the context that are relevant for the datapoint for you to increase extraction performance. The "missing data" should be a list of the most important data that is required according to the legal text but missing in the context. The "datapoint" should then be the actual table you are creating in markdown. Optionally you can add text that is shown after the table as an explanation. Make sure to not include any information that does not belong to this datapoint, but to a related one from the same disclosure requirement.
  
  Data found as <Context>:
  --------------`;
  }

  // data is being inserted in between here

  // old, not in use anymore
  generateDatapointContentGenerationExample(exampleOutput: string) {
    return `
  -------------- (End of Text snippet context)
  --------------
  Learn from this one-shot example json with masked facts what the output can look like:
  {"chainOfThought":
  [
    "Company has established clear governance structures for sustainability oversight",
    "Transition plan is formally integrated into corporate strategy via Articles of Association",
    "Regular monitoring and reporting mechanisms are in place",
  ],
  "missing data":"
  [
    "Quantitative metrics used to track transition plan progress",
    "Frequency of sustainability reporting to board",
    "Specific budget allocations for transition plan implementation"
  ],// only elements that are actually mandated by the legal text of the datapoint, do not belong to a related DP and are missing in the context
  "datapoint": "${exampleOutput}"}
  -------------- (End of Example text)`;
  }

  // old, not in use anymore
  generateDatapointContentGenerationSpecificDPSystemPrompt(
    esrsDatapoints: ESRSDatapoint[],
    otherDatapoints: ESRSDatapoint[] // related ones that are not part of the table
  ): string {
    const otherDatapointsNames = otherDatapoints
      .map((datapoint) => datapoint.name)
      .join(', ');

    // insert here mulitple datapoints, if the table combines several ones
    // packing the following into a for loop:
    let prompt = 'Datapoints to report upon: ';
    for (const esrsDatapoint of esrsDatapoints) {
      prompt += `<Datapoint> **${esrsDatapoint.datapointId} - ${esrsDatapoint.name}**

    Here are the exact requirements for **${esrsDatapoint.datapointId}** based on the paragraphs from the official ESRS Document and the respective IDs matched to the exact paragraph. Your Task is to create ${esrsDatapoint.datapointId}.
    
    **Requirements of the Standard - extracted from [ESRS ${esrsDatapoint.esrsDisclosureRequirement.dr}](https://xbrl.efrag.org/e-esrs/esrs-set1-2023.html)**:
    `;

      prompt += this.createFullEsrsDatapointLawTextContext(esrsDatapoint);
      prompt += `</Datapoint>`;
    }

    prompt += `\n\n Here is a list of related Datapoints from the same Disclosure Requirement. Those are not part of this Datapoint and thus information that belongs to them should not marked as missing if it is not in this datapoint: <other Datapoints> [${otherDatapointsNames}] </other Datapoints>`;
    return prompt;
  }

  createFullEsrsDatapointLawTextContext(esrsDatapoint: ESRSDatapoint) {
    let esrsDatapointContenxt = `DP ${esrsDatapoint.datapointId}: ${esrsDatapoint.name}
  Requirements: ${esrsDatapoint.lawText}`;

    if (esrsDatapoint.footnotes) {
      esrsDatapointContenxt += `
    Footnotes: ${esrsDatapoint.footnotes}`;
    }
    if (esrsDatapoint.lawTextAR) {
      esrsDatapointContenxt += `
    Application Requirements: ${esrsDatapoint.lawTextAR}`;
    }
    if (esrsDatapoint.footnotesAR) {
      esrsDatapointContenxt += `
    Footnotes: ${esrsDatapoint.footnotesAR}`;
    }
    return esrsDatapointContenxt;
  }

  // old, not in use anymore
  generateDatapointContentGenerationSystemPrompt2({
    esrsDatapoint,
    generationLanguage,
    reportTextGenerationRules,
    customUserRemark,
  }: {
    esrsDatapoint: ESRSDatapoint;
    generationLanguage: Language;
    reportTextGenerationRules: string;
    customUserRemark: string;
  }) {
    const cleanCustomUserRemark = customUserRemark
      .replace(/<p>/g, '')
      .replace(/<\/p>/g, '')
      .trim();

    return `
  **Task**:
  Your task is to create tables for a combination of **Datapoints** that contains all relevant information according to the legal requirements. Optionally add potentially important considerations as specified by the company in the corresponding use case. The results should be formulated in such a way that they can be directly integrated into the company’s sustainability report. For the given Datapoint all relevant requirements must be considered. In order to improve your performance utilise chain-of-thought: In order to allow for automated processing, return nothing else than a json with these three keys: "chainOfThought", "missing data", "datapoint". The "chainOfThought" should be a list of the most important (data)points you have identified in the context that are relevant for the datapoint for you to increase extraction performance. The "missing data" should be a list of the most important data that is required according to the legal text but missing in the context. The "datapoint" should then be the actual table you are creating in markdown. Optionally you can add text that is shown after the table as an explanation. Make sure to not include any information that does not belong to this datapoint, but to a related one from the same disclosure requirement.

  **Procedure:**

  1. **Context Review:** Use the information provided in the the company’s documents to extract the necessary data and meet the requirements of the specific <Datapoint>.
  2. **Integration of Relevant Information**: Ensure that found data from the documents is written accordingly to given requirements and include relevant key figures and datapoints.
  3. Cite the information sources. Include information like title of the Document, Page Number and Paragraph Number in this format (Sustainability Report, P. 12, §4). If this is not available cite the first words or sentence of the respective source.4. Pre-Gap-Analysis: Clearly identify any missing or insufficiently covered information that prevents the company from fully meeting the requirements according to the legal requirements.
  5. Recommendation for Action Steps: Provide specific, actionable steps to address the gap and ensure compliance with the ${esrsDatapoint.datapointId}. Make sure to be as concrete as possible.

  Notes:
  1. Ensure that the writing is as complete as possible based on the given information.
  2. Ensure that the output is complete and coherent, meeting all the requirements of the relevant <Datapoint> within the standard.
  3. Avoid identifying gaps or missing information at this stage; focus solely on disclosing the requirements.
  4. DO NOT WRITE ABOUT ANYTHING THAT IS NOT SPECIFICALLY MENTIONED IN <Context>.
  5. If information are not available in the <Context> but are required from the standards, leave it out and DO NOT create Information that are not given.
  6. ONLY fulfill the described requirements and no other requirements, when writing the text.
  7. When fulfilling a requirement, include the ID and Pagraph ( specified before and summarize what it is about in the headline.
  8. The output structure should be in html. DO NOT ESCAPE THE HTML AS A STRING. JUST GIVE OUT THE PURE HTML. Do not add any css classes and just give the output as h1, h2, h3, p.
  9. Cite sources. For each information that is taken from a chunk, cite the chunk as [DOCUMENT-CHUNK: <ID>]

  **Generation Rules**
  ${reportTextGenerationRules !== '' ? `**Content Generation Rules**:\n${reportTextGenerationRules}` : ''}

  **Language**:
  All the outcomes that you generate are written in ${generationLanguage}
  ${cleanCustomUserRemark !== '' && cleanCustomUserRemark.length > 2 ? `**Additional User Remark (High Priority - CONSIDER THIS WHEN GENERATING THE DATAPOINT)**:\n${cleanCustomUserRemark}` : ''}
`;
  }

  generateTableSystemPrompt({
    esrsDatapoints,
    generationLanguage,
    reportTextGenerationRules,
    customUserRemark,
    currentContent,
    linkedChunks,
    otherDatapoints,
    reportingYear,
    generalCompanyProfile,
  }: {
    esrsDatapoints: ESRSDatapoint[];
    generationLanguage: Language;
    reportTextGenerationRules: string;
    customUserRemark: string;
    currentContent: string;
    linkedChunks: string;
    otherDatapoints: ESRSDatapoint[];
    reportingYear: string;
    generalCompanyProfile: string;
  }) {
    const otherDatapointsNames = otherDatapoints
      .map((datapoint) => datapoint.name)
      .join(', ');

    const cleanCustomUserRemark = customUserRemark
      .replace(/<p>/g, '')
      .replace(/<\/p>/g, '')
      .trim();

    const processedCurrentContent = currentContent
      ? this.turndownService.turndown(
          currentContent.replace(CITATION_CLIENT_REGEX, '$3')
        )
      : '';

    const datapointsString = esrsDatapoints
      .map((datapoint) => `[*${datapoint.datapointId} - ${datapoint.name}*]`)
      .join(', ');

    let lawtext = 'Datapoints to report upon: ';
    for (const esrsDatapoint of esrsDatapoints) {
      lawtext += `<Datapoint> **${esrsDatapoint.datapointId} - ${esrsDatapoint.name}**
  
      Here are the exact requirements for **${esrsDatapoint.datapointId}** based on the paragraphs from the official ESRS Document and the respective IDs matched to the exact paragraph. Your Task is to create ${esrsDatapoint.datapointId}.
      
      **Requirements of the Standard - extracted from [ESRS ${esrsDatapoint.esrsDisclosureRequirement.dr}](https://xbrl.efrag.org/e-esrs/esrs-set1-2023.html)**:
      `;

      lawtext += this.createFullEsrsDatapointLawTextContext(esrsDatapoint);
      lawtext += `</Datapoint>
      
      ${esrsDatapoint.exampleOutput ? `<Example Output Template>\n${esrsDatapoint.exampleOutput}\n</Example Output Template>` : ''}
      
      `;
    }

    const currentYear = new Date().getFullYear();

    return `
    You are an AI assistant tasked to craft tables and text that are inserted as you write them in a sustainability report of a European company. You write the text and table(s) in html for the datapoints ${datapointsString}. Follow the provided legal requirements of the EU's corporate sustainability reporting directive (CSRD) and correctly reference and cite the company's internal document chunks. Below there is chunks of internal documents retrieved from our database as context where you might find the required data. Use them as source to inform the paragraphs, numbers and table cells for the report, cite the Chunk IDs that are above each chunk (not the chunkNumber), write from their perspective and adhere to the requirements provided. If the required numbers are missing, do not report numbers that are not explicitly required by the law text instead, just report the numbers you have.

      The company has a range of disclosure requirements (DR) it has to report upon. Each DR consists of several datapoints (DPs). Some DPs are reported together in one table. The company has many documents with partially relevant data which are chunked up and stored in a db. Your task is to find relevant information for the table datapoints (DP) *${datapointsString}*, which the company has to report upon, from a list of mapped chunks and craft the table/text. Track the sources and cite them correctly. Consider all relevant legal requirements and write in a way that can be directly integrated into the company’s sustainability report.

      The contents of this prompt are
      1. *Instructions* with details on the requirements for your table datapoint text.
      2. *Legal Requirements* Law texts incl application requirements for each datapoint detailling what exactly to report.
      3. *Example output* what the generated json should exactly look like and bad examples for what it should not look like.
      4. *Context*: Mapped chunks from corporate documentation
      5. Final Remark instructions.

      **Instructions**:
      1. Analyze the legal requirements and the context to identify relevant facts as defined by the legal requirements of *these* datapoints as well as relevant considerations for them:  *${datapointsString}*. Usually the content is hidden somewhere in narrative text chunks, buried in well structured but irrelevant tables. Somewhere in the narrative sections, the right data is often to be found. If you cannot find them and during reasoning think its not there, look again in different unstructured narrative sections. And again. Information is more likely in narrative texts, than in well structured documents Ensure that the numeric values are as accurate as possible based on the given information and meets all the requirements of the relevant <Datapoint> within the standard. In particular consider source and time of claims, as sometimes there might be different claims for different values, stemming from subsidies of the company, data from previous years etc. In those cases, write them in the result in this format <sources-options>{"active":["chunk-3":"60%"], "inactive":["chunk-6": "50%", "chunk-2":"55%"]}</sources-options>. Here the active-key is the value in the text and its most important supporting source. The inactive-keys are optional, for when there is multiple sources, where some might be contradicting. Pick the chunk that's most likely the true, the most relevant source and recent as active. Put other supporting chunks with the same value or contradicting chunks to the inactive list.
      2. If you cannot find information, go back and look again in different places. Ensure the writing aligns with the DP's structural and content requirements. Use only information provided in the context. Do not infer or create additional information. Correctly cite all claims. If there is no relevant information available, you have looked again meticulously in each section and still cannot find anything, just literally state: "The required information is not provided in the context." Do not use any other phrasing for this, except this precise sentence and only use it as a last resort after multiple retries. But only do this if you really don't find relevant information. Before you decide for this sentence, search again, to be really sure you didn't overlook a chunk with data that ar applicable to the legal requirements to report. Often, there is a lot of irrelevant tables and you might think the relevant information is missing, but in fact it is hidden in a narrative section. If just part of the information is missing, leave those respective fields free and just fill out what is provided. Do not spend part of the report you are generating here discussing what information is provided or missing. Just write a draft with numeric table information provided, and if none of the required information is there, just literally generate the sentence before. But use the sentence as a last resort, if really nothing is there to report. First look again and check narrative sections.
      3. Use only information provided in the context. Do not infer or create additional information. Use only information actually asked for. Use the units described in the legal requirements. Correctly cite all claims.
      4. Use precise and professional language suitable for a corporate sustainability report.
      5. Format the output as JSON with HTML formatting within the "datapoint" section. Never introduce any empty lines for space. Do not wrap the table itself in <p>-tags.
      6. Cite sources using the format: <sources-options>{"active":["chunk-1":"500 TEUR"], "inactive":["chunk-9": "300 TEUR", "chunk-12":"5 Million"]}</sources-options>. Use this in-text instead of writing the number, the citation itself is being rendered as the number. The number is the document chunk id at the top of each document chunk (e.g. Document Chunk ID: chunk-3) and typically is the position of the chunk in the array of chunks provided to you. For some chunks there is chunkNumber mentioned at the end of the text. This chunkNumber is just the position of the chunk within its source document, this is NOT the document chunk ID that you have to cite here.
      7.  All the outcomes incl reasoning tokens that you generate are written in ${LANGUAGE_MAP[generationLanguage]} language.
      8. ${reportTextGenerationRules !== '' ? `**Content Generation Rules**:\n${reportTextGenerationRules} \n Prioritize strictly adhering to these rules.` : ''}
      ${!!cleanCustomUserRemark && cleanCustomUserRemark.length > 5 ? `10. **USER INSTRUCTION (HIGHEST PRIORITY)**:\n${cleanCustomUserRemark}\n\nYou MUST incorporate this instruction when generating the datapoint text.` : ''}
      ${!!currentContent && `9.1: **EXISTING CONTENT**:\n${this.turndownService.turndown(currentContent.replace(CITATION_CLIENT_REGEX, '$3'))}\n\nThe datapoint already contains the text above. The output you generate will overwrite it. The user is likely requesting either:\n- Quality improvements to this existing text\n- Specific modifications based on their instruction above\n\nYour response should build upon this content while addressing the user's needs.`}
      ${!!generalCompanyProfile && `9..2: **GENERAL COMPANY PROFILE**:\n${this.turndownService.turndown(generalCompanyProfile)}\n\nThis is the general company profile. Use this information to inform your generation of the data point. In particular focus on company structure and which year is being reported for.`}
      10. Prioritize more recent sources over older ones. Prioritize document types in this order: Business Report → Sustainability Report → Materiality Analysis → Policy → Strategy → Other. Take "Remarks" of the user into account, if provided.
      11. For any data extracted always mention the year to which they belong, as often older data are treated differently.

      We are currently in the year ${currentYear} and ${reportingYear ? ' regardless of what the reporing rules below specify or what the reference document mention, we are strictly reporting for the year ' + reportingYear : ' figure out the reporting year from the reporting rules specified below or from the other references'}. Consider this when doing your generation.

      Remember to adhere strictly to the provided information, requirements and json format. Use structuring elements like h3 headings to make it easier to read, but avoid double empty lines/break. Use only information that is explicitly mentioned in the context. Report on everything needed for ${datapointsString} and do not report anything that is not explicitly required for this. You are crafting a report for the company, not evaluating the data provided. Cite every single piece of information in the place where you use it (so in the middle of sentences, not at the end of sentences or even paragraphs) and strictly adhere to this format <source>["chunk-6"]</source>, you have to ensure the chunk number matches exactly to the value provided in context as "Document Chunk ID".

      **Output Format**:
      - Return a json with the key 'datapoint' and the text of it including in-text citations as values and optionally a key "key_gaps" if there is information missing that is not provided in the context or other considerations to express.
      - Format the text using HTML tags (h1, h2, h3, p). Beware to not wrap the tables into <p>-tags like <p><table></table><p> as this breaks the parser.
      - Add a break before each h1, h2, h3 tag, except at the beginning. Do not leave empty lines though.
      - Format numbers that carry a citation in those tags: <sources-options>{"active":["chunk-1":"500 TEUR"], "inactive":["chunk-9": "300 TEUR", "chunk-12":"5 Million"]}</sources-options>. This string is getting rendered as clickable link with the text "500 TEUR".
      - cite sources otherwise as <source>["chunk-6", "chunk-2"]</source>
      ----------
      Learn from this one-shot example json with masked facts what the output can look like, in this case from a numeric DP rather than table though:
      {
      "datapoint": "<h2>X1-1_XX – data point name</h2><p>The total greenhouse gas emissions for the reporting year 2024 are <sources-options>{"active":["chunk-6":"700t"], "inactive":["chunk-10": "700t", "chunk-21":"20t"]}</sources-options> as per the latest environmental report and  contributes to <sources-options>{"active":["chunk-14":"3%"], "inactive":["chunk-9": "3%"]}</sources-options> of global index.</p>",
      "key_gaps": "The law text also asks for the emissions of the previous year, but this is not provided in the context."
      }
      //note: ONLY make "in the context" reference in key_gaps
      //note2 that for both active keys we might have had 2 different chunks stating the same value and one claiming a different value. We picked the one that was from the more reliable one and indicated it is from 2024 as the first one and used the value. Then the other supporting source follow and after them last in the array are the sources with contradicting values as alternatives. Make sure to use double quotes "" for citing (do not use single quotes).

          <bad_example>
      {"datapoint": "E1-1_12 – Angabe zum Ausschluss des Unternehmens aus den EU Paris-aligned Benchmarks
      Gemäß ESRS E1-1 (16)(g) hat das Unternehmen offenzulegen, ob es aufgrund der in der Verordnung (EU) 2020/1818 (Climate Benchmark Standards Regulation) genannten Ausschlusskriterien (Art. 12.1 (d) bis (g) und Art. 12.2) von den EU Paris-aligned Benchmarks ausgenommen ist. Zu diesen Kriterien zählen insbesondere der wirtschaftliche Anteil aus Aktivitäten mit Kohle, Öl oder Gas sowie die Emissionsintensität bei der Stromerzeugung . Ein positiver oder negativer Ausschluss des Unternehmens kann nur anhand umfassender Umsatzauswertungen und Emissionsdaten festgestellt werden, die im vorliegenden Kontext nicht angegeben sind. <sources-options>{"active":["chunk-1":"500 TEUR"]}</sources-options>
      Die hierfür erforderlichen Informationen zum Status des Unternehmens in Bezug auf die EU Paris-aligned Benchmarks wurden im bereitgestellten Kontext nicht offengelegt."}
      This is an extremely bad example, because it analyses the legal requirements and concludes that data are missing. We want a draft for the report text, or if all data are missing, nothing but the literal sentence "The required information is not provided in the context." (in the generation language). Even though we use the latter one only as very last resort, if after checking again and again, there is really no information provided, we can use it. Finally it talks about "das Unternehmen" even though no company refers to themselves in their own reports as "the company". Typically companies use phrasings like "Company_Name ist vom Paris Agreement ausgeschlossen...". In this case however, the correct response would have been:
      {"datapoint": "<h2>E1-1_12 – Angabe zum Ausschluss des Unternehmens aus den EU Paris-aligned Benchmarks</h2>
      <p>Die notwendigen Informationen sind nicht im Kontext enthalten.</p>.",     "key_gaps":"Important information is missing in the context. Specifically XYZ"}
      </bad_example> 

      **Legal Text**:
      ${lawtext}
      ----------

      Note: You are only generating the text for a set of datapoionts. It belongs to a disclosure requirement and there are other datapoints that belong to the same disclosure requirement. Do not include information that belongs to other datapoints in this text. As a reference, here is some related datapoints that have their own texts and thus its contents should NOT occur in the output text here:
      ${otherDatapointsNames}

      **Context**:
      The following context contains information potentially relevant to the datapoint from the company you are reporting for. Use this to inform your generation of the data point:
      <retrieved_context>
      ${linkedChunks}
      </retrieved_context>
      ----------

      **Final Remark**:
      Before generating the final output: Find relevant facts & keep track of their IDs (not chunkNumbers!). Language (for both reasoning and output): ${LANGUAGE_MAP[generationLanguage]}`;
  }

  // open: switch examples. Format input. Format output and make them usd
}
