import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DatapointRequestData } from 'src/data-request/entities/data-request.dto';
import {
  DatapointQueueStatus,
  DatapointRequest,
  DatapointRequestStatus,
} from 'src/datapoint/entities/datapoint-request.entity';
import { JobProcessor, JobQueue } from 'src/types/jobs';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';
import { DataRequest } from 'src/data-request/entities/data-request.entity';
import { DEFAULT_JOB_CONFIG } from 'src/util/queue.config';
import { WorkerLogger } from './logger.service';

@Injectable()
export class DatapointDataRequestSharedService {
  constructor(
    @InjectRepository(DatapointRequest)
    private readonly datapointRequestRepository: Repository<DatapointRequest>,
    @InjectRepository(DataRequest)
    private readonly dataRequestRepository: Repository<DataRequest>,
    @InjectQueue(JobProcessor.DatapointGeneration)
    private readonly datapointGenerationQueue: Queue,
    @InjectQueue(JobProcessor.DatapointReview)
    private readonly datapointReviewQueue: Queue
  ) {}

  private readonly logger = new WorkerLogger(DatapointDataRequestSharedService.name);

  async addDatapointToGenerationQueue({
    datapointRequest,
    userId,
    workspaceId,
    useExistingReportTextForReference,
  }: {
    datapointRequest: DatapointRequestData;
    userId: string;
    workspaceId: string;
    useExistingReportTextForReference: boolean;
  }): Promise<void> {
    try {
      await this.datapointRequestRepository.update(
        { id: datapointRequest.id },
        { queueStatus: DatapointQueueStatus.QueuedForGeneration }
      );

      await this.datapointGenerationQueue.add(
        JobQueue.DatapointGenerate,
        {
          dataRequestId: datapointRequest.dataRequestId,
          datapointRequestId: datapointRequest.id,
          userId,
          workspaceId,
          useExistingReportTextForReference,
        },
        DEFAULT_JOB_CONFIG
      );
    } catch (error) {
      this.logger.error(
        `Error generating datapoint ${datapointRequest.id} for data request ${datapointRequest.dataRequestId}: ${error}`
      );
      await this.datapointRequestRepository.update(
        { id: datapointRequest.id },
        {
          queueStatus: null,
          status:
            datapointRequest.status || DatapointRequestStatus.IncompleteData,
        }
      );
    }
  }

  async addDatapointToReviewQueue({
    datapointRequest,
    userId,
    workspaceId,
  }: {
    datapointRequest: DatapointRequestData;
    userId: string;
    workspaceId: string;
  }): Promise<void> {
    try {
      await this.datapointRequestRepository.update(
        { id: datapointRequest.id },
        { queueStatus: DatapointQueueStatus.QueuedForReview }
      );

      await this.datapointReviewQueue.add(
        JobQueue.DatapointReview,
        {
          dataRequestId: datapointRequest.dataRequestId,
          datapointRequestId: datapointRequest.id,
          userId,
          workspaceId,
        },
        DEFAULT_JOB_CONFIG
      );
    } catch (error) {
      this.logger.error(
        `Error reviewing datapoint ${datapointRequest.id} for data request ${datapointRequest.dataRequestId}: ${error}`
      );
      await this.datapointRequestRepository.update(
        { id: datapointRequest.id },
        {
          queueStatus: null,
          status:
            datapointRequest.status || DatapointRequestStatus.IncompleteData,
        }
      );
    }
  }

  async restoreVersion({ versionId, requestId, content, event }) {
    let response: DataRequest | DatapointRequest;
    if (event === 'datapoint_request_updated') {
      response = await this.datapointRequestRepository.save({
        id: requestId,
        content,
        content_version: versionId,
      });
    } else if (event === 'data_request_updated') {
      response = await this.dataRequestRepository.save({
        id: requestId,
        content,
        content_version: versionId,
      });
    }
    return response;
  }
}
