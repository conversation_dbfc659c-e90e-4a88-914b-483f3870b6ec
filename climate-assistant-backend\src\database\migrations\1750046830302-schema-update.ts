import { MigrationInterface, QueryRunner } from 'typeorm';
import { SystemPermissions } from '../../constants';

// Migration for adding permissions and roles to the database
const RolePermissions = {
  SUPER_ADMIN: Object.values(SystemPermissions),
  WORKSPACE_ADMIN: [
    SystemPermissions.CHANGE_MATERIALITY_SETTING,
    SystemPermissions.ACCESS_MATERIALITY_SETTING,
    SystemPermissions.CREATE_DATAPOINTS,
    SystemPermissions.EDIT_DATAPOINTS,
    SystemPermissions.APPROVE_DATAPOINTS,
    SystemPermissions.MARK_DATAPOINTS,
    SystemPermissions.EXPORT_DATAPOINTS_EXCEL,
    SystemPermissions.GAP_ANALYSIS_DATAPOINTS,
    SystemPermissions.CREATE_DRS,
    SystemPermissions.EDIT_DRS,
    SystemPermissions.GAP_ANALYSIS_DRS,
    SystemPermissions.APPROVE_DRS,
    SystemPermissions.MARK_DRS,
    SystemPermissions.ACCESS_VERSION_HISTORY,
    SystemPermissions.RESTORE_VERSION_HISTORY,
    SystemPermissions.CREATE_COMMENTS,
    SystemPermissions.EDIT_COMMENTS,
    SystemPermissions.EDIT_WORKSPACE_SETTINGS,
    SystemPermissions.EDIT_PROJECT_SETTINGS,
    SystemPermissions.INVITE_USERS,
    SystemPermissions.CHANGE_USER_ROLES,
    SystemPermissions.ASSIGN_USERS_DRS,
    SystemPermissions.ACCESS_LEGAL_TEXT,
    SystemPermissions.EXPORT_FINAL_REPORT,
    SystemPermissions.EXPORT_DR_EXCEL,
    SystemPermissions.EXPORT_DATAPOINTS_EXCEL,
  ],
  AI_CONTRIBUTOR: [
    SystemPermissions.ACCESS_MATERIALITY_SETTING,
    SystemPermissions.CREATE_DATAPOINTS,
    SystemPermissions.EDIT_DATAPOINTS,
    SystemPermissions.APPROVE_DATAPOINTS,
    SystemPermissions.MARK_DATAPOINTS,
    SystemPermissions.EXPORT_DATAPOINTS_EXCEL,
    SystemPermissions.GAP_ANALYSIS_DATAPOINTS,
    SystemPermissions.CREATE_DRS,
    SystemPermissions.EDIT_DRS,
    SystemPermissions.GAP_ANALYSIS_DRS,
    SystemPermissions.APPROVE_DRS,
    SystemPermissions.MARK_DRS,
    SystemPermissions.ACCESS_VERSION_HISTORY,
    SystemPermissions.RESTORE_VERSION_HISTORY,
    SystemPermissions.CREATE_COMMENTS,
    SystemPermissions.EDIT_COMMENTS,
    SystemPermissions.ACCESS_LEGAL_TEXT,
    SystemPermissions.EXPORT_FINAL_REPORT,
    SystemPermissions.EXPORT_DR_EXCEL,
    SystemPermissions.EXPORT_DATAPOINTS_EXCEL,
  ],
  CONTRIBUTOR: [
    SystemPermissions.ACCESS_MATERIALITY_SETTING,
    SystemPermissions.CREATE_COMMENTS,
    SystemPermissions.ACCESS_LEGAL_TEXT,
  ],
};

export class SchemaUpdate1750046830302 implements MigrationInterface {
  name = 'SchemaUpdate1750046830302';

  getPermissionDescription(permission: SystemPermissions): string {
    const descriptions: Record<string, string> = {
      // Datapoints
      [SystemPermissions.CREATE_DATAPOINTS]: 'Create new datapoints',
      [SystemPermissions.EDIT_DATAPOINTS]: 'Edit existing datapoints',
      [SystemPermissions.APPROVE_DATAPOINTS]: 'Approve datapoints',
      [SystemPermissions.MARK_DATAPOINTS]: 'Change status of datapoints',
      [SystemPermissions.EXPORT_DATAPOINTS_EXCEL]: 'Export datapoints to Excel',

      // Disclosure Requirements
      [SystemPermissions.CREATE_DRS]: 'Create new DRs',
      [SystemPermissions.EDIT_DRS]: 'Edit existing DRs',
      [SystemPermissions.APPROVE_DRS]: 'Approve DRs',
      [SystemPermissions.MARK_DRS]: 'Change status of DRs',
      [SystemPermissions.ASSIGN_USERS_DRS]: 'Assign users to DRs',
      [SystemPermissions.EXPORT_DR_EXCEL]: 'Export DRs to Excel',

      // GAP Analysis
      [SystemPermissions.GAP_ANALYSIS_DATAPOINTS]:
        'Perform gap analysis on datapoints',
      [SystemPermissions.GAP_ANALYSIS_DATAPOINTS_REVIEW]:
        'Review datapoint gap analysis',
      [SystemPermissions.GAP_ANALYSIS_DRS]: 'Perform gap analysis on DRs',
      [SystemPermissions.GAP_ANALYSIS_DRS_REVIEW]: 'Review DR gap analysis',

      // Comments
      [SystemPermissions.CREATE_COMMENTS]: 'Create comments',
      [SystemPermissions.EDIT_COMMENTS]: 'Edit comments',

      // Administrative & Workspace
      [SystemPermissions.CREATE_WORKSPACE]: 'Create new workspaces',
      [SystemPermissions.SWITCH_WORKSPACE]: 'Switch between workspaces',
      [SystemPermissions.EDIT_WORKSPACE_SETTINGS]: 'Edit workspace settings',
      [SystemPermissions.EDIT_PROJECT_SETTINGS]: 'Edit project settings',
      [SystemPermissions.CHANGE_USER_ROLES]: 'Modify user roles',
      [SystemPermissions.INVITE_USERS]: 'Invite new users',

      // General System Access
      [SystemPermissions.ACCESS_LEGAL_TEXT]: 'Access legal text',
      [SystemPermissions.EXPORT_FINAL_REPORT]: 'Export final reports',

      // Materiality Setting
      [SystemPermissions.CHANGE_MATERIALITY_SETTING]:
        'Modify materiality settings',
      [SystemPermissions.ACCESS_MATERIALITY_SETTING]:
        'View materiality settings',

      // Version History
      [SystemPermissions.ACCESS_VERSION_HISTORY]: 'View version history',
      [SystemPermissions.RESTORE_VERSION_HISTORY]:
        'Restore version history entries',
    };

    return descriptions[permission] || `Permission ${permission}`;
  }

  public async up(queryRunner: QueryRunner): Promise<void> {
    const enumValues = Object.values(SystemPermissions)
      .map((value) => `'${value}'`)
      .join(', ');
    await queryRunner.query(
      `CREATE TYPE "public"."permission_name_enum" AS ENUM (${enumValues})`
    );
    await queryRunner.query(
      `CREATE TABLE "permission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" "public"."permission_name_enum" NOT NULL, "description" character varying, CONSTRAINT "UQ_240853a0c3353c25fb12434ad33" UNIQUE ("name"), CONSTRAINT "PK_3b8b97af9d9d8807e41e6f48362" PRIMARY KEY ("id"))`
    );
    for (const permission of Object.values(SystemPermissions)) {
      await queryRunner.query(
        `
        INSERT INTO "permission" (name, description)
        VALUES ($1, $2)
        ON CONFLICT (name) DO NOTHING
      `,
        [permission, this.getPermissionDescription(permission)]
      );
    }
    await queryRunner.query(
      `CREATE TABLE "role_permission" ("roleId" uuid NOT NULL, "permissionId" uuid NOT NULL, CONSTRAINT "PK_b42bbacb8402c353df822432544" PRIMARY KEY ("roleId", "permissionId"))`
    );
    await queryRunner.query(
      `ALTER TABLE "role_permission" ADD CONSTRAINT "FK_e3130a39c1e4a740d044e685730" FOREIGN KEY ("roleId") REFERENCES "role"("id") ON DELETE CASCADE ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "role_permission" ADD CONSTRAINT "FK_72e80be86cab0e93e67ed1a7a9a" FOREIGN KEY ("permissionId") REFERENCES "permission"("id") ON DELETE CASCADE ON UPDATE NO ACTION`
    );
    for (const [roleName, permissions] of Object.entries(RolePermissions)) {
      await queryRunner.query(
        `
        WITH role_data AS (
          SELECT id FROM "role" WHERE name = $1
        ),
        permission_data AS (
          SELECT id FROM "permission" WHERE name = ANY($2)
        )
        INSERT INTO "role_permission" ("roleId", "permissionId")
        SELECT rd.id, pd.id
        FROM role_data rd
        CROSS JOIN permission_data pd
      `,
        [roleName, permissions]
      );
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "role_permission" DROP CONSTRAINT "FK_72e80be86cab0e93e67ed1a7a9a"`
    );
    await queryRunner.query(
      `ALTER TABLE "role_permission" DROP CONSTRAINT "FK_e3130a39c1e4a740d044e685730"`
    );
    await queryRunner.query(`DROP TABLE "role_permission"`);
    await queryRunner.query(`DROP TABLE "permission"`);
    await queryRunner.query(`DROP TYPE "public"."permission_name_enum"`);
  }
}
