import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1749104768325 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TYPE "role_name_enum" AS ENUM (
        'SUPER_ADMIN',
        'WORKSPACE_ADMIN',
        'AI_CONTRIBUTOR',
        'AI_ONLY_REVIEW',
        'CONTRIBUTOR'
      )
    `);
    // Create role table
    await queryRunner.query(`
      CREATE TABLE "role" (
        "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
        "name" role_name_enum NOT NULL UNIQUE,
        "description" varchar
      )
    `);

    // Insert default roles
    await queryRunner.query(`
      INSERT INTO "role" (name, description) VALUES
      ('SUPER_ADMIN', 'Super administrator with full access'),
      ('WORKSPACE_ADMIN', 'Workspace administrator'),
      ('AI_CONTRIBUTOR', 'AI content contributor'),
      ('AI_ONLY_REVIEW', 'AI content reviewer'),
      ('CONTRIBUT<PERSON>', 'Regular contributor')
    `);

    // Add roleId column to user_workspace
    await queryRunner.query(`
      ALTER TABLE "user_workspace"
      ADD COLUMN "roleId" uuid
    `);

    // Update roleId based on existing role enum values
    await queryRunner.query(`
      UPDATE "user_workspace" uw
      SET "roleId" = r.id
      FROM "role" r
      WHERE r.name::text = uw.role::text
    `);

    // Add foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "user_workspace"
      ADD CONSTRAINT "FK_user_workspace_role"
      FOREIGN KEY ("roleId")
      REFERENCES "role"(id)
    `);

    // Drop the old role enum column
    await queryRunner.query(`
      ALTER TABLE "user_workspace"
      DROP COLUMN "role"
    `);

    // Drop the enum type
    await queryRunner.query(`
      DROP TYPE IF EXISTS "public"."user_workspace_role_enum"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Recreate the enum type
    await queryRunner.query(`
      CREATE TYPE "user_workspace_role_enum" AS ENUM (
        'SUPER_ADMIN',
        'WORKSPACE_ADMIN',
        'AI_CONTRIBUTOR',
        'AI_ONLY_REVIEW',
        'CONTRIBUTOR'
      )
    `);

    // Add back the role column
    await queryRunner.query(`
      ALTER TABLE "user_workspace"
      ADD COLUMN "role" "user_workspace_role_enum"
    `);

    // Migrate data back
    await queryRunner.query(`
      UPDATE "user_workspace" uw
      SET role = r.name::"user_workspace_role_enum"
      FROM "role" r
      WHERE r.id = uw."roleId"
    `);

    // Drop the roleId foreign key and column
    await queryRunner.query(`
      ALTER TABLE "user_workspace"
      DROP CONSTRAINT "FK_user_workspace_role",
      DROP COLUMN "roleId"
    `);

    // Drop the role table
    await queryRunner.query(`
      DROP TABLE "role"
    `);

    await queryRunner.query(`
      DROP TYPE "role_name_enum"
    `);
  }
}
