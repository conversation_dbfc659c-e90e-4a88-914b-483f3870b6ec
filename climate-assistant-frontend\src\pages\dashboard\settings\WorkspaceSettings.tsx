import { useEffect, useState } from 'react';
import { Plus, LoaderCircle, SaveIcon } from 'lucide-react';

import { Label } from '@/components/ui/label';
import { WORKSPACE_ROLES_MAPPING_FOR_UI } from '@/constants/workspaceConstants';
import { useAuthentication } from '@/api/authentication/authentication.query';
import { Button, buttonVariants } from '@/components/ui/button';
import { USER_ROLE } from '@/constants/workspaceConstants';
import { useToast } from '@/components/ui/use-toast';
import { IUser, IUserWithStatus, USER_STATUS_VARIANTS } from '@/types/user';
import AddUserDialogue from '@/components/AddUserDialogue';
import { cn } from '@/lib/utils';
import { Textarea } from '@/components/ui/textarea';
import { useWorkspaceSettings } from '@/api/workspace-settings/useWorkspaceSettings';
import { usePermissions } from '@/context/permissionsContext';

const USER_STATUS_STYLE_MAPPING = {
  INVITED: {
    label: 'Invited',
    bgColor: 'rgba(214, 158, 46, 0.1)',
    color: 'rgb(214, 158, 46)',
  },
  ACTIVE: {
    label: 'Active',
    bgColor: 'rgba(56, 161, 105, 0.1)',
    color: 'rgb(56, 161, 105)',
  },
  INACTIVE: {
    label: 'Not Active',
    bgColor: 'rgba(209, 213, 219, 1)',
    color: 'rgb(75, 85, 99)',
  },
};

const COMPANY_PROFILE_PLACEHOLDER = `Company Name & Legal Form
Full legal name and any relevant trade names
Legal form (e.g., public limited company, private limited company)

Headquarters & Geographic Footprint
Location of the main office / HQ
Parent-Organization if applicable
Major subsidiaries or business units
Geographic scope of operations (countries, key markets)

Founding Year & Brief History
Year established and major milestones
Any significant acquisitions or mergers

Ownership & Shareholding Structure
Whether the company is publicly listed or privately held
Key shareholders or investor groups, if relevant

Size & Scale
Financial Data (revenue, profit, capital expenditure)
Workforce Data (gender diversity, employee turnover, training hours)
Environmental Data (energy consumption, CO₂ emissions, water usage, waste management)

Industry & Sector
Main industry sector(s) in which the company operates
Primary products, services, or solutions

Applicable Regulations
Any local, national, or regional legislation affecting sustainability reporting
Specific compliance requirements (e.g., CSRD if in the EU, or other frameworks)
Whether the company aligns with the EU Taxonomy for sustainable activities`;

const WorkspaceSettings = () => {
  const [isAddUserDialogueOpen, setIsAddUserDialogueOpen] = useState(false);
  const [companyProfileText, setCompanyProfileText] = useState('');
  const {
    workspaceUsers,
    isWorkspaceUsersLoading,
    inviteUsers,
    isInviteUsersInProgress,
    updateCompany,
    isUpdateCompanyInProgress,
    workspaceDetail,
  } = useWorkspaceSettings();
  const { user: loggedInUser } = useAuthentication();
  const { toast } = useToast();
  const { userPermissions } = usePermissions();

  useEffect(() => {
    if (workspaceDetail?.companies?.[0]?.generalCompanyProfile) {
      setCompanyProfileText(workspaceDetail.companies[0].generalCompanyProfile);
    }
  }, [workspaceDetail]);

  function getUserName(user: IUser) {
    let userName = user.name || user.email;
    if (loggedInUser?.email === user.email) {
      userName += ' (You)';
    }
    return userName;
  }

  const handleInviteUser = async (emails: string[], role: string) => {
    try {
      const { success, failure } = await inviteUsers({ emails, role });
      setIsAddUserDialogueOpen(false);
      if (failure) {
        toast({
          variant: 'destructive',
          description: failure,
        });
      }
      if (success) {
        toast({
          variant: 'success',
          description: success,
        });
      }
    } catch (err) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to invite user',
      });
      console.log(err);
    }
  };

  const handleSaveCompanyProfile = async () => {
    try {
      const workspaceId = loggedInUser?.userWorkspaces[0].workspaceId;
      if (!workspaceId) {
        throw new Error('Workspace ID is not available.');
      }
      await updateCompany({
        companyPayload: { generalCompanyProfile: companyProfileText },
      });
      toast({
        variant: 'success',
        description: 'Company profile saved successfully.',
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'An unknown error occurred.',
      });
      console.error('Failed to save company profile:', error);
    }
  };

  return (
    <div className="max-w-3xl">
      {/* User Management Section */}
      <div className="flex justify-between">
        <div className="flex flex-col gap-4">
          <h3 className="text-2xl font-bold">User Management</h3>
          <p>Manage your team members and their account permissions here.</p>
        </div>
        {userPermissions.canInviteUser && (
          <Button
            variant="outline"
            className="ml-auto"
            onClick={() => setIsAddUserDialogueOpen(!isAddUserDialogueOpen)}
          >
            <Plus className="w-4 h-4 mr-2" /> Add Users
          </Button>
        )}
      </div>
      {isWorkspaceUsersLoading ? (
        <p>Fetching users data</p>
      ) : (
        <table className="w-full mt-8">
          <tbody>
            {workspaceUsers
              ?.filter((user: IUserWithStatus) => {
                if (userPermissions.canInviteUser) {
                  return true;
                }

                return ![USER_ROLE.SuperAdmin].includes(user.role);
              })
              .map((user: IUserWithStatus) => {
                const {
                  bgColor,
                  color,
                  label: statusLabel,
                } = USER_STATUS_STYLE_MAPPING[user.status];
                return (
                  <tr key={user.id} className="border-b-2 border-gray-200">
                    <td className="text-md p-4">
                      <h4 className="font-bold">{getUserName(user)}</h4>
                      {user.email}
                    </td>
                    <td className="text-sm p-4 flex">
                      <Label
                        className="flex items-center rounded-sm py-1 px-3 w-fit whitespace-nowrap font-normal"
                        style={{ backgroundColor: bgColor, color: color }}
                      >
                        {statusLabel}
                      </Label>
                      {user.status === USER_STATUS_VARIANTS.INVITED &&
                        userPermissions.canInviteUser && (
                          <Button
                            variant={'link'}
                            onClick={() =>
                              handleInviteUser([user.email], user.role)
                            }
                          >
                            Resend Invite
                          </Button>
                        )}
                    </td>
                    <td className="text-sm p-4">
                      {WORKSPACE_ROLES_MAPPING_FOR_UI[user.role]}
                    </td>
                  </tr>
                );
              })}
          </tbody>
        </table>
      )}
      {/* General Company Profile Section */}
      <div className="mt-10 grid gap-5">
        {/* Title and Description */}
        <Label
          className="text-2xl font-semibold"
          htmlFor="reportTextGenerationRules"
        >
          General Company Profile Section
        </Label>
        <p className="text-gray-600 mt-2">
          Tell us about your company: structure, sector, core services/products,
          employee count, and operating countries. This info tailors AI features
          to your business.
        </p>

        <Textarea
          id="companyProfile"
          value={companyProfileText}
          onChange={(e) => setCompanyProfileText(e.target.value)}
          placeholder={COMPANY_PROFILE_PLACEHOLDER}
          autoCapitalize="none"
          autoCorrect="off"
          rows={10}
          disabled={
            isUpdateCompanyInProgress || !userPermissions.canUpdateProject
          }
        />
        <div className="flex gap-2">
          <button
            className={cn(
              buttonVariants({ variant: 'outline' }),
              'flex gap-2',
              isUpdateCompanyInProgress ? 'cursor-not-allowed opacity-50' : ''
            )}
            onClick={handleSaveCompanyProfile}
            disabled={
              isUpdateCompanyInProgress || !userPermissions.canUpdateProject
            }
          >
            {isUpdateCompanyInProgress && (
              <LoaderCircle className="h-4 w-4 animate-spin" />
            )}
            <SaveIcon className="h-4 w-4" />
            Save Settings
          </button>
        </div>
      </div>
      {/* Add User Dialogue */}
      <AddUserDialogue
        isDialogueOpen={isAddUserDialogueOpen}
        setIsDialogueOpen={setIsAddUserDialogueOpen}
        handleInviteUser={handleInviteUser}
        isInviteUsersInProgress={isInviteUsersInProgress}
      />
    </div>
  );
};

export default WorkspaceSettings;
