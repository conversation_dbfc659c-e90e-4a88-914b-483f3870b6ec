<br><br>

<p align="center">
  <img src="climate-assistant-frontend/public/logo-md.png" alt="Glacier Climate Assistant Architecture" width="600">
</p>
<br><br>

# Glacier Climate Assistant

Glacier Climate Assistant is a multi-service platform designed to provide climate assistance through a collection of interconnected components. This repository contains the core code and configurations for the following services:

- **Frontend:** A React application serving the climate assistant user interface.
- **Backend:** A NestJS (Node.js) API that powers the climate assistant.
- **NGINX:** A reverse proxy responsible for routing requests to the appropriate services and managing SSL certificates.
- **Backend DB:** A PostgreSQL database for the backend.
- **Redis:** An in-memory data store used for caching and ephemeral data storage to improve application performance.
- **PM2:** A production process manager for Node.js applications that provides clustering, monitoring, and automatic restarts.
- **Grafana:** A monitoring and observability platform that provides dashboards and alerting for system metrics and application performance.

> **Important:** All critical environment variables are defined in the `.env` file. When running the project locally, copy `.env-example` to `.env` and update the credentials as necessary.

---

## Frontend

### Development

1. **Use Node v18.17.0 or higher:**
   ```bash
   nvm use 18.17.0
   ```
2. **Install dependencies:**
   ```bash
   npm install
   ```
3. **Start the development server:**
   ```bash
   npm run dev
   ```

### Production

- The production build is executed automatically in the Dockerfile. To build manually:
  ```bash
  npm run build
  ```

### Architecture & Tools

- **State Management:** tanstack/query
- **Styling:** shadcn/tailwind
- **Linting:** ESLint
- **Formatting:** Prettier

---

## Backend

### Development

1. **Use Node v20.9.0 or higher:**
   ```bash
   nvm use 20.9.0
   ```
2. **Install dependencies:**
   ```bash
   npm install
   ```
3. **Start the server in development mode (watch mode enabled):**
   ```bash
   npm run start:dev
   ```

### Production

- The production build is executed automatically in the Dockerfile. To build manually:
  ```bash
  npm run build
  ```

### Database Migrations

- **Generate a new migration:**
  ```bash
  npm run migration:generate
  ```
- **Run migrations locally:**
  ```bash
  npm run migration:run
  ```
- **Run migrations in production:**  
  _(Usually not needed as they run automatically on startup)_
  ```bash
  npm run migration:run:prod
  ```

### Architecture & Tools

- **Database Management:** TypeORM
- **Authentication:** JWT with HTTP-only cookies

---

## Redis

Redis is included in the Docker Compose setup to handle caching and ephemeral data storage.

### Key Details

- **Image:** `redis:7-alpine`
- **Port:** 6379
- **Healthcheck:** Uses `redis-cli ping`

### Usage in Local Development

- **Access:** Other services can connect to Redis using `redis:6379`.
- **Verification:** To check if Redis is running, enter the container and run:
  ```bash
  docker compose exec redis sh
  redis-cli ping
  ```

### Production

- Redis starts automatically with:
  ```bash
  docker compose up -d
  ```
- For enhanced security, consider configuring network-level access controls or adding password protection as needed.

---

## Additional Documentation

For more detailed information on specific aspects of the project, please refer to the documentation in the [`docs`](docs) directory:

### **Core Infrastructure & Architecture**  
- **Backend Architecture:** [docs/backend-architecture.md](docs/backend-architecture.md)  
- **Process Queue:** [docs/process-queue.md](docs/process-queue.md)  
- **Migrations:** [docs/migrations/updating-dr-dp.md](docs/migrations/updating-dr-dp.md)

- **[Backend Architecture](docs/backend-architecture.md)**
- **[Process Queue](docs/process-queue.md)**
- **[Server-Side Events](docs/server-side-events.md)**
- **[Redis Pub/Sub Implementation](docs/redis-pubsub-implementation.md)**
- **[Migrations](docs/migrations/updating-dr-dp.md)**

### **Data Processing & Services**

- **[Datapoint Generation](docs/services/datapoint-generation-process.md)**
- **[Prompt Management Integration](docs/services/prompt-management-system.md)**

### **Authentication & Security**

- **[Roles & Permissions](docs/roles-permission.md)**

### **Process Management**

- **[PM2 Setup](docs/pm2-setup.md)**
- **[PM2 Complete Implementation](docs/pm2-complete-implementation.md)**

### **Server Monitoring**

- **[Base Configuration](docs/server-monitoring/base-configuration.md)**
- **[PM2 Cluster Monitoring](docs/server-monitoring/pm2-cluster-monitoring.md)**
- **[PM2 Monitoring Quickstart](docs/server-monitoring/pm2-monitoring-quickstart.md)**

### **Server & Maintenance**

- **[NGINX & Certbot Management](docs/nginx-certbot.md)**
- **[Database Backup Procedures](docs/database-backup.md)**
- **[Ubuntu Container Recovery](docs/ubuntu-container-recovery.md)**
- **[CI/CD Processes](docs/github-cicd.md)**

### **General Guides**

- **[Prompt Guide](docs/prompt-guide.md)**
