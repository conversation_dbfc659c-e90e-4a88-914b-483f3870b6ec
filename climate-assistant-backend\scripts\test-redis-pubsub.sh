#!/bin/bash

echo "=========================================="
echo "Testing Redis Pub/Sub for SSE Events"
echo "=========================================="
echo ""

# Check if Redis is running
if ! redis-cli ping > /dev/null 2>&1; then
    echo "❌ Redis is not running. Please start Redis first."
    exit 1
fi

echo "✓ Redis is running"
echo ""

# Monitor Redis pub/sub in background
echo "Starting Redis monitor for 'datapoint-generation-events' channel..."
redis-cli SUBSCRIBE datapoint-generation-events &
MONITOR_PID=$!

# Give monitor time to start
sleep 2

echo ""
echo "Publishing test event..."
redis-cli PUBLISH datapoint-generation-events '{"test": "message", "timestamp": "'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'"}'

echo ""
echo "Waiting for event to be received..."
sleep 2

# Kill the monitor
kill $MONITOR_PID 2>/dev/null

echo ""
echo "=========================================="
echo "Test complete. If you saw the test message above, Redis pub/sub is working!"
echo ""
echo "To test with the actual application:"
echo "1. Start the app with PM2: pm2 start ecosystem.config.js --env production"
echo "2. Open multiple terminals and connect SSE clients:"
echo "   curl -N http://localhost:3000/data-request/events/datapoint/test-id"
echo "3. Trigger an event through the queue processor"
echo "4. All SSE clients should receive the event"
echo "==========================================" 