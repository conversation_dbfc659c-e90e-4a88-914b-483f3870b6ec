# Permission System Documentation

## Overview

The permission system is implemented across both frontend and backend to ensure consistent access control. Permissions are managed through guards and context providers, with a centralized list of system permissions.

Note: You can view the role permissions in the metabase [here](https://reports.glacier.eco/dashboard/21-role-permissions)

## Permission Sources 

The authoritative source for permissions is maintained in the Glacier SharePoint document. Any changes to permissions should first be documented there.

## Frontend Implementation

Permission Context. 

Permissions are managed through a React context provider:

```javascript
// src/context/permissionsContext.tsx
const PermissionsContext = createContext<PermissionsContextProps>({
  permissions: [],
  setPermissions: () => {},
  hasPermission: () => false,
  userPermissions: {
    canViewVersionHistory: false,
    canPerformAiGenerateOrReviewOnDr: false,
    // ... other permission flags
  }
});
```

### Using permissions

1. Wrap your app with the provider:

```javascript
function App() {
  return (
    <PermissionsProvider>
      <YourApp />
    </PermissionsProvider>
  );
}
```

2. Check permissions in components:

```javascript
function ProtectedComponent() {
  const { userPermissions } = usePermissions();
  
  if (!userPermissions.canEditDr) {
    return null;
  }
  
  return <div>Protected Content</div>;
}
```

### Adding new permissions 

1. Add the permission enum in constants:

```javascript
// src/constants/userRoleConstants.tsx
export enum SystemPermissions {
  NEW_PERMISSION = 'NEW_PERMISSION',
  // ... existing permissions
}
```

2. Update the permisison mapping:

```javascript
const PERMISSION_MAPPING: PermissionMapping = {
  newPermissionFlag: SystemPermissions.NEW_PERMISSION,
  // ... existing mappings
};
```

## Backend Implementation

### Permission Guard

Permissions are enforced using guards at the controller level:

```javascript
@Controller('documents')
@UseGuards(AuthGuard)
@UseGuards(PermissionGuard)
export class DocumentController {
  @Post()
  @Permissions(SystemPermissions.CREATE_DATAPOINTS)
  async createDocument() {
    // Protected endpoint
  }
}
```

### Permission Decorator

Define required permissions using `@Permissions` decorator:

```javascript
@Permissions(SystemPermissions.EDIT_DRS, SystemPermissions.APPROVE_DRS)
async updateDocument() {
  // Only accessible with both permissions
}
```

### Permission Service

The backend validates permissions through the `PermissionService`:

```javascript
@Injectable()
export class PermissionService {
  async getUserPermissions(userId: string, workspaceId: string) {
    const userPermissions = await this.rolePermissionRepository
      .createQueryBuilder('rolePermission')
      .innerJoin('user_workspace', 'userWorkspace')
      .where('userWorkspace.userId = :userId', { userId })
      .andWhere('userWorkspace.workspaceId = :workspaceId', { workspaceId })
      .getMany();

    return userPermissions.map((rp) => rp.permission.name);
  }
}
```

## Best Practices

1. Always reference the SharePoint document before adding new permissions
2. Update both frontend and backend when adding permissions
3. Use TypeScript enums to maintain consistency
4. Cache permission checks where appropriate

## Permission Flow

1. User logs in
2. Backend returns user permissions
3. Frontend stores permissions on API calls
4. Guards check permissions on API calls
5. Components conditionally render based on permissions.


## Adding New Permissions Checklist
- [ ] Document in SharePoint
- [ ] Add to SystemPermissions enum
- [ ] Update PermissionMapping in frontend
- [ ] Add permission to relevant roles in backend