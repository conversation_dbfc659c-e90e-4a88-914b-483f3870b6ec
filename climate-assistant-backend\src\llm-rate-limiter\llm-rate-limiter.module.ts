import { Module } from '@nestjs/common';
import { LlmRateLimiterService } from './llm-rate-limiter.service';
import { BullModule } from '@nestjs/bull';
import { JobProcessor } from 'src/types/jobs';
import { LlmModule } from 'src/llm/llm.module';
import { DEFAULT_QUEUE_JOB_OPTIONS } from 'src/util/queue.config';

@Module({
  imports: [
    LlmModule,
    BullModule.registerQueue({ 
      name: JobProcessor.LlmRequest,
      defaultJobOptions: DEFAULT_QUEUE_JOB_OPTIONS,
    }),
  ],
  providers: [LlmRateLimiterService],
  exports: [LlmRateLimiterService],
  controllers: [],
})
export class LlmRateLimiterModule {}
