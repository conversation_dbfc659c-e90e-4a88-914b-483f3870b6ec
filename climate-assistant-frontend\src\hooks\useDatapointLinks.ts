import { useEffect, useState } from 'react';

import { fetchProjectEsrsDatapoints } from '@/api/project-settings/project-settings.api';
import { updateDatapointLinkToDocumentChunk } from '@/api/documents/documents.api';
import { toast } from '@/components/ui/use-toast';
import { LinkedESRSDatapoint } from '@/components/documents/DatapointLinksTable';
import { DatapointRequestLinkage } from '@/types/document';

export function useDatapointLinks({
  documentChunkId,
  datapointRequests,
  mutate,
}: {
  documentChunkId: string;
  datapointRequests: DatapointRequestLinkage[];
  mutate: () => void;
}) {
  const [esrs, setEsrs] = useState('all');
  const [filter, setFilter] = useState('all');
  const [loading, setLoading] = useState(false);
  const [esrsDatapoints, setEsrsDatapoints] = useState<LinkedESRSDatapoint[]>(
    []
  );
  const [esrsDatapointsFiltered, setEsrsDatapointsFiltered] = useState<
    LinkedESRSDatapoint[]
  >([]);
  const [pendingUpdates, setPendingUpdates] = useState<Map<string, boolean>>(
    new Map()
  );

  // Handle individual link/unlink actions (stored in state only)
  function handleUpdateSet(id: LinkedESRSDatapoint['id'], linked: boolean) {
    const datapointRequestId = esrsDatapoints.find(
      (datapoint) => datapoint.id === id
    )?.datapointRequestId;

    if (!datapointRequestId) return;

    // Store the update in pendingUpdates
    setPendingUpdates(new Map(pendingUpdates.set(datapointRequestId, linked)));

    // Update UI immediately
    setEsrsDatapoints((prev) =>
      prev.map((datapoint) => {
        if (datapoint.id === id) {
          return { ...datapoint, linked };
        }
        return datapoint;
      })
    );
  }

  // Handle saving all pending updates to the database
  async function handleSubmitUpdates() {
    if (pendingUpdates.size === 0) return;

    setLoading(true);
    try {
      const updates = Array.from(pendingUpdates.entries()).map(
        ([datapointRequestId, linked]) => ({
          datapointRequestId,
          linked,
        })
      );

      await updateDatapointLinkToDocumentChunk(documentChunkId, updates);

      toast({
        title: 'Datapoint links updated successfully',
      });

      // Clear pending updates after successful save
      setPendingUpdates(new Map());
      mutate();
    } catch (error) {
      toast({
        title: 'An error occurred',
        variant: 'destructive',
      });
      // Reload the original state on error
      loadUserFileUploads();
    }
    setLoading(false);
  }

  async function loadUserFileUploads() {
    setLoading(true);
    setPendingUpdates(new Map());
    try {
      const fetchDatapoints = await fetchProjectEsrsDatapoints(esrs);
      const linkedDatapoints = fetchDatapoints.map((datapoint) => ({
        ...datapoint,
        linked: datapointRequests.some(
          (request) =>
            request.esrsDatapointId === datapoint.id && request.linked
        ),
      }));

      setEsrsDatapoints(linkedDatapoints);
    } catch (error) {
      toast({
        title: 'Error loading datapoints',
        variant: 'destructive',
      });
    }
    setLoading(false);
  }

  useEffect(() => {
    if (filter === 'all') {
      setEsrsDatapointsFiltered(esrsDatapoints);
    } else if (filter === 'Linked') {
      setEsrsDatapointsFiltered(
        esrsDatapoints.filter((datapoint) => datapoint.linked)
      );
    } else {
      setEsrsDatapointsFiltered(
        esrsDatapoints.filter((datapoint) => !datapoint.linked)
      );
    }
  }, [esrsDatapoints, filter]);

  useEffect(() => {
    loadUserFileUploads();
  }, [esrs]);

  return {
    esrs,
    setEsrs,
    filter,
    setFilter,
    loading,
    esrsDatapoints,
    esrsDatapointsFiltered,
    handleUpdateSet,
    handleSubmitUpdates,
    hasPendingChanges: pendingUpdates.size > 0,
  };
}
