import { Injectable } from '@nestjs/common';
import { MarkdownTextSplitter } from '@langchain/textsplitters';
import type { DocumentChunkGenerated } from 'src/types';
import { DocumentParsingUtils } from './utils';
import { HtmlProcessingService } from './html-processing.service';
import { WorkerLogger } from 'src/shared/logger.service';

@Injectable()
export class ChunkProcessingService {
  private readonly logger = new WorkerLogger(ChunkProcessingService.name);

  constructor(private readonly htmlProcessingService: HtmlProcessingService) {}

  /**
   * Process a current chunk and add it to the chunk data array
   */
  async processCurrentChunk({
    currentChunk,
    currentChunkTokens,
    currentChunkPageNumbers,
    chunkDataArray,
    chunkHeadingHierarchy,
    sourceDocumentName,
    mainSections,
    chunkNumber,
  }: {
    currentChunk: string;
    currentChunkTokens: number;
    currentChunkPageNumbers: Set<number>;
    chunkDataArray: DocumentChunkGenerated[];
    chunkHeadingHierarchy: string[];
    sourceDocumentName: string;
    mainSections: string[];
    chunkNumber: number;
  }): Promise<number> {
    this.logger.log(
      `Processing chunk #${chunkNumber}, ${currentChunkTokens} tokens, pages: ${Array.from(currentChunkPageNumbers).join(',')}`
    );

    // Generate page number metadata
    const pageNumberMetadata = DocumentParsingUtils.generatePageNumberMetadata(
      currentChunkPageNumbers
    );

    if (currentChunkTokens > 3000) {
      const htmlTableInfo =
        this.htmlProcessingService.analyzeHtmlTables(currentChunk);
      this.logger.log(
        `Large chunk contains HTML tables: ${htmlTableInfo.hasTables}, table percentage: ${htmlTableInfo.tablePercentage.toFixed(2)}`
      );

      // If the chunk contains HTML tables, we need to be careful about splitting
      if (htmlTableInfo.hasTables && htmlTableInfo.tablePercentage > 0) {
        chunkDataArray.push({
          text: currentChunk,
          metadata: {
            headings: [...chunkHeadingHierarchy],
            sourceDocumentName: sourceDocumentName,
            mainSections: [...mainSections],
            pageNumber: pageNumberMetadata,
            chunkNumber: chunkNumber,
          },
        });
        chunkNumber++;
      } else {
        // Split the chunk if it's too large
        const markdownTextSplitter = new MarkdownTextSplitter({
          chunkSize: 3000,
          chunkOverlap: 0,
          keepSeparator: true,
        });
        const splitChunks = await markdownTextSplitter.splitText(currentChunk);

        for (const splitChunk of splitChunks) {
          chunkDataArray.push({
            text: splitChunk,
            metadata: {
              headings: [...chunkHeadingHierarchy],
              sourceDocumentName: sourceDocumentName,
              mainSections: [...mainSections],
              pageNumber: pageNumberMetadata,
              chunkNumber: chunkNumber,
            },
          });
          chunkNumber++;
        }
      }
    } else {
      this.logger.log(`Adding regular chunk #${chunkNumber}`);
      chunkDataArray.push({
        text: currentChunk,
        metadata: {
          headings: [...chunkHeadingHierarchy],
          sourceDocumentName: sourceDocumentName,
          mainSections: [...mainSections],
          pageNumber: pageNumberMetadata,
          chunkNumber: chunkNumber,
        },
      });
      chunkNumber++;
    }

    this.logger.log(
      `Finished processing chunk, new chunk number: ${chunkNumber}`
    );
    return chunkNumber;
  }

  /**
   * Create page-based chunk for PDF documents
   */
  createPageChunk({
    pageContent,
    headingHierarchy,
    sourceDocumentName,
    mainSections,
    pageNumber,
    chunkNumber,
  }: {
    pageContent: string;
    headingHierarchy: string[];
    sourceDocumentName: string;
    mainSections: string[];
    pageNumber: number;
    chunkNumber: number;
  }): DocumentChunkGenerated {
    return {
      text: pageContent,
      metadata: {
        headings: [...headingHierarchy],
        sourceDocumentName: sourceDocumentName,
        mainSections: [...mainSections],
        pageNumber: pageNumber.toString(),
        chunkNumber: chunkNumber,
      },
    };
  }

  /**
   * Create table chunk for spreadsheet documents
   */
  createTableChunk({
    text,
    sourceDocumentName,
    sheetName,
    tableNumber,
    pageNumber,
    chunkNumber,
  }: {
    text: string;
    sourceDocumentName: string;
    sheetName: string;
    tableNumber: number;
    pageNumber: number;
    chunkNumber: number;
  }): DocumentChunkGenerated {
    return {
      text,
      metadata: {
        sourceDocumentName: sourceDocumentName,
        headings: [sheetName, `Table ${tableNumber}`],
        pageNumber: String(pageNumber),
        chunkNumber: chunkNumber,
        mainSections: [],
      },
    };
  }
}
