import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Loader2 } from 'lucide-react';
import { fetchPromptHistory, type PromptHistory } from '@/api/admin/admin.api';
import { Card } from '@/components/ui/card';

interface PromptHistoryModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  promptId: string;
  promptName: string;
}

export function PromptHistoryModal({
  open,
  onOpenChange,
  promptId,
  promptName,
}: PromptHistoryModalProps) {
  const { data: history = [], isLoading } = useQuery({
    queryKey: ['prompt-history', promptId],
    queryFn: () => fetchPromptHistory(promptId),
    enabled: open && !!promptId,
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Version History - {promptName}</DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : history.length === 0 ? (
          <p className="text-center text-muted-foreground py-8">
            No version history available for this prompt.
          </p>
        ) : (
          <Accordion type="single" collapsible className="w-full">
            {history.map((item: PromptHistory, index: number) => (
              <AccordionItem key={item.id} value={item.id}>
                <AccordionTrigger>
                  <div className="flex flex-col items-start text-left">
                    <span className="font-medium">
                      Version {history.length - index}
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {format(new Date(item.createdAt), 'PPp')} by{' '}
                      {item.changedBy?.name ||
                        item.changedBy?.email ||
                        'Unknown'}
                    </span>
                    {item.changes?.comment && (
                      <span className="text-sm text-muted-foreground italic mt-1">
                        "{item.changes.comment}"
                      </span>
                    )}
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-4">
                    {index < history.length - 1 && (
                      <Card className="p-4">
                        <h4 className="font-medium mb-2">Previous Version:</h4>
                        <pre className="whitespace-pre-wrap text-sm bg-muted p-3 rounded-md">
                          {item.oldPrompt}
                        </pre>
                      </Card>
                    )}
                    <Card className="p-4">
                      <h4 className="font-medium mb-2">
                        {index === 0 ? 'Current' : 'New'} Version:
                      </h4>
                      <pre className="whitespace-pre-wrap text-sm bg-muted p-3 rounded-md">
                        {item.newPrompt}
                      </pre>
                    </Card>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        )}
      </DialogContent>
    </Dialog>
  );
}
