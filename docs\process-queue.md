# **Bull Queue Setup & Monitoring Guide**

This documentation serves as a guide for developers to seamlessly integrate new queues into the project using **Bull** and **BullBoard**. It covers the setup of a new queue, the structure of processors, how to monitor jobs using BullBoard, and follows our centralized configuration architecture.

---

## **1. Understanding the Queue Structure**
A queue in **Bull** consists of:
- **Producers**: Services that add jobs to the queue.
- **Processors**: Workers that execute the jobs.
- **BullBoard**: A UI to monitor job processing (centrally registered).
- **Configuration**: Centralized settings for consistency across all queues.

Each queue follows a modular pattern with centralized configuration, ensuring separation of concerns and maintainability.

---

## **2. Centralized Configuration**

All queue configurations are managed in a single file to ensure consistency:

### **Location**: `/src/util/queue.config.ts`

### **Configuration Features**:
- **Job Cleanup**: Automatic removal of old jobs to prevent Redis memory bloat
- **Retry Policy**: Standardized retry behavior with exponential backoff
- **Environment-Aware**: Different settings for development vs production

### **Default Settings**:
- **Completed Jobs**: Removed after 30 minutes OR when exceeding 100 jobs
- **Failed Jobs**: Removed after 3 days OR when exceeding 200 jobs
- **Retries**: 5 attempts with exponential backoff starting at 5 seconds
- **Development Mode**: Jobs kept indefinitely for debugging

---

## **3. Setting Up a New Queue**

### **3.1 Register the Queue in a Module**
Each queue should be registered in a NestJS module using `BullModule.registerQueue()` with centralized configuration.

**Example (queue.module.ts)**
```ts
import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { ExampleQueueProcessor } from './queue.service';
import { SomeServiceModule } from 'src/some-service/some-service.module';
import { DEFAULT_QUEUE_JOB_OPTIONS } from 'src/util/queue.config';

@Module({
  imports: [
    BullModule.registerQueue({ 
      name: 'example-queue',
      defaultJobOptions: DEFAULT_QUEUE_JOB_OPTIONS, // Use centralized config
    }),
    SomeServiceModule,
  ],
  providers: [ExampleQueueProcessor],
})
export class ExampleQueueModule {}
```
📌 **Key Points:**
- Import `DEFAULT_QUEUE_JOB_OPTIONS` from the centralized config
- **DO NOT** register BullBoardModule here - it's centralized in app.module.ts
- The queue processor is added to `providers`

---

### **3.2 Implementing the Queue Processor**
A **processor** defines how jobs in a queue are handled.

**Example (queue.service.ts)**
```ts
import { Injectable, Logger } from '@nestjs/common';
import { Processor, Process } from '@nestjs/bull';
import { Job } from 'bull';
import { SomeService } from 'src/some-service/some-service.service';

@Injectable()
@Processor('example-queue') // Link processor to the queue
export class ExampleQueueProcessor {
  private readonly logger = new WorkerLogger(ExampleQueueProcessor.name);

  constructor(private readonly someService: SomeService) {}

  @Process({ name: 'process-example-task', concurrency: 5 })
  async handleExampleTask(job: Job) {
    const { data } = job;
    this.logger.log(`Processing example task: ${JSON.stringify(data)}`);

    try {
      // Perform some processing
      await this.someService.performTask(data);
      this.logger.log(`Successfully processed job ${job.id}`);
    } catch (error) {
      this.logger.error(`Error processing job ${job.id}`, error);
      throw error; // Bull will handle retries based on config
    }
  }

  // Clean up on shutdown
  async onModuleDestroy() {
    await this.queue.close();
  }
}
```
📌 **Key Points:**
- `@Processor('example-queue')`: Binds the processor to a queue
- `@Process()`: Defines a specific task inside the queue
- `concurrency`: Defines how many jobs can run in parallel
- Include `onModuleDestroy` for proper cleanup

---

### **3.3 Adding Jobs to the Queue**
A **producer** service is responsible for adding jobs using centralized configuration.

**Example (producer.service.ts)**
```ts
import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { DEFAULT_JOB_CONFIG } from 'src/util/queue.config';

@Injectable()
export class ExampleQueueProducer {
  constructor(@InjectQueue('example-queue') private readonly queue: Queue) {}

  async addExampleJob(taskData: any) {
    await this.queue.add(
      'process-example-task',
      taskData,
      DEFAULT_JOB_CONFIG // Use centralized config
    );
  }

  // With custom job ID
  async addExampleJobWithId(taskData: any) {
    await this.queue.add(
      'process-example-task',
      taskData,
      {
        ...DEFAULT_JOB_CONFIG,
        jobId: `example-${taskData.id}`,
      }
    );
  }
}
```
📌 **Key Points:**
- Import `DEFAULT_JOB_CONFIG` for consistent job settings
- Spread operator allows overriding specific options like `jobId`
- Cleanup and retry policies are automatically applied

---

### **3.4 Register BullBoard Adapter (App Module Only)**
**IMPORTANT**: BullBoard adapters must ONLY be registered in `app.module.ts` to prevent event listener conflicts.

**Example (app.module.ts)**
```ts
import { Module } from '@nestjs/common';
import { BullBoardModule } from '@bull-board/nestjs';
import { ExpressAdapter } from '@bull-board/express';
import { BullAdapter } from '@bull-board/api/bullAdapter';

@Module({
  imports: [
    // Root configuration
    BullBoardModule.forRoot({
      route: '/api/queues',
      adapter: ExpressAdapter,
    }),
    
    // Register ALL queue adapters here
    BullBoardModule.forFeature({
      name: 'example-queue',
      adapter: BullAdapter,
    }),
    BullBoardModule.forFeature({
      name: 'another-queue',
      adapter: BullAdapter,
    }),
    // ... other queue adapters
  ],
})
export class AppModule {}
```

⚠️ **Critical**: Never register BullBoard adapters in feature modules - this causes memory leaks!

---

## **4. Monitoring with BullBoard**

### **4.1 Accessing BullBoard**
Navigate to:
```
http://localhost:<PORT>/api/queues
```

### **4.2 Features Available**
- View job status (`waiting`, `active`, `completed`, `failed`)
- Retry or remove jobs manually
- Debug failed jobs with error details
- Monitor job processing metrics
- View cleanup effectiveness (jobs are automatically removed based on config)

---

## **5. Modifying Queue Configuration**

To change cleanup, retry, or other settings globally:

1. Edit `/src/util/queue.config.ts`
2. Update the relevant configuration values:
   ```ts
   export const QUEUE_JOB_CLEANUP_CONFIG = {
     removeOnComplete: {
       age: 30 * 60,    // 30 minutes
       count: 100,      // Keep max 100 jobs
     },
     removeOnFail: {
       age: 3 * 24 * 60 * 60, // 3 days
       count: 200,            // Keep max 200 jobs
     },
   };
   ```
3. All queues automatically use the new settings

---

## **6. Best Practices**

### **Queue Setup**
- ✅ Register queues in their respective modules
- ✅ Use centralized configuration from `queue.config.ts`
- ✅ Register BullBoard adapters ONLY in `app.module.ts`
- ✅ Implement `onModuleDestroy` for cleanup

### **Job Processing**
- ✅ Use clear, descriptive job names
- ✅ Set appropriate concurrency based on workload
- ✅ Let the centralized config handle retries and cleanup
- ✅ Include comprehensive logging for debugging

### **Memory Management**
- ✅ Monitor Redis memory usage
- ✅ Watch for MaxListenersExceededWarning
- ✅ Rely on automatic job cleanup to prevent bloat
- ✅ Set process event listener limits if needed:
  ```ts
  // In main.ts
  const EventEmitter = require('events');
  EventEmitter.defaultMaxListeners = 15;
  ```

### **Development vs Production**
- Development: Jobs kept indefinitely for debugging
- Production: Automatic cleanup after configured time/count limits

---

## **7. Troubleshooting**

### **MaxListenersExceededWarning**
If you see this warning, check that:
- BullBoard adapters are ONLY registered in `app.module.ts`
- No duplicate queue registrations exist
- Each queue has only one BullAdapter instance

### **Redis Memory Growth**
- Verify cleanup configuration is applied
- Check BullBoard for accumulating jobs
- Monitor job processing rates vs creation rates

---

## **8. Summary Checklist**

When adding a new queue:
1. ✅ Create queue module with `BullModule.registerQueue()` using `DEFAULT_QUEUE_JOB_OPTIONS`
2. ✅ Implement processor with proper error handling and cleanup
3. ✅ Create producer service using `DEFAULT_JOB_CONFIG`
4. ✅ Add BullBoard adapter ONLY in `app.module.ts`
5. ✅ Test job processing and monitor via BullBoard at `/api/queues`

This architecture ensures consistent behavior, prevents memory issues, and simplifies queue management across the application. 🚀
