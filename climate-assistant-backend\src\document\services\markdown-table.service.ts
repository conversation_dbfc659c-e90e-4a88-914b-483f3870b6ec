import { Injectable } from '@nestjs/common';
import { DocumentParsingUtils } from './utils';
import { WorkerLogger } from 'src/shared/logger.service';

@Injectable()
export class MarkdownTableService {
  private readonly logger = new WorkerLogger(MarkdownTableService.name);

  /**
   * Parse markdown table from lines
   */
  parseMarkdownTable(tableLines: string[]): {
    headers: any[][];
    rows: any[][];
  } {
    this.logger.log(`Parsing markdown table with ${tableLines.length} lines`);

    // Find the separator line index (line with |---|---|)
    const separatorLineIndex = tableLines.findIndex(
      (line) =>
        line.trim().includes('---') &&
        /^\|(\s*[-]+\s*\|)+\s*$/.test(line.trim())
    );

    if (separatorLineIndex <= 0) {
      this.logger.log('Table format appears to use single header row');
      const headersLine = tableLines[0];
      const dataLines = tableLines.slice(2); // Skip separator line

      const headers = [
        headersLine
          .trim()
          .split('|')
          .map((header) => header.trim())
          .filter((header) => header.length > 0),
      ];

      const rows = dataLines.map((line) => {
        return line
          .trim()
          .split('|')
          .map((cell) => cell.trim())
          .filter((cell) => cell.length > 0);
      });

      const rowsWithContent = rows.filter((row) =>
        row.some((cell) => cell !== '')
      );

      this.logger.log(
        `Table parsed: ${headers.length} header rows, ${rowsWithContent.length} data rows`
      );
      return { headers, rows: rowsWithContent };
    }

    // All lines before separator are headers
    const headerLines = tableLines.slice(0, separatorLineIndex);
    // All lines after separator are data
    const dataLines = tableLines.slice(separatorLineIndex + 1);

    // Parse header rows
    const headers = headerLines.map((line) =>
      line
        .trim()
        .split('|')
        .map((header) => header.trim())
        .filter((header) => header.length > 0)
    );

    // Parse data rows
    const rows = dataLines.map((line) => {
      return line
        .trim()
        .split('|')
        .map((cell) => cell.trim())
        .filter((cell) => cell.length > 0);
    });

    // Remove rows where all cells are empty
    const rowsWithContent = rows.filter((row) =>
      row.some((cell) => cell !== '')
    );

    this.logger.log(
      `Table parsed: ${headers.length} header rows, ${rowsWithContent.length} data rows`
    );
    return { headers, rows: rowsWithContent };
  }

  /**
   * Create markdown table from headers and rows
   */
  createMarkdownTable(headers: any[][], rows: any[][]): string {
    this.logger.log(
      `Creating markdown table with ${headers.length} header rows and ${rows.length} data rows`
    );

    if (headers.length === 0 || rows.length === 0) return '';

    let markdown = '';

    // Find the maximum number of columns across all headers and rows
    const maxColumns = Math.max(
      ...headers.map((row) => row.length),
      ...rows.map((row) => row.length)
    );

    // Extend all header rows to match maximum columns
    const extendedHeaders = headers.map((headerRow) => {
      const extended = [...headerRow];
      while (extended.length < maxColumns) {
        extended.push('');
      }
      return extended;
    });

    // Determine which columns to include (have data in any header or data row)
    const columnsToInclude: number[] = [];

    for (let colIndex = 0; colIndex < maxColumns; colIndex++) {
      let hasData = false;

      // Check header rows
      for (const headerRow of extendedHeaders) {
        const cell = headerRow[colIndex];
        if (cell !== undefined && cell !== null && String(cell).trim() !== '') {
          hasData = true;
          break;
        }
      }

      // Check data rows if needed
      if (!hasData) {
        for (const row of rows) {
          const cell = row[colIndex];
          if (
            cell !== undefined &&
            cell !== null &&
            String(cell).trim() !== ''
          ) {
            hasData = true;
            break;
          }
        }
      }

      if (hasData) {
        columnsToInclude.push(colIndex);
      }
    }

    // If less than 2 columns have data, return an empty string
    if (columnsToInclude.length < 2) {
      return '';
    }

    // Special case: Only one header row - standard markdown table format
    if (extendedHeaders.length === 1) {
      // Create the single header row
      const filteredRow = columnsToInclude.map((colIndex) =>
        DocumentParsingUtils.escapeCell(extendedHeaders[0][colIndex])
      );
      markdown += '| ' + filteredRow.join(' | ') + ' |\n';

      // Create separator row
      markdown += '| ' + columnsToInclude.map(() => '---').join(' | ') + ' |\n';

      // Create data rows
      for (const row of rows) {
        const rowData = columnsToInclude.map((colIndex) => {
          const cellData =
            row[colIndex] !== undefined && row[colIndex] !== null
              ? row[colIndex]
              : '';
          return DocumentParsingUtils.escapeCell(cellData);
        });
        markdown += '| ' + rowData.join(' | ') + ' |\n';
      }
    }
    // Multiple header rows - use a different approach to make them visually distinct
    else {
      // Use the first header row as the actual markdown header
      const firstHeaderRow = extendedHeaders[0];
      const filteredFirstHeaderRow = columnsToInclude.map((colIndex) =>
        DocumentParsingUtils.escapeCell(firstHeaderRow[colIndex])
      );
      markdown += '| ' + filteredFirstHeaderRow.join(' | ') + ' |\n';

      // Create separator row
      markdown += '| ' + columnsToInclude.map(() => '---').join(' | ') + ' |\n';

      // Add remaining header rows as bold data rows
      for (let i = 1; i < extendedHeaders.length; i++) {
        const headerRow = extendedHeaders[i];
        const filteredRow = columnsToInclude.map((colIndex) => {
          const cell = headerRow[colIndex];
          const cellStr = DocumentParsingUtils.escapeCell(cell);
          // Make header cells bold
          return cellStr ? `**${cellStr}**` : '';
        });
        markdown += '| ' + filteredRow.join(' | ') + ' |\n';
      }

      // Create data rows
      for (const row of rows) {
        const rowData = columnsToInclude.map((colIndex) => {
          const cellData =
            row[colIndex] !== undefined && row[colIndex] !== null
              ? row[colIndex]
              : '';
          return DocumentParsingUtils.escapeCell(cellData);
        });
        markdown += '| ' + rowData.join(' | ') + ' |\n';
      }
    }

    this.logger.log(`Markdown table created, length: ${markdown.length}`);
    return markdown;
  }

  /**
   * Split table by token count recursively
   */
  splitTableByTokenCount(
    headers: any[][],
    rows: any[][],
    maxTokens: number
  ): string[] {
    this.logger.log(
      `Splitting table by token count, threshold: ${maxTokens}, rows: ${rows.length}`
    );

    const tables: string[] = [];

    // Adjust headers for consistent column count
    const maxColumns = Math.max(
      ...headers.map((row) => row.length),
      ...rows.map((row) => row.length)
    );

    const extendedHeaders = headers.map((headerRow) => {
      const extended = [...headerRow];
      while (extended.length < maxColumns) {
        extended.push('');
      }
      return extended;
    });

    this.splitRowsRecursively({
      headers: extendedHeaders,
      rows: rows,
      start: 0,
      end: rows.length,
      maxTokens: maxTokens,
      result: tables,
    });

    this.logger.log(
      `Table splitting complete, generated ${tables.length} table chunks`
    );
    return tables;
  }

  private splitRowsRecursively({
    headers,
    rows,
    start,
    end,
    maxTokens,
    result,
  }: {
    headers: any[][];
    rows: any[][];
    start: number;
    end: number;
    maxTokens: number;
    result: string[];
  }) {
    this.logger.log(
      `Splitting rows recursively: rows ${start}-${end}, max tokens: ${maxTokens}`
    );

    const chunkRows = rows.slice(start, end);
    const markdown = this.createMarkdownTable(headers, chunkRows);
    if (!markdown) {
      return; // Skip empty
    }
    const tokenCount = DocumentParsingUtils.countTokens(markdown);
    if (tokenCount > maxTokens && end - start > 1) {
      const mid = Math.floor((start + end) / 2);
      this.splitRowsRecursively({
        headers,
        rows,
        start,
        end: mid,
        maxTokens,
        result,
      });
      this.splitRowsRecursively({
        headers,
        rows,
        start: mid,
        end,
        maxTokens,
        result,
      });
    } else {
      result.push(markdown);
    }
  }
}
