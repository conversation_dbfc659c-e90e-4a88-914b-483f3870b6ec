name: Deploy to Server

on:
  push:
    branches:
      - staging
  pull_request:
    branches:
      - staging

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout the repository
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '20'
      
      # Cache dependencies for frontend
      - name: Cache frontend dependencies
        uses: actions/cache@v3
        with:
          path: climate-assistant-frontend/node_modules
          key: frontend-${{ runner.os }}-${{ hashFiles('climate-assistant-frontend/package-lock.json') }}
          restore-keys: |
            frontend-${{ runner.os }}-

      - name: Install dependencies for frontend
        run: |
          cd climate-assistant-frontend
          npm install

      # Cache dependencies for backend
      - name: Cache backend dependencies
        uses: actions/cache@v3
        with:
          path: climate-assistant-backend/node_modules
          key: backend-${{ runner.os }}-${{ hashFiles('climate-assistant-backend/package-lock.json') }}
          restore-keys: |
            backend-${{ runner.os }}-

      - name: Install dependencies for backend
        run: |
          cd climate-assistant-backend
          npm install


      - name: Run build for frontend
        run: |
          cd climate-assistant-frontend
          npm run build
        env:
          CI: false

      - name: Run build for backend
        run: |
          cd climate-assistant-backend
          npm run build
        env:
          CI: false
      
      - name: Send Build Failure Notification to Teams
        if: failure() && github.event_name == 'push' && github.ref == 'refs/heads/staging' && !contains(github.event.head_commit.message, '[skip deploy]')
        env: 
          DEPLOY_TRIGGER: ${{ github.event.head_commit.message || github.event.pull_request.title }}
          MERGE_INITIATOR: ${{ github.event.pusher.name }}
        run: |
          TODAY=$(date)
          curl -X POST -H 'Content-Type: application/json' --data '{
            "@type": "MessageCard",
            "@context": "http://schema.org/extensions",
            "summary": "Build Status",
            "themeColor": "DC2626",
            "sections": [{
              "activityTitle": "Staging Build Failed 🚨",
              "activitySubtitle": "'"$DEPLOY_TRIGGER"'",
              "facts": [
                {
                  "name": "Date of Build",
                  "value": "'"$TODAY"'"
                },
                {
                  "name": "Initiated By",
                  "value": "'"$MERGE_INITIATOR"'"
                }
              ],
              "text": "The build process has failed. This will prevent deployment to staging. Please check the logs for details.",
              "markdown": true
            }],
            "potentialAction": [{
              "@type": "OpenUri",
              "name": "View Build Logs",
              "targets": [{
                "os": "default",
                "uri": "https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
              }]
            }]
          }' ${{ secrets.TEAMS_WEBHOOK_URL }}

  deploy:
    if: github.event_name == 'push' && github.ref == 'refs/heads/staging' && !contains(github.event.head_commit.message, '[skip deploy]')
    runs-on: ubuntu-latest
    needs: build

    steps:
    # Step 1: Checkout the repository
    - name: Checkout Code
      uses: actions/checkout@v3

    # Step 2: Set up SSH
    - name: Set up SSH
      uses: webfactory/ssh-agent@v0.5.3
      with:
        ssh-private-key: ${{ secrets.STAGING_SSH_PRIVATE_KEY_GCP }}

    # Step 3: Deploy the application
    - name: Deploy to Server
      run: |
        ssh -o StrictHostKeyChecking=no kishan.chimminiyan@${{ secrets.STAGING_SERVER_IP_GCP }} << 'EOF'
        set -e 
        cd climate-assistant/
        sudo su
        eval "$(ssh-agent -s)"
        ssh-add ~/.ssh/github
        git pull
        docker compose up -d --build backend frontend
        docker compose restart nginx
        docker system prune -af
        EOF

    # Step 4: Send success notification to Teams
    - name: Send Success Notification to Teams
      if: success()
      env:
        DEPLOY_TRIGGER: ${{ github.event.head_commit.message || github.event.pull_request.title }}
        MERGE_INITIATOR: ${{ github.event.pusher.name }}
      run: |
        TODAY=$(date)
        curl -X POST -H 'Content-Type: application/json' --data '{
          "@type": "MessageCard",
          "@context": "http://schema.org/extensions",
          "summary": "Deployment Status",
          "themeColor": "6DD4AD",
          "sections": [{
            "activityTitle": "Staging Deployment Successful 🎉",
            "activitySubtitle": "'"$DEPLOY_TRIGGER"'",
            "facts": [
              {
                "name": "Date of Deployment",
                "value": "'"$TODAY"'"
              },
              {
                "name": "Initiated By",
                "value": "'"$MERGE_INITIATOR"'"
              }
            ],
            "text": "The deployment to the staging server has been completed successfully.",
            "markdown": true
          }],
          "potentialAction": [{
            "@type": "OpenUri",
            "name": "Create Production PR",
            "targets": [{
              "os": "default",
              "uri": "https://github.com/Glacier-Carbon-Reduction/climate-assistant/compare/main...staging?expand=1"
            }]
          },{
            "@type": "OpenUri",
            "name": "Visit Staging App",
            "targets": [{
              "os": "default",
              "uri": "http://staging.glacier.eco/dashboard"
            }]
          },{
            "@type": "OpenUri",
            "name": "View Build Logs",
            "targets": [{
              "os": "default",
              "uri": "https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
            }]
          }]
        }' ${{ secrets.TEAMS_WEBHOOK_URL }}

    # Step 5: Send failure notification to Teams
    - name: Send Failure Notification to Teams
      if: failure()
      env:
        DEPLOY_TRIGGER: ${{ github.event.head_commit.message || github.event.pull_request.title }}
        MERGE_INITIATOR: ${{ github.event.pusher.name }}
      run: |
        TODAY=$(date)
        curl -X POST -H 'Content-Type: application/json' --data '{
          "@type": "MessageCard",
          "@context": "http://schema.org/extensions",
          "summary": "Deployment Status",
          "themeColor": "DC2626",
          "sections": [{
            "activityTitle": "Staging Deployment Failed 🚨",
            "activitySubtitle": "'"$DEPLOY_TRIGGER"'",
            "facts": [
              {
                "name": "Date of Deployment",
                "value": "'"$TODAY"'"
              },
              {
                "name": "Initiated By",
                "value": "'"$MERGE_INITIATOR"'"
              }
            ],
            "text": "The deployment to the staging server has failed. Please check the logs for details.",
            "markdown": true
          }],
          "potentialAction": [{
            "@type": "OpenUri",
            "name": "View Build Logs",
            "targets": [{
              "os": "default",
              "uri": "https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
            }]
          }]
        }' ${{ secrets.TEAMS_WEBHOOK_URL }}
