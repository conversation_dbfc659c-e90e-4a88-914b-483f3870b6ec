# PM2 Cluster Mode: Complete Implementation Guide

## Overview

The Climate Assistant backend runs on PM2 cluster mode with 3 worker processes, providing better performance, fault tolerance, and resource utilization. This document covers the complete implementation including configuration, optimizations, and solutions for cluster-specific challenges.

## Architecture

graph TB
subgraph "External"
Client[Client/Browser]
Nginx[NGINX<br/>Port 443]
end

    subgraph "PM2 Cluster"
        PM2[PM2 Master Process<br/>Load Balancer]
        W0[Worker 0<br/>Main Worker<br/>PID: XXX]
        W1[Worker 1<br/>PID: YYY]
        W2[Worker 2<br/>PID: ZZZ]
    end

    subgraph "Data Layer"
        PG[(PostgreSQL<br/>Max 24 connections<br/>8 per worker)]
        Redis[(Redis<br/>• Bull Queues<br/>• Pub/Sub<br/>• Cache)]
    end

    Client --> Nginx
    Nginx --> PM2
    PM2 -->|Round-robin| W0
    PM2 -->|Round-robin| W1
    PM2 -->|Round-robin| W2

    W0 -->|"✓ API<br/>✓ Queues<br/>✓ Cron Jobs<br/>✓ SSE"| PG
    W1 -->|"✓ API<br/>✓ Queues<br/>✗ Cron Jobs<br/>✓ SSE"| PG
    W2 -->|"✓ API<br/>✓ Queues<br/>✗ Cron Jobs<br/>✓ SSE"| PG

    W0 --> Redis
    W1 --> Redis
    W2 --> Redis

    Redis -.->|Pub/Sub Events| W0
    Redis -.->|Pub/Sub Events| W1
    Redis -.->|Pub/Sub Events| W2

The system uses PM2's built-in load balancer to distribute requests across 3 Node.js processes (workers):

- **PM2 Master Process**: Manages workers and load balancing
- **3 Worker Processes**: Each handles API requests, with specialized roles
- **Round-Robin Distribution**: Requests distributed sequentially across workers
- **Shared Resources**: Redis for queues/pub-sub, PostgreSQL for data

### Worker Responsibilities

| Feature          | Worker 0 (Main) | Worker 1      | Worker 2      |
| ---------------- | --------------- | ------------- | ------------- |
| API Requests     | ✓               | ✓             | ✓             |
| Queue Processing | ✓               | ✓             | ✓             |
| Cron Jobs        | ✓               | ✗             | ✗             |
| SSE Connections  | ✓               | ✓             | ✓             |
| Database Pool    | 8 connections   | 8 connections | 8 connections |

## Configuration Files

### Production Configuration (`ecosystem.config.js`)

```javascript
module.exports = {
  apps: [
    {
      name: 'climate-assistant-backend',
      script: './dist/main.js',
      instances: 3,
      exec_mode: 'cluster',
      max_memory_restart: '1500M',
      watch: false,
      env: {
        NODE_ENV: 'production',
        NODE_OPTIONS: '--max-old-space-size=5120',
      },
      error_file: './logs/err.log',
      out_file: './logs/out.log',
      merge_logs: true,
      time: true,
      kill_timeout: 5000,
      listen_timeout: 3000,
      max_restarts: 10,
      restart_delay: 4000,
      autorestart: true,
      cron_restart: '0 3 * * *',
      min_uptime: '10s',
    },
  ],
};
```

### Development Configuration (`ecosystem.dev.config.js`)

```javascript
module.exports = {
  apps: [
    {
      name: 'climate-assistant-backend',
      script: './dist/main.js',
      instances: 1,
      exec_mode: 'fork',
      watch: ['dist'],
      env: {
        NODE_ENV: 'development',
        NODE_OPTIONS: '--max-old-space-size=2048',
      },
    },
  ],
};
```

## Implementation Details

### 1. Database Connection Pool Optimization

Each worker maintains its own connection pool. Configuration adjusted for 3 workers:

```typescript
// data-source.ts
export const AppDataSource = new DataSource({
  // ... existing config
  extra: {
    max: 8, // 8 connections per worker (24 total)
    min: 2, // Minimum connections per pool
    acquire: 30000, // Max time to acquire connection
    idle: 10000, // Max idle time
    evict: 10000, // Check for idle connections
  },
});
```

### 2. Bull Queue Concurrency Adjustments

Queue concurrency reduced to account for 3 workers:

```typescript
// Queue processors with adjusted concurrency
@Process({ name: JobQueue.DatapointGenerate, concurrency: 3 }) // Was 10, now 3×3=9
@Process({ name: JobQueue.DatapointReview, concurrency: 3 })   // Was 10, now 3×3=9
@Process({ name: JobQueue.ChunkExtract, concurrency: 2 })      // Was 5, now 2×3=6
@Process({ name: JobQueue.ChunkDpLink, concurrency: 1 })       // Was 2, now 1×3=3
```

### 3. Cron Job Single-Worker Execution

Only the main worker (instance 0) runs cron jobs to prevent duplication:

```typescript
// cron.service.ts
@Injectable()
export class CronService {
  private readonly isMainWorker =
    process.env.INSTANCE_ID === '0' || !process.env.INSTANCE_ID;
  private readonly workerId = process.env.INSTANCE_ID || process.pid;

  @Cron('*/10 * * * *')
  async checkUnprocessedDocuments() {
    if (!this.isMainWorker) {
      this.logger.debug(
        `Worker ${this.workerId} skipping cron - not main worker`
      );
      return;
    }

    this.logger.log(
      `Main worker ${this.workerId} checking unprocessed documents...`
    );
    // ... existing logic
  }

  @Cron('0 2 * * *')
  async dailyWorkspaceLinkCleanup() {
    if (!this.isMainWorker) return;
    // ... cleanup logic
  }
}
```

### 4. Shared LLM Rate Limiting Constants

All LLM rate limiting configuration is centralized in a shared constants file:

```typescript
// src/utils/llm-rate-limit.constants.ts
export const MODEL_LIMITS: Record<LLM_MODELS, ModelLimits> = {
  [LLM_MODELS['gpt-4o']]: {
    maxTokensPerMinute: 445000,
    maxRequestsPerMinute: 267,
  },
  // ... other models
};

export const RATE_LIMIT_CONFIG = {
  RESET_INTERVAL: 80 * 1000, // 80 seconds
  REDIS_KEY_PREFIX: 'llm_rate_limit',
  REDIS_KEY_EXPIRY: 120, // 120 seconds
} as const;

export function getRedisKey(
  model: LLM_MODELS,
  field: 'tokens' | 'requests' | 'lastReset'
): string {
  return `${RATE_LIMIT_CONFIG.REDIS_KEY_PREFIX}:${model}:${field}`;
}

export function getModelStatus(
  tokensUsed: number,
  requestsUsed: number,
  limits: ModelLimits
): string {
  // Returns: 'idle', 'active', 'moderate', 'warning', 'critical'
}
```

Both the rate limiter service and health controller import these shared constants, eliminating code duplication and ensuring consistency.

### 5. Redis-based LLM Rate Limiting

LLM rate limiting uses Redis for shared state across all workers to prevent API quota breaches:

```typescript
// llm-rate-limiter.service.ts
@Injectable()
export class LlmRateLimiterService implements OnModuleDestroy {
  private readonly redis: Redis;
  private readonly REDIS_KEY_PREFIX = 'llm_rate_limit';

  constructor() {
    this.redis = new Redis({
      host: getRedisHost(),
      port: 6379,
    });
  }

  private async getModelUsage(model: LLM_MODELS): Promise<TokenUsage> {
    // Get shared token/request counters from Redis
    const pipeline = this.redis.pipeline();
    pipeline.get(this.getRedisKey(model, 'tokens'));
    pipeline.get(this.getRedisKey(model, 'requests'));
    pipeline.get(this.getRedisKey(model, 'lastReset'));

    const results = await pipeline.exec();

    return {
      tokensUsed: parseInt(results[0][1] as string) || 0,
      requestsUsed: parseInt(results[1][1] as string) || 0,
      lastReset: parseInt(results[2][1] as string) || 0,
    };
  }

  private async updateModelUsage(
    model: LLM_MODELS,
    tokensToAdd: number
  ): Promise<void> {
    // Atomically update counters across all workers
    const pipeline = this.redis.pipeline();
    pipeline.incrby(this.getRedisKey(model, 'tokens'), tokensToAdd);
    pipeline.incr(this.getRedisKey(model, 'requests'));
    await pipeline.exec();
  }
}
```

This ensures that all workers share the same token/request counters, preventing any single model from exceeding its API limits even when multiple workers are making concurrent requests.

### 6. Redis Pub/Sub for SSE Events

```typescript
// redis-event.service.ts
import { Injectable, OnModuleDestroy } from '@nestjs/common';
import { Redis } from 'ioredis';
import { Subject } from 'rxjs';
import { getRedisHost } from '../env-helper';

@Injectable()
export class RedisEventService implements OnModuleDestroy {
  private publisher: Redis;
  private subscriber: Redis;
  private readonly eventSubjects = new Map<string, Subject<any>>();

  constructor() {
    this.publisher = new Redis({
      host: getRedisHost(),
      port: 6379,
    });

    this.subscriber = new Redis({
      host: getRedisHost(),
      port: 6379,
    });

    this.subscriber.on('message', (channel, message) => {
      const subject = this.eventSubjects.get(channel);
      if (subject) {
        try {
          const data = JSON.parse(message);
          subject.next(data);
        } catch (error) {
          console.error('Failed to parse Redis message:', error);
        }
      }
    });
  }

  subscribe<T>(channel: string): Subject<T> {
    if (!this.eventSubjects.has(channel)) {
      const subject = new Subject<T>();
      this.eventSubjects.set(channel, subject);
      this.subscriber.subscribe(channel);
    }
    return this.eventSubjects.get(channel) as Subject<T>;
  }

  async publish(channel: string, data: any): Promise<void> {
    await this.publisher.publish(channel, JSON.stringify(data));
  }

  async onModuleDestroy() {
    await this.publisher.quit();
    await this.subscriber.quit();
  }
}
```

```typescript
// data-request.service.ts - Updated to use Redis pub/sub
@Injectable()
export class DataRequestService {
  private readonly CHANNEL = 'datapoint-generation-events';

  constructor(private readonly redisEventService: RedisEventService) {
    this.events$ = this.redisEventService
      .subscribe<DatapointGenerationEvent>(this.CHANNEL)
      .asObservable();
  }

  public readonly events$: Observable<DatapointGenerationEvent>;

  async emitSseEvents(event: DatapointGenerationEvent) {
    const eventPayload = {
      ...event,
      timestamp: new Date(),
    };

    await this.redisEventService.publish(this.CHANNEL, eventPayload);
  }
}
```

## Health Check & Monitoring Endpoints

### Enhanced Health Check

```typescript
// health.controller.ts
@Get('cluster')
async getClusterStatus() {
  const isMainWorker = process.env.INSTANCE_ID === '0' || !process.env.INSTANCE_ID;

  return {
    worker: {
      id: process.env.INSTANCE_ID || process.pid,
      pid: process.pid,
      isMainWorker,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      roles: {
        cronJobs: isMainWorker,
        queueProcessing: true,
        apiRequests: true,
        sseEvents: true
      }
    },
    timestamp: new Date()
  };
}

@Get('connections')
async getDatabaseConnections() {
  const connections = await this.dataSource.query(
    'SELECT count(*) as active_connections FROM pg_stat_activity WHERE state = $1',
    ['active']
  );

  return {
    worker: process.pid,
    dbConnections: connections[0].active_connections,
    maxConnections: 8
  };
}
```

### LLM Rate Limiting Statistics

```typescript
// health.controller.ts
@Get('llm-stats')
async getLlmStats() {
  // Returns Redis-based rate limiting statistics across all workers
  return {
    timestamp: new Date().toISOString(),
    worker: {
      id: process.env.INSTANCE_ID || process.pid,
      pid: process.pid,
    },
    redis: {
      connected: true,
      memoryUsage: '2.5M',
    },
    models: {
      'gpt-4o': {
        usage: {
          tokensUsed: 12500,
          requestsUsed: 45,
          tokensPercentage: 3,
          requestsPercentage: 17,
        },
        limits: {
          maxTokensPerMinute: 445000,
          maxRequestsPerMinute: 267,
        },
        resetInfo: {
          lastReset: '2025-07-02T10:30:00.000Z',
          timeSinceReset: '45s',
          nextResetIn: '35s',
        },
        status: 'active'
      }
      // ...other models
    },
    summary: {
      totalActiveModels: 3,
      highUsageModels: ['gpt-4o']
    }
  };
}
```

## Deployment & Operations

### Starting PM2 in Production

The Dockerfile automatically uses PM2:

```dockerfile
CMD ["sh", "-c", "npm run migration:run:prod && npm run start:prod:pm2"]
```

### PM2 Commands Reference

```bash
# Inside container
docker exec -it climate-assistant-backend-1 sh

# View process status
pm2 status

# Monitor CPU/Memory in real-time
pm2 monit

# View logs
pm2 logs
pm2 logs --lines 100

# Reload with zero downtime
pm2 reload all

# Show detailed process info
pm2 show climate-assistant-backend

# View specific worker logs
pm2 logs 0  # Main worker
pm2 logs 1  # Worker 1
pm2 logs 2  # Worker 2
```

### Verification Scripts

```bash
# Verify PM2 instances
./scripts/verify-pm2-instances.sh

# Test Redis pub/sub
./scripts/test-redis-pubsub.sh

# Check worker distribution
for i in {1..6}; do
  curl https://app.glacier.eco/api/health/cluster | jq .worker.id
done

# Check LLM rate limiting statistics
curl https://app.glacier.eco/api/health/llm-stats | jq .

# Monitor specific model usage
curl https://app.glacier.eco/api/health/llm-stats | jq '.models["gpt-4o"].usage'

# Check for high usage models
curl https://app.glacier.eco/api/health/llm-stats | jq '.summary.highUsageModels'
```

## Troubleshooting Guide

### Issue: LLM Rate Limit Exceeded

**Symptom**: "Rate limit hit" errors despite low individual worker usage  
**Cause**: Cumulative usage across all workers exceeding API limits  
**Solution**: Redis-based rate limiting (already implemented)

```bash
# Debug Redis rate limiting
docker exec -it climate-assistant-redis-1 redis-cli KEYS "llm_rate_limit:*"

# Check current usage for a model
docker exec -it climate-assistant-redis-1 redis-cli GET "llm_rate_limit:gpt-4o:tokens"
docker exec -it climate-assistant-redis-1 redis-cli GET "llm_rate_limit:gpt-4o:requests"

# Reset counters manually if needed
docker exec -it climate-assistant-redis-1 redis-cli DEL "llm_rate_limit:gpt-4o:tokens" "llm_rate_limit:gpt-4o:requests"
```

### Issue: Events Not Reaching Frontend

**Symptom**: SSE events work intermittently  
**Cause**: Event emitted on different worker than client connection  
**Solution**: Redis pub/sub implementation (already implemented)

```bash
# Debug Redis pub/sub
docker exec -it climate-assistant-redis-1 redis-cli MONITOR | grep -E "(PUBLISH|SUBSCRIBE)"
```

### Issue: Database Connection Limit

**Symptom**: "too many connections" errors  
**Solution**: Already optimized (8 connections per worker = 24 total)

```sql
-- Check current connections
SELECT count(*) as total, state
FROM pg_stat_activity
WHERE datname = 'climate_assistant'
GROUP BY state;

-- Check connections by worker
SELECT application_name, count(*)
FROM pg_stat_activity
WHERE datname = 'climate_assistant'
GROUP BY application_name;
```

### Issue: Duplicate Cron Execution

**Symptom**: Same documents processed multiple times  
**Solution**: Only main worker runs crons (already implemented)

```bash
# Verify only one worker runs crons
docker exec -it climate-assistant-backend-1 pm2 logs | grep "checking for unprocessed"
```

### Issue: High Memory Usage

```bash
# Check memory per worker
pm2 monit

# Force restart if needed
pm2 restart all

# Check memory limits
pm2 show climate-assistant-backend | grep -E "(memory|restart)"
```

## Performance Benefits

- **3x Request Capacity**: Handles 3x more concurrent API requests
- **Fault Tolerance**: Other workers continue if one crashes
- **Zero-Downtime Deployments**: Rolling restarts maintain availability
- **Better CPU Utilization**: Utilizes multi-core servers effectively
- **Automatic Recovery**: PM2 restarts crashed workers automatically
- **Shared Rate Limiting**: Redis-based LLM rate limiting prevents API quota breaches across workers

## Quick Reference

### Key Configuration Values

| Setting               | Value         | Description                     |
| --------------------- | ------------- | ------------------------------- |
| Workers               | 3             | Number of cluster instances     |
| DB Connections/Worker | 8             | PostgreSQL connection pool size |
| Max Memory/Worker     | 1500MB        | Auto-restart threshold          |
| Queue Concurrency     | ÷3            | Adjusted for 3 workers          |
| Cron Jobs             | Worker 0 only | Prevents duplication            |
| Daily Restart         | 3 AM          | Prevents memory leaks           |

### Environment Variables

```bash
NODE_ENV=production
NODE_OPTIONS=--max-old-space-size=5120
INSTANCE_ID=0  # Set by PM2 (0, 1, or 2)
```

### Critical Files

- `ecosystem.config.js` - PM2 production configuration
- `ecosystem.dev.config.js` - PM2 development configuration
- `src/cron/cron.service.ts` - Single-worker cron implementation
- `src/events/redis-event.service.ts` - Redis pub/sub for SSE
- `src/llm-rate-limiter/llm-rate-limiter.service.ts` - Redis-based rate limiting
- `src/utils/llm-rate-limit.constants.ts` - Shared rate limiting constants
- `src/health/health.controller.ts` - Health checks and LLM statistics
- `src/database/data-source.ts` - Database connection pooling

## Next Steps

1. **Monitor Production**: Watch logs and metrics for 48 hours after deployment
2. **Tune Settings**: Adjust concurrency based on actual load
3. **Scale Further**: Can increase to 4-6 workers if needed
4. **Add Metrics**: Consider Prometheus metrics for detailed monitoring

This configuration provides a robust, scalable architecture while maintaining the simplicity of a single-container deployment.
