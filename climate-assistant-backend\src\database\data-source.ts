import * as dotenv from 'dotenv';
import { createDataSourceWithVectorSupport, getDBHost } from '../env-helper';

dotenv.config({ path: '../.env' });

export default createDataSourceWithVectorSupport({
  type: 'postgres',
  host: getDBHost(),
  port: +process.env.BACKEND_DB_PORT,
  username: process.env.BACKEND_DB_USER,
  password: process.env.BACKEND_DB_PASSWORD,
  database: process.env.BACKEND_DB_NAME,
  synchronize: false,
  dropSchema: false,
  logging: false,
  logger: 'file',
  entities: ['src/**/*.entity{.ts,.js}'],
  migrations: ['src/database/migrations/**/*.ts'],
  migrationsTableName: 'migration_table',
  // Connection pool optimization for PM2 cluster mode (3 workers)
  extra: {
    max: 8,           // Max connections per worker (8×3 = 24 total)
    min: 2,           // Min connections per worker
    acquire: 30000,   // Max time to get connection (30s)
    idle: 10000,      // Max idle time before closing connection (10s)
    evict: 10000,     // How often to check for idle connections (10s)
    acquireTimeoutMillis: 30000,
    idleTimeoutMillis: 10000,
  }
});
