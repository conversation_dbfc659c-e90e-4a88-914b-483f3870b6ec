import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataRequestService } from './data-request.service';
import { Workspace } from '../workspace/entities/workspace.entity';
import { DataRequestGuard } from './data-request.guard';
import { DataRequestController } from './data-request.controller';
import { DataRequest } from './entities/data-request.entity';
import { DatapointRequest } from '../datapoint/entities/datapoint-request.entity';
import { PromptModule } from 'src/prompts/prompts.module';
import { UsersModule } from 'src/users/users.module';
import { WorkspaceModule } from 'src/workspace/workspace.module';
import { DatapointRequestModule } from 'src/datapoint/datapoint-request.module';
import { BullModule } from '@nestjs/bull';
import { DataRequestGeneration } from './entities/datarequest-generation.entity';
import { LlmRateLimiterModule } from 'src/llm-rate-limiter/llm-rate-limiter.module';
import { SharedModule } from 'src/shared/shared.module';
import { ESRSDisclosureRequirement } from 'src/knowledge-base/entities/esrs-disclosure-requirement.entity';
import { ESRSDatapoint } from 'src/datapoint/entities/esrs-datapoint.entity';
import { User } from 'src/users/entities/user.entity';
import { UserPromptContext } from 'src/users/entities/user-prompt-context.entity';
import { VersionHistory } from 'src/workspace/entities/version-history.entity';
import { LlmModule } from 'src/llm/llm.module';
import { ProjectModule } from 'src/project/project.module';
import { DocumentModule } from 'src/document/document.module';
import { DatapointDocumentChunkModule } from 'src/datapoint-document-chunk/datapoint-document-chunk.module';
import { KnowledgeBaseModule } from 'src/knowledge-base/knowledge-base.module';
import { JobProcessor } from 'src/types/jobs';
import { DEFAULT_QUEUE_JOB_OPTIONS } from 'src/util/queue.config';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DataRequest,
      ESRSDisclosureRequirement,
      ESRSDatapoint,
      Workspace,
      User,
      UserPromptContext,
      VersionHistory,
      DatapointRequest,
      DataRequestGeneration,
    ]),
    forwardRef(() => DatapointRequestModule),
    UsersModule,
    PromptModule,
    WorkspaceModule,
    LlmModule,
    LlmRateLimiterModule,
    SharedModule,
    forwardRef(() => ProjectModule),
    BullModule.registerQueue({
      name: JobProcessor.DatapointGeneration,
      defaultJobOptions: DEFAULT_QUEUE_JOB_OPTIONS,
    }),
    BullModule.registerQueue({
      name: JobProcessor.DatapointReview,
      defaultJobOptions: DEFAULT_QUEUE_JOB_OPTIONS,
    }),
    forwardRef(() => DatapointRequestModule), //This is acircular dependency
    DocumentModule,
    DatapointDocumentChunkModule,
    KnowledgeBaseModule,
  ],
  providers: [DataRequestService, DataRequestGuard],
  exports: [DataRequestService],
  controllers: [DataRequestController],
})
export class DataRequestModule {}
