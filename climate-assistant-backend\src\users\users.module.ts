import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { UserPromptContext } from './entities/user-prompt-context.entity';
import { Workspace } from '../workspace/entities/workspace.entity';
import { UserWorkspace } from './entities/user-workspace.entity';
import { Token } from './entities/token.entity';
import { UsersController } from './users.controller';
import { ChatGptService } from '../llm/chat-gpt.service';
import { PerplexityService } from '../util/perplexity.service';
import { PromptContextGenerationService } from './prompt-context-generation.service';
import { MultiQuestionSearchEngine } from '../util/multi-question-search-engine.service';
import { EmailService } from '../external/email.service';
import { Company } from '../workspace/entities/company.entity';
import { EmailModule } from 'src/external/email.module';
import { WorkspaceService } from 'src/workspace/workspace.service';
import { WorkspaceModule } from 'src/workspace/workspace.module';
import { Role } from './entities/role.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      UserPromptContext,
      Token,
      Company,
      Workspace,
      UserWorkspace,
      Role,
    ]),
    EmailModule,
    WorkspaceModule,
  ],
  providers: [
    UsersService,
    ChatGptService,
    PerplexityService,
    PromptContextGenerationService,
    MultiQuestionSearchEngine,
    EmailService,
    WorkspaceService,
  ],
  exports: [UsersService, TypeOrmModule],
  controllers: [UsersController],
})
export class UsersModule {}
