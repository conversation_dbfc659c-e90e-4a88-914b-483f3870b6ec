import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { DatapointRequestService } from './datapoint-request.service';
import type { GenerateDatapointRequestTextPayload } from './entities/datapoint-request.dto';
import { UseGuards } from '@nestjs/common';
import { DatapointRequestGuard } from './datapoint-request.guard';
import { SystemPermissions } from 'src/constants';
import { datapointGenerationStatus } from './entities/datapoint-generation.entity';
import { DatapointDataRequestSharedService } from 'src/shared/shared-datapoint-datarequest.service';
import { DatapointRequest } from './entities/datapoint-request.entity';
import { User } from 'src/users/entities/user.entity';
import { PermissionGuard } from 'src/auth/guard/permission.guard';
import { Permissions } from 'src/auth/decorators/permissions.decorator';
import { AuthGuard } from 'src/auth/auth.guard';
import { DatapointCitationService } from './datapoint-citation.service';
import { DatapointStatusService } from './datapoint-status.service';
import { DatapointDocumentService } from './datapoint-document.service';
import { MaterialTopicsService } from './material-topics.service';

@ApiTags('Datapoint Request')
@UseGuards(AuthGuard)
@UseGuards(PermissionGuard)
@UseGuards(DatapointRequestGuard)
@Controller('datapoint-request')
export class DatapointRequestController {
  constructor(
    private readonly datapointRequestService: DatapointRequestService,
    private readonly datapointCitationService: DatapointCitationService,
    private readonly datapointDocumentService: DatapointDocumentService,
    private readonly datapointStatusService: DatapointStatusService,
    private readonly materialTopicsService: MaterialTopicsService,
    private readonly datapointDataRequestSharedService: DatapointDataRequestSharedService
  ) {}

  @Get('/:datapointRequestId')
  @ApiOperation({ summary: 'Get a specific datapoint request by ID' })
  @ApiResponse({
    status: 200,
    description: 'Datapoint request retrieved successfully',
  })
  async getDataRequest(
    @Param('datapointRequestId') datapointRequestId: string
  ) {
    return await this.datapointRequestService.findData(datapointRequestId);
  }

  @Get('/:datapointRequestId/material-topics')
  @ApiOperation({
    summary: 'Get material topics specific datapoint request by ID',
  })
  @ApiResponse({
    status: 200,
    description: 'All topics related to datapoint fetched successfully',
  })
  async getMaterialTopics(
    @Param('datapointRequestId') datapointRequestId: string
  ) {
    const datapointRequest =
      await this.datapointRequestService.findData(datapointRequestId);
    return await this.materialTopicsService.loadMaterialTopics(
      datapointRequest
    );
  }

  @Permissions(SystemPermissions.APPROVE_DATAPOINTS)
  @Put('/:datapointRequestId/status')
  @ApiOperation({ summary: 'Update a specific datapoint request by ID' })
  @ApiResponse({
    status: 200,
    description: 'Datapoint request updated successfully',
  })
  async updateDatapointRequestStatus(
    @Param('datapointRequestId') datapointRequestId: string,
    @Body() updateDatapointRequestPayload: Partial<DatapointRequest>,
    @Req() req
  ) {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    const datapointRequest = await this.datapointRequestService.update({
      datapointRequestId,
      updateDatapointRequestPayload,
      userId,
      workspaceId,
      event: 'datapoint_request_status_updated',
    });
    return datapointRequest;
  }

  @Permissions(SystemPermissions.EDIT_DATAPOINTS)
  @Put('/:datapointRequestId/content')
  @ApiOperation({ summary: 'Update a specific datapoint request by ID' })
  @ApiResponse({
    status: 200,
    description: 'Datapoint request updated successfully',
  })
  async updateDatapointRequestContent(
    @Param('datapointRequestId') datapointRequestId: string,
    @Body() updateDatapointRequestPayload: Partial<DatapointRequest>,
    @Req() req
  ) {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    const datapointRequest = await this.datapointRequestService.update({
      datapointRequestId,
      updateDatapointRequestPayload,
      userId,
      workspaceId,
    });
    return datapointRequest;
  }

  @Permissions(SystemPermissions.GAP_ANALYSIS_DATAPOINTS)
  @Post('/:datapointRequestId/review-with-ai')
  @ApiOperation({ summary: 'Review datapoint request content with AI' })
  @ApiResponse({
    status: 200,
    description: 'Datapoint request content reviewed successfully',
  })
  async reviewContentWithAi(
    @Param('datapointRequestId') datapointRequestId: string,
    @Req() req
  ): Promise<void> {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    const datapointRequest = req.datapointRequest;
    return await this.datapointDataRequestSharedService.addDatapointToReviewQueue(
      {
        datapointRequest,
        userId,
        workspaceId,
      }
    );
  }

  @Permissions(SystemPermissions.GAP_ANALYSIS_DATAPOINTS)
  @Post('/:datapointRequestId/generate-with-ai')
  @ApiOperation({ summary: 'Generate datapoint request content with AI' })
  @ApiResponse({
    status: 200,
    description: 'Datapoint request content generated successfully',
  })
  async generateContentWithAi(
    @Param('datapointRequestId') datapointRequestId: string,
    @Body() additionalData: GenerateDatapointRequestTextPayload,
    @Req() req
  ): Promise<void> {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    const datapointRequest = req.datapointRequest;

    if (additionalData.additionalReportTextGenerationRules) {
      await this.datapointRequestService.update({
        datapointRequestId: datapointRequestId,
        updateDatapointRequestPayload: {
          customUserRemark: additionalData.additionalReportTextGenerationRules,
        },
        userId,
        workspaceId,
        event: 'datapoint_request_custom_user_remark_updated',
      });
    }

    return await this.datapointDataRequestSharedService.addDatapointToGenerationQueue(
      {
        datapointRequest,
        userId,
        workspaceId,
        useExistingReportTextForReference: additionalData.useExistingReportText,
      }
    );
  }

  @Get('/:datapointRequestId/citations')
  @ApiOperation({ summary: 'Get citations specific datapoint request by ID' })
  @ApiResponse({
    status: 200,
  })
  async getDataRequestCitations(
    @Param('datapointRequestId') datapointRequestId: string,
    @Query() { citationId }: { citationId: string },
    @Req() req
  ) {
    return await this.datapointCitationService.loadDatapointCitations({
      citationId,
      datapointRequest: req.datapointRequest,
      datapointGeneration: req.datapointGeneration,
    });
  }

  @Put('/:datapointRequestId/citations')
  @ApiOperation({
    summary: 'Update citations specific datapoint request by ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Datapoint request citations updated successfully',
  })
  async updateDataRequestCitations(
    @Param('datapointRequestId') datapointRequestId: string,
    @Body()
    payload: {
      citationId: string;
      index: number;
    },
    @Req() req
  ) {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    const updatedDatapointRequest =
      await this.datapointCitationService.updateContentAndReplaceCitation({
        datapointRequestId,
        citationId: payload.citationId,
        index: payload.index,
        userId,
        workspaceId,
        datapointRequest: req.datapointRequest,
      });
    return updatedDatapointRequest;
  }

  @Permissions(SystemPermissions.GAP_ANALYSIS_DATAPOINTS_REVIEW)
  @Put('/:datapointRequestId/generation-status')
  @ApiOperation({
    summary: 'Update generation status specific datapoint request by ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Datapoint request generation status updated successfully',
  })
  async updateDatapointGenerationStatus(
    @Req() req,
    @Body()
    payload: {
      datapointGenerationId: string;
      status: datapointGenerationStatus;
      evaluatorComment?: string;
    }
  ): Promise<{
    content?: string;
    status: datapointGenerationStatus;
    evaluator?: User;
    evaluatedAt?: Date;
    evaluatorComment?: string;
  }> {
    return await this.datapointStatusService.updateGenerationStatus({
      datapointGenerationId: payload.datapointGenerationId,
      status: payload.status,
      userId: req.user.id,
      workspaceId: req.user.workspaceId,
      evaluatorComment: payload.evaluatorComment,
    });
  }

  @Permissions(SystemPermissions.GAP_ANALYSIS_DATAPOINTS_REVIEW)
  @Post('/:datapointRequestId/assess-score')
  @ApiOperation({
    summary:
      'Assess score for datapoint generation using external evaluation API',
  })
  @ApiResponse({
    status: 200,
    description: 'Datapoint generation score assessed successfully',
  })
  async assessDatapointScore(
    @Body()
    payload: {
      datapointGenerationId: string;
      textContent: string;
    }
  ) {
    return await this.datapointStatusService.assessScore({
      datapointGenerationId: payload.datapointGenerationId,
      textContent: payload.textContent,
    });
  }

  @Get('/:datapointRequestId/document-links')
  @ApiOperation({
    summary: 'Get document links specific datapoint request by ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Datapoint request document links fetched successfully',
  })
  async getDocumentLinksForDatapointRequest(
    @Param('datapointRequestId') datapointRequestId: string
  ) {
    return await this.datapointDocumentService.loadDocumentLinks(
      datapointRequestId
    );
  }
}
