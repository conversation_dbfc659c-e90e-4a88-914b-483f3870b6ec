import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration to update LLM prompts with required variables based on feature and chain identifier
 * This migration updates the `requiredVariables` field in the `llm_prompts` table
 * for various features and chain identifiers to ensure they are aligned with the latest requirements.
 * The updates include modifying variables such that redundant or outdated variables are removed,
 * and new variables are added as necessary to support the latest data generation processes.
 */

export class SchemaUpdate1752235309000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `-- UPDATE queries for LLM prompts required variables based on feature and chain identifier

-- DP_DEFAULT_GENERATION A1
UPDATE llm_prompts
SET "requiredVariables" = '{"currentYear": "string", "reportingYear": "string", "existingContent": "string", "materialTopics": "array", "customUserRemark": "string", "nonMaterialTopics": "array", "relatedDatapoints": "array", "generationLanguage": "string", "mainMaterialTopics": "array", "linkedChunksContext": "string", "generalCompanyProfile": "object", "useExistingReportText": "boolean", "reportTextGenerationRules": "string"}'
WHERE feature = 'DP_DEFAULT_GENERATION' AND "chainIdentifier" = 'A1';

-- DP_DEFAULT_GENERATION B1
UPDATE llm_prompts
SET "requiredVariables" = '{"reportingYear": "string", "existingContent": "string", "customUserRemark": "string", "generationLanguage": "string", "generalCompanyProfile": "object", "reportTextGenerationRules": "string", "predatapointGenerationChatCompletionResponse": "string"}'
WHERE feature = 'DP_DEFAULT_GENERATION' AND "chainIdentifier" = 'B1';

-- DP_MDRA_GENERATION A1
UPDATE llm_prompts
SET "requiredVariables" = '{"currentYear": "string", "reportingYear": "string", "materialTopics": "array", "existingContent": "string", "customUserRemark": "string", "nonMaterialTopics": "array", "relatedDatapoints": "array", "generationLanguage": "string", "mainMaterialTopics": "array", "linkedChunksContext": "string", "generalCompanyProfile": "object", "useExistingReportText": "boolean", "reportTextGenerationRules": "string"}'
WHERE feature = 'DP_MDRA_GENERATION' AND "chainIdentifier" = 'A1';

-- DP_MDRA_GENERATION B1
UPDATE llm_prompts
SET "requiredVariables" = '{"reportingYear": "string", "customUserRemark": "string", "generationLanguage": "string", "generalCompanyProfile": "object", "reportTextGenerationRules": "string", "predatapointGenerationChatCompletionResponse": "string"}'
WHERE feature = 'DP_MDRA_GENERATION' AND "chainIdentifier" = 'B1';

-- DP_MDRP_GENERATION A1
UPDATE llm_prompts
SET "requiredVariables" = '{"currentYear": "string", "reportingYear": "string", "materialTopics": "array", "existingContent": "string", "customUserRemark": "string", "nonMaterialTopics": "array", "relatedDatapoints": "array", "generationLanguage": "string", "mainMaterialTopics": "array", "linkedChunksContext": "string", "generalCompanyProfile": "object", "useExistingReportText": "boolean", "reportTextGenerationRules": "string"}'
WHERE feature = 'DP_MDRP_GENERATION' AND "chainIdentifier" = 'A1';

-- DP_MDRP_GENERATION B1
UPDATE llm_prompts
SET "requiredVariables" = '{"reportingYear": "string", "customUserRemark": "string", "generationLanguage": "string", "generalCompanyProfile": "object", "reportTextGenerationRules": "string", "predatapointGenerationChatCompletionResponse": "string"}'
WHERE feature = 'DP_MDRP_GENERATION' AND "chainIdentifier" = 'B1';

-- DP_MDRT_GENERATION A1
UPDATE llm_prompts
SET "requiredVariables" = '{"currentYear": "string", "reportingYear": "string", "materialTopics": "array", "existingContent": "string", "customUserRemark": "string", "nonMaterialTopics": "array", "relatedDatapoints": "array", "generationLanguage": "string", "mainMaterialTopics": "array", "linkedChunksContext": "string", "generalCompanyProfile": "object", "useExistingReportText": "boolean", "reportTextGenerationRules": "string"}'
WHERE feature = 'DP_MDRT_GENERATION' AND "chainIdentifier" = 'A1';

-- DP_MDRT_GENERATION B1
UPDATE llm_prompts
SET "requiredVariables" = '{"reportingYear": "string", "customUserRemark": "string", "generationLanguage": "string", "generalCompanyProfile": "object", "reportTextGenerationRules": "string", "predatapointGenerationChatCompletionResponse": "string"}'
WHERE feature = 'DP_MDRT_GENERATION' AND "chainIdentifier" = 'B1';

-- DP_NUMERIC_GENERATION A1
UPDATE llm_prompts
SET "requiredVariables" = '{"currentYear": "string", "reportingYear": "string", "materialTopics": "array", "existingContent": "string", "customUserRemark": "string", "nonMaterialTopics": "array", "relatedDatapoints": "array", "generationLanguage": "string", "mainMaterialTopics": "array", "linkedChunksContext": "string", "generalCompanyProfile": "object", "useExistingReportText": "boolean", "reportTextGenerationRules": "string"}'
WHERE feature = 'DP_NUMERIC_GENERATION' AND "chainIdentifier" = 'A1';

-- DP_NUMERIC_GENERATION B1
UPDATE llm_prompts
SET "requiredVariables" = '{"reportingYear": "string", "customUserRemark": "string", "generationLanguage": "string", "generalCompanyProfile": "object", "reportTextGenerationRules": "string", "predatapointGenerationChatCompletionResponse": "string"}'
WHERE feature = 'DP_NUMERIC_GENERATION' AND "chainIdentifier" = 'B1';

-- DP_TABLE_GENERATION A1
UPDATE llm_prompts
SET "requiredVariables" = '{"currentYear": "string", "reportingYear": "string", "materialTopics": "array", "existingContent": "string", "customUserRemark": "string", "nonMaterialTopics": "array", "relatedDatapoints": "array", "generationLanguage": "string", "mainMaterialTopics": "array", "linkedChunksContext": "string", "generalCompanyProfile": "object", "useExistingReportText": "boolean", "reportTextGenerationRules": "string"}'
WHERE feature = 'DP_TABLE_GENERATION' AND "chainIdentifier" = 'A1';

-- DP_TABLE_GENERATION B1
UPDATE llm_prompts
SET "requiredVariables" = '{"reportingYear": "string", "customUserRemark": "string", "generationLanguage": "string", "generalCompanyProfile": "object", "reportTextGenerationRules": "string", "predatapointGenerationChatCompletionResponse": "string"}'
WHERE feature = 'DP_TABLE_GENERATION' AND "chainIdentifier" = 'B1';`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
