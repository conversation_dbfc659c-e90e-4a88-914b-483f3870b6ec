import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1738317166874 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 1) Create new Disclosure Requirement
    await queryRunner.query(`INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name)
        VALUES (97, 97, 'G1.MDR', 'G1', 'Policies, Actions and Targets related to business conduct');
        `);

    // 2) Link it to the topic
    await queryRunner.query(`INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId")
        VALUES (10, 97);`);

    // 3) Update affected datapoints to reference the new DR
    await queryRunner.query(`UPDATE esrs_datapoint
        SET "esrsDisclosureRequirementId" = 97
        WHERE "datapointId" IN ('G1.MDR-P_01-06','G1.MDR-P_07-09','G1.MDR-A_01-12','G1.MDR-A_13-15');`);

    // 4) Create a new DataRequest for each project if it doesn't already exist
    await queryRunner.query(`INSERT INTO data_request("dataRequestTypeId","dataRequestType","status","content","projectId")
        SELECT DISTINCT 97 as "dataRequestTypeId", 'ESRS' as "dataRequestType", 'no_data'::data_request_status_enum as "status", '' as "content", dr."projectId"
        FROM data_request dr
        WHERE NOT EXISTS (
            SELECT 1 FROM data_request x
            WHERE x."projectId" = dr."projectId"
            AND x."dataRequestTypeId" = 97
        );`);

    // 5) Update the DatapointRequests to use the newly created DataRequest
    await queryRunner.query(`UPDATE datapoint_request dpr
        SET "dataRequestId" = drNew.id
        FROM data_request drOld
        JOIN data_request drNew ON drNew."projectId" = drOld."projectId" AND drNew."dataRequestTypeId" = 97
        WHERE dpr."dataRequestId" = drOld.id
        AND dpr."esrsDatapointId" IN (
            SELECT ed.id
            FROM esrs_datapoint ed
            WHERE ed."esrsDisclosureRequirementId" = 97
        );`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
