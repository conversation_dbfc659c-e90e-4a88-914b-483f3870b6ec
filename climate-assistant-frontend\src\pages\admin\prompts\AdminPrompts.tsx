import React, { useState, useRef, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Save, Play, History } from 'lucide-react';
import { PromptEditor } from './PromptEditor';
import { TestResults } from './TestResults';
import {
  SavePromptConfirmModal,
  SavePromptFormData,
} from './SavePromptConfirmModal';
import { PromptHistoryModal } from './PromptHistoryModal';
import { DatapointSelector } from '@/components/DatapointSelector';
import {
  fetchAllPrompts,
  fetchPromptsByFeature,
  updatePrompt,
  testDPGenerationPrompt,
  testDPGapAnalysisPrompt,
  fetchDatapointsForTesting,
  fetchAvailableModels,
  type Prompt,
  type DPGenerationPromptResult,
  LLM_MODELS,
} from '@/api/admin/admin.api';
import { toast } from '@/components/ui/use-toast';
import { MainLayout } from '@/components/MainLayout';
import { WorkspaceSwitcher } from '@/components/WorkspaceSwitcher';
import { useAuthentication } from '@/api/authentication/authentication.query';
import { getMissingVariables } from '@/lib/prompt-utils';

export const AdminPrompts: React.FC = () => {
  const queryClient = useQueryClient();
  const testButtonRef = useRef<HTMLDivElement>(null);

  const [state, setState] = useState({
    selectedFeature: '',
    editedPrompts: {} as Record<string, string>,
    editedModels: {} as Record<string, LLM_MODELS>,
    missingVariablesByPrompt: {} as Record<string, string[]>,
    selectedDatapoint: '',
    testResult: null as DPGenerationPromptResult | null,
    isTestLoading: false,
    saveModalPrompt: null as { id: string; name: string } | null,
    historyModalPrompt: null as { id: string; name: string } | null,
  });

  const { user: loggedInUser } = useAuthentication();
  const currentWorkspaceId = loggedInUser?.userWorkspaces[0]?.workspaceId || '';

  // Clear state on feature change
  useEffect(() => {
    setState((prev) => ({
      ...prev,
      editedPrompts: {},
      editedModels: {},
      missingVariablesByPrompt: {},
      testResult: null,
    }));
  }, [state.selectedFeature]);

  // Queries
  const { data: allPrompts = [] } = useQuery({
    queryKey: ['all-prompts'],
    queryFn: fetchAllPrompts,
  });

  const { data: availableModels = [] } = useQuery({
    queryKey: ['available-models'],
    queryFn: fetchAvailableModels,
  });

  const features = [...new Set(allPrompts.map((p: any) => p.feature))].sort();

  const { data: prompts = [], isLoading: promptsLoading } = useQuery({
    queryKey: ['prompts', state.selectedFeature],
    queryFn: () => fetchPromptsByFeature(state.selectedFeature),
    enabled: !!state.selectedFeature,
  });

  const { data: datapoints = [] } = useQuery({
    queryKey: ['datapoints-for-testing'],
    queryFn: fetchDatapointsForTesting,
  });

  // Update missing variables
  useEffect(() => {
    if (prompts.length > 0) {
      const missingVars = prompts.reduce(
        (acc: Record<string, string[]>, p: Prompt) => {
          if (p.requiredVariables) {
            acc[p.id] = getMissingVariables(
              state.editedPrompts[p.id] || p.prompt,
              p.requiredVariables
            );
          }
          return acc;
        },
        {}
      );
      setState((prev) => ({ ...prev, missingVariablesByPrompt: missingVars }));
    }
  }, [prompts, state.editedPrompts]);

  const updatePromptMutation = useMutation({
    mutationFn: ({ id, prompt, model, comment }: any) =>
      updatePrompt(id, { prompt, model, comment }),
    onSuccess: (updatedPrompt, variables) => {
      queryClient.invalidateQueries({
        queryKey: ['prompts', state.selectedFeature],
      });
      queryClient.invalidateQueries({
        queryKey: ['prompt-history', updatedPrompt.id],
      });
      toast({
        variant: 'success',
        title: 'Success',
        description: 'Prompt updated successfully',
      });

      setState((prev) => ({
        ...prev,
        editedPrompts: Object.fromEntries(
          Object.entries(prev.editedPrompts).filter(
            ([k]) => k !== variables.id && k !== updatedPrompt.id
          )
        ),
        editedModels: Object.fromEntries(
          Object.entries(prev.editedModels).filter(
            ([k]) => k !== variables.id && k !== updatedPrompt.id
          )
        ),
        historyModalPrompt:
          prev.historyModalPrompt?.id === variables.id
            ? {
                id: updatedPrompt.id,
                name: prev.historyModalPrompt?.name || 'No description',
              }
            : prev.historyModalPrompt,
      }));
    },
    onError: (error: any) => {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.response?.data?.message || 'Failed to update prompt',
      });
    },
  });

  const testPromptFunction = async () => {
    if (!state.selectedDatapoint) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Please select a datapoint to test the prompt',
      });
      return;
    }

    setState((prev) => ({ ...prev, isTestLoading: true, testResult: null }));

    testButtonRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
    });

    try {
      const promptsWithChainIdentifier = prompts.reduce(
        (acc: any, p: Prompt) => {
          acc[p.chainIdentifier] = {
            prompt: state.editedPrompts[p.id] || p.prompt,
            chainIdentifier: p.chainIdentifier,
            model: state.editedModels[p.id] || p.model,
          };
          return acc;
        },
        {}
      );

      // Determine if this is a gap analysis feature
      const isGapAnalysisFeature =
        state.selectedFeature.includes('GAP_ANALYSIS');

      if (isGapAnalysisFeature) {
        const result = await testDPGapAnalysisPrompt({
          datapointRequestId: state.selectedDatapoint,
          prompts: promptsWithChainIdentifier,
          feature: state.selectedFeature,
        });

        setState((prev) => ({
          ...prev,
          testResult: {
            ...result,
            content: result.text || JSON.stringify(result.gaps || result.gap), // TODO: This is not a clean way
          },
        }));
      } else {
        const result = await testDPGenerationPrompt({
          datapointRequestId: state.selectedDatapoint,
          prompts: promptsWithChainIdentifier,
          feature: state.selectedFeature,
        });
        setState((prev) => ({ ...prev, testResult: result }));
      }
    } catch (error: any) {
      setState((prev) => ({
        ...prev,
        testResult: {
          content: '',
          error: error.response?.data?.message || 'Test failed',
        },
      }));
    } finally {
      setState((prev) => ({ ...prev, isTestLoading: false }));
    }
  };

  const hasIndividualChange = (promptId: string) => {
    const original = prompts.find((p: Prompt) => p.id === promptId);
    return (
      original &&
      ((state.editedPrompts[promptId] &&
        state.editedPrompts[promptId] !== original.prompt) ||
        (state.editedModels[promptId] &&
          state.editedModels[promptId] !== original.model))
    );
  };

  const handleConfirmSave = async (data: SavePromptFormData) => {
    if (state.saveModalPrompt) {
      await updatePromptMutation.mutateAsync({
        id: state.saveModalPrompt.id,
        prompt: state.editedPrompts[state.saveModalPrompt.id],
        model: state.editedModels[state.saveModalPrompt.id],
        comment: data.comment,
      });
    }
    setState((prev) => ({ ...prev, saveModalPrompt: null }));
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto p-6">
        <h1 className="text-3xl font-bold mb-6">
          Datapoint Processing Prompts
        </h1>

        <div className="mb-6 space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Select prompt to work on
            </label>
            <Select
              value={state.selectedFeature}
              onValueChange={(v) =>
                setState((prev) => ({ ...prev, selectedFeature: v }))
              }
            >
              <SelectTrigger className="w-full md:w-96">
                <SelectValue placeholder="Select a prompt" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel>Datapoint Generation Prompts</SelectLabel>
                  {features
                    .filter(
                      (feature) => !String(feature).includes('GAP_ANALYSIS')
                    )
                    .map((feature) => (
                      <SelectItem key={String(feature)} value={String(feature)}>
                        {String(feature).replace(/_/g, ' ')}
                      </SelectItem>
                    ))}
                </SelectGroup>
                <SelectGroup>
                  <SelectLabel>Gap Analysis Prompts</SelectLabel>
                  {features
                    .filter((feature) =>
                      String(feature).includes('GAP_ANALYSIS')
                    )
                    .map((feature) => (
                      <SelectItem key={String(feature)} value={String(feature)}>
                        {String(feature).replace(/_/g, ' ')}
                      </SelectItem>
                    ))}
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          {state.selectedFeature && (
            <div>
              <label className="block text-sm font-medium mb-2">
                Select Datapoint for testing
              </label>
              <DatapointSelector
                datapoints={datapoints}
                selectedDatapoint={state.selectedDatapoint}
                onSelect={(datapointId) =>
                  setState((prev) => ({
                    ...prev,
                    selectedDatapoint: datapointId,
                  }))
                }
                placeholder="Select a datapoint to test"
              />
            </div>
          )}
        </div>

        <div className="flex flex-col mb-10">
          <label className="block text-sm font-medium mb-2">
            Select Workspace to test
          </label>
          <div className="flex items-center justify-between gap-2 mb-4">
            <WorkspaceSwitcher currentWorkspaceId={currentWorkspaceId} />
            {prompts.length > 0 && state.selectedDatapoint && (
              <Button
                onClick={testPromptFunction}
                variant="secondary"
                disabled={state.isTestLoading}
              >
                {state.isTestLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Play className="mr-2 h-4 w-4" />
                )}
                {state.selectedFeature.includes('GAP_ANALYSIS')
                  ? 'Test Gap Analysis'
                  : 'Test Generation'}
              </Button>
            )}
          </div>
        </div>

        {promptsLoading && (
          <div className="flex justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        )}

        {prompts.length > 0 && (
          <div className="space-y-6">
            {prompts.map((prompt: Prompt) => (
              <Card key={prompt.id}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>
                      {prompt.chainIdentifier} -{' '}
                      {prompt.description || 'No description'}
                    </span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">
                        Model:
                      </span>
                      <Select
                        value={state.editedModels[prompt.id] || prompt.model}
                        onValueChange={(v) =>
                          setState((prev) => ({
                            ...prev,
                            editedModels: {
                              ...prev.editedModels,
                              [prompt.id]: v as LLM_MODELS,
                            },
                          }))
                        }
                      >
                        <SelectTrigger className="w-[140px] h-8">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {availableModels.map((model) => (
                            <SelectItem key={model} value={model}>
                              {model}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Button
                        onClick={() =>
                          setState((prev) => ({
                            ...prev,
                            historyModalPrompt: {
                              id: prompt.id,
                              name: `${prompt.chainIdentifier} - ${prompt.description || 'No description'}`,
                            },
                          }))
                        }
                        variant="ghost"
                        size="sm"
                        title="View version history"
                      >
                        <History className="h-4 w-4" />
                      </Button>
                      <Button
                        onClick={() => {
                          const confirmed = window.confirm(
                            `Are you sure you want to deploy the changes to "${prompt.chainIdentifier} - ${prompt.description}"?` +
                              'This will update the prompt in the system. If you are just testing, you dont need to deploy it - just directly click "Test Generation" or "Test Gap Analysis".'
                          );
                          if (confirmed) {
                            setState((prev) => ({
                              ...prev,
                              saveModalPrompt: {
                                id: prompt.id,
                                name: `${prompt.chainIdentifier} - ${prompt.description || 'No description'}`,
                              },
                            }));
                          }
                        }}
                        disabled={
                          !hasIndividualChange(prompt.id) ||
                          updatePromptMutation.isPending
                        }
                        variant="outline"
                        size="sm"
                      >
                        <Save className="h-4 w-4 mr-1" /> Deploy
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {prompt.requiredVariables &&
                    Object.keys(prompt.requiredVariables).length > 0 && (
                      <Alert
                        className={`mb-4 ${
                          state.missingVariablesByPrompt[prompt.id]?.length > 0
                            ? 'border-red-200 bg-red-50'
                            : 'border-green-200 bg-green-50'
                        }`}
                      >
                        <AlertDescription>
                          <div className="flex flex-col gap-2">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">
                                Required variables:
                              </span>
                              {state.missingVariablesByPrompt[prompt.id]
                                ?.length > 0 && (
                                <span className="text-red-600 text-sm font-medium">
                                  (
                                  {
                                    state.missingVariablesByPrompt[prompt.id]
                                      .length
                                  }{' '}
                                  missing)
                                </span>
                              )}
                            </div>
                            <div className="flex flex-wrap gap-2">
                              {Object.keys(prompt.requiredVariables).map(
                                (v) => {
                                  const isMissing =
                                    state.missingVariablesByPrompt[
                                      prompt.id
                                    ]?.includes(v);
                                  return (
                                    <span
                                      key={v}
                                      className={`inline-block px-2 py-1 rounded text-xs font-mono ${
                                        isMissing
                                          ? 'bg-red-100 text-red-800 border border-red-300'
                                          : 'bg-green-100 text-green-800 border border-green-300'
                                      }`}
                                      title={
                                        isMissing
                                          ? 'Missing from prompt'
                                          : 'Found in prompt'
                                      }
                                    >
                                      {`{{${v}}}`}
                                    </span>
                                  );
                                }
                              )}
                            </div>
                          </div>
                        </AlertDescription>
                      </Alert>
                    )}
                  <PromptEditor
                    promptId={prompt.id}
                    initialContent={prompt.prompt}
                    requiredVariables={prompt.requiredVariables}
                    onChange={(content) =>
                      setState((prev) => ({
                        ...prev,
                        editedPrompts: {
                          ...prev.editedPrompts,
                          [prompt.id]: content,
                        },
                      }))
                    }
                  />
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {prompts.length > 0 && state.selectedDatapoint && (
          <div ref={testButtonRef} className="mt-6 flex gap-4">
            <Button
              onClick={testPromptFunction}
              variant="secondary"
              disabled={state.isTestLoading}
            >
              {state.isTestLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Play className="mr-2 h-4 w-4" />
              )}
              {state.selectedFeature.includes('GAP_ANALYSIS')
                ? 'Test Gap Analysis'
                : 'Test Generation'}
            </Button>
          </div>
        )}

        {state.testResult && (
          <div className="mt-8">
            <TestResults result={state.testResult} />
          </div>
        )}

        <SavePromptConfirmModal
          open={!!state.saveModalPrompt}
          setOpen={(open) =>
            !open && setState((prev) => ({ ...prev, saveModalPrompt: null }))
          }
          onConfirm={handleConfirmSave}
          promptName={state.saveModalPrompt?.name}
        />

        {state.historyModalPrompt && (
          <PromptHistoryModal
            open={!!state.historyModalPrompt}
            onOpenChange={(open) =>
              !open &&
              setState((prev) => ({ ...prev, historyModalPrompt: null }))
            }
            promptId={state.historyModalPrompt.id}
            promptName={state.historyModalPrompt.name}
          />
        )}
      </div>
    </MainLayout>
  );
};
