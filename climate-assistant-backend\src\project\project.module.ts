import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProjectService } from './project.service';
import { Project } from './entities/project.entity';
import { ProjectController } from './project.controller';
import { Workspace } from '../workspace/entities/workspace.entity';
import { Comment } from './entities/comment.entity';
import { ProjectGuard } from './project.guard';
import { DataRequest } from 'src/data-request/entities/data-request.entity';
import { KnowledgeBaseModule } from 'src/knowledge-base/knowledge-base.module';
import { User } from 'src/users/entities/user.entity';
import { WorkspaceModule } from 'src/workspace/workspace.module';
import { LlmModule } from 'src/llm/llm.module';
import { PromptModule } from 'src/prompts/prompts.module';
import { MaterialESRSTopic } from './entities/material-esrs-topic.entity';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { ESRSTopicDatapoint } from 'src/knowledge-base/entities/esrs-topic-datapoint.entity';
import { ProjectDatapointRequestService } from './project-dp.service';
import { CommentGeneration } from './entities/comment-generation.entity';
import { LlmRateLimiterModule } from 'src/llm-rate-limiter/llm-rate-limiter.module';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Project,
      Comment,
      CommentGeneration,
      Workspace,
      DataRequest,
      User,
      MaterialESRSTopic,
      DatapointRequest,
      ESRSTopicDatapoint,
    ]),
    KnowledgeBaseModule,
    LlmModule,
    LlmRateLimiterModule,
    PromptModule,
    forwardRef(() => WorkspaceModule),
  ],
  providers: [ProjectService, ProjectDatapointRequestService, ProjectGuard],
  exports: [ProjectService, ProjectDatapointRequestService],
  controllers: [ProjectController],
})
export class ProjectModule {}
