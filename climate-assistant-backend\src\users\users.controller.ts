import {
  Body,
  Controller,
  Get,
  Post,
  Request,
  UseGuards,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { ApiTags } from '@nestjs/swagger';
import { AuthGuard } from 'src/auth/auth.guard';
import { SystemPermissions } from 'src/constants';
import { PermissionGuard } from 'src/auth/guard/permission.guard';
import { Permissions } from 'src/auth/decorators/permissions.decorator';

@ApiTags('users')
@UseGuards(AuthGuard)
@UseGuards(PermissionGuard)
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('/prompt-context')
  async getUserPromptSettings(@Request() req) {
    const userId = req.user.id;

    const context = await this.usersService.getUserPromptContext(userId);

    return {
      context,
    };
  }

  @Get('/generated-prompt-context')
  async getGeneratedPromptContext(@Request() req) {
    const userId = req.user.id;
    const context = await this.usersService.getGeneratedPromptContext(userId);

    return {
      context,
    };
  }

  @Post('/user-prompt-settings')
  async savePromptSettings(@Request() req, @Body() body: { context: string }) {
    const userId = req.user.id;
    const { context } = body;

    await this.usersService.saveUserPromptContext(userId, context);
    return { success: true };
  }

  @Permissions(SystemPermissions.CREATE_WORKSPACE)
  @Post('/create-workspace')
  async createUserCompanyWorkspace(
    @Body() body: { email: string; password: string; name: string }
  ) {
    const { email, password, name } = body;

    const create = await this.usersService.createUserWithCompanyAndWorkspace({
      email,
      password,
      companyName: name,
    });
    return create;
  }
}
