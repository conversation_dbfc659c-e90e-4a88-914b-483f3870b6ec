import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1720764073594 implements MigrationInterface {
  name = 'SchemaUpdate1720764073594';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TABLE "user"
                                 (
                                     "id"       uuid                   NOT NULL DEFAULT uuid_generate_v4(),
                                     "email"    character varying(100) NOT NULL,
                                     "password" character varying(100) NOT NULL,
                                     CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id")
                                 )`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "user"`);
  }
}
