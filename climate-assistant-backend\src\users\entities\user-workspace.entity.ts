import {
  <PERSON><PERSON><PERSON>,
  Colum<PERSON>,
  ManyTo<PERSON>ne,
  PrimaryColumn,
  JoinColumn,
  CreateDateColumn,
} from 'typeorm';
import { User } from './user.entity';
import { Workspace } from '../../workspace/entities/workspace.entity';
import { Role } from './role.entity';

@Entity()
export class UserWorkspace {
  @PrimaryColumn('uuid')
  userId: string;

  @PrimaryColumn('uuid')
  workspaceId: string;

  @Column('uuid')
  roleId: string;

  @ManyToOne(() => Role, (role) => role.userWorkspaces)
  @JoinColumn({ name: 'roleId' })
  role: Role | null;

  @Column({ type: 'timestamp', nullable: true })
  joinedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => User, (user) => user.userWorkspaces)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Workspace, (workspace) => workspace.userWorkspaces)
  @JoinColumn({ name: 'workspaceId' })
  workspace: Workspace;
}
