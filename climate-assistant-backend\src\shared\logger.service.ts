import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class Worker<PERSON>ogger extends Logger {
  private workerId: string;

  constructor(context?: string) {
    super(context);
    this.workerId = this.getWorkerId();
  }

  private getWorkerId(): string {
    const instanceId = process.env.INSTANCE_ID;
    const pid = process.pid;

    if (instanceId !== undefined) {
      return `worker-${instanceId}`;
    }

    return `pid-${pid}`;
  }

  private formatMessage(message: any): string {
    const prefix = `[${this.workerId}]`;
    return typeof message === 'string'
      ? `${prefix} ${message}`
      : `${prefix} ${JSON.stringify(message)}`;
  }

  log(message: any, context?: string): void {
    if (context && context.trim() !== '') {
      super.log(this.formatMessage(message), context);
    } else {
      super.log(this.formatMessage(message));
    }
  }

  error(message: any, trace?: string, context?: string): void {
    if (context && context.trim() !== '') {
      super.error(this.formatMessage(message), trace, context);
    } else {
      super.error(this.formatMessage(message), trace);
    }
  }

  warn(message: any, context?: string): void {
    if (context && context.trim() !== '') {
      super.warn(this.formatMessage(message), context);
    } else {
      super.warn(this.formatMessage(message));
    }
  }

  debug(message: any, context?: string): void {
    if (context && context.trim() !== '') {
      super.debug(this.formatMessage(message), context);
    } else {
      super.debug(this.formatMessage(message));
    }
  }

  verbose(message: any, context?: string): void {
    if (context && context.trim() !== '') {
      super.verbose(this.formatMessage(message), context);
    } else {
      super.verbose(this.formatMessage(message));
    }
  }
}
