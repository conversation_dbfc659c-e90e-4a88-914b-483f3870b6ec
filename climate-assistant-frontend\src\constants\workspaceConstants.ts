export enum USER_ROLE {
  SuperAdmin = 'SUPER_ADMIN',
  WorkspaceAdmin = 'WORKSPACE_ADMIN',
  AiContributor = 'AI_CONTRIBUTOR',
  AiReviewer = 'AI_ONLY_REVIEW',
  Contributor = 'CONTRIBUTOR',
}

export function isCurrentUserAllowedToInviteUser(role: USER_ROLE | undefined) {
  if (
    role &&
    [
      USER_ROLE.SuperAdmin,
      USER_ROLE.WorkspaceAdmin,
      USER_ROLE.AiContributor,
    ].includes(role)
  ) {
    return true;
  }
  return false;
}

export function getRolesAllowedToImport() {
  //Return all user roles other than super admin
  return Object.values(USER_ROLE).filter(
    (r) => r !== USER_ROLE.SuperAdmin
  ) as USER_ROLE[];
}

export const WORKSPACE_ROLES_MAPPING_FOR_UI: {
  [key in USER_ROLE]: string;
} = {
  SUPER_ADMIN: 'Super Admin',
  WOR<PERSON>PACE_ADMIN: 'Workspace Admin',
  AI_CONTRIBUTOR: 'AI Contributor',
  AI_ONLY_REVIEW: 'AI Reviewer',
  CONTRIBUTOR: 'Contributor',
};
