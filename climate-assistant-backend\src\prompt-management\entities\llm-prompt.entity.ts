import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Unique,
} from 'typeorm';
import { LLM_MODELS } from '../../constants';
import { LlmPromptHistory } from './llm-prompt-history.entity';

@Entity('llm_prompts')
@Unique(['feature', 'chainIdentifier'])
export class LlmPrompt {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'varchar',
    length: 100,
  })
  feature: string;

  @Column({
    type: 'varchar',
    length: 10,
  })
  chainIdentifier: string;

  @Column({
    type: 'text',
  })
  prompt: string;

  @Column({
    type: 'enum',
    enum: LLM_MODELS,
  })
  model: LLM_MODELS;

  @Column({
    type: 'jsonb',
    nullable: true,
  })
  requiredVariables: Record<string, string>;

  @Column({
    type: 'varchar',
    length: 255,
  })
  endpoint: string;

  @Column({
    type: 'boolean',
    default: true,
  })
  isActive: boolean;

  @Column({
    type: 'text',
    nullable: true,
  })
  description: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => LlmPromptHistory, (history) => history.prompt)
  history: LlmPromptHistory[];
}
