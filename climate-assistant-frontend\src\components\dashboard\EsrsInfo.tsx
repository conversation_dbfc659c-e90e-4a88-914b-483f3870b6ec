import { useMemo, useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react'; // Make sure to install lucide-react

import { ESRSDatapoint } from '@/types';

const EsrsInfo = ({
  esrsDatapoint,
  source,
}: {
  esrsDatapoint: ESRSDatapoint[];
  source: 'datapoint' | 'datarequest';
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const isDataPoint = useMemo(() => source === 'datapoint', [source]);

  return (
    <div className="border rounded-lg">
      <button
        className="w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 rounded-lg"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <span className="font-semibold text-lg">ESRS Legal Requirement</span>
        {isExpanded ? (
          <ChevronUp className="w-5 h-5" />
        ) : (
          <ChevronDown className="w-5 h-5" />
        )}
      </button>

      {isExpanded &&
        esrsDatapoint &&
        esrsDatapoint.map((esrs) => {
          const {
            datapointId: esrsId,
            lawText,
            lawTextAR,
            paragraph,
            relatedAR,
          } = esrs;
          const hasContent = !!(lawText || paragraph || lawTextAR || relatedAR);
          if (!hasContent) return null;
          return (
            <div key={esrsId} className="p-4 space-y-4">
              <div>
                <span className="font-semibold">
                  {isDataPoint ? 'ESRS' : 'DP'}:
                </span>{' '}
                {isDataPoint ? esrsId.split('_')[0] : esrsId}
              </div>
              {paragraph && (
                <div>
                  <span className="font-semibold">Paragraph:</span> §{paragraph}
                </div>
              )}
              {lawText && (
                <div>
                  <span className="font-semibold">Legal text:</span> {lawText}
                </div>
              )}
              {relatedAR && (
                <div>
                  <span className="font-semibold">AR Paragraph:</span>{' '}
                  {relatedAR}
                </div>
              )}
              {lawTextAR && (
                <div>
                  <span className="font-semibold">
                    Application Requirements:
                  </span>{' '}
                  {lawTextAR}
                </div>
              )}
            </div>
          );
        })}
    </div>
  );
};

export default EsrsInfo;
